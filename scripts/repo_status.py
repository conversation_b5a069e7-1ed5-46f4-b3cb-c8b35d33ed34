#!/usr/bin/env python3
import os
import json
from pathlib import Path

def show_repo_status():
    """Show status of all external repositories."""
    external_dir = Path("docs/external")
    
    if not external_dir.exists():
        print("❌ No external repositories found")
        return
    
    print("📊 External Repositories Status:")
    print("=" * 50)
    
    for repo_dir in external_dir.iterdir():
        if not repo_dir.is_dir():
            continue
            
        metadata_file = repo_dir / ".repo_metadata.json"
        
        print(f"\n📁 {repo_dir.name}")
        print("-" * 30)
        
        if metadata_file.exists():
            with open(metadata_file) as f:
                metadata = json.load(f)
            
            print(f"🔗 URL: {metadata['repo_url']}")
            print(f"🔄 Method: {metadata['sync_method']}")
            print(f"⏰ Last sync: {metadata['last_sync']}")
            print(f"📄 Files: {len(metadata['files'])}")
            
            # Check if files exist
            missing_files = []
            for file_path in metadata['files']:
                local_file = repo_dir / file_path.strip('/')
                if not local_file.exists():
                    missing_files.append(file_path)
            
            if missing_files:
                print(f"⚠️  Missing files: {missing_files}")
            else:
                print("✅ All files present")
                
        else:
            print("❓ No metadata (legacy Git clone?)")
            
            # Check for .git directory
            git_dir = repo_dir / ".git"
            if git_dir.exists():
                print("📦 Git repository detected")
            else:
                print("📄 File-only repository")
        
        # Show size
        size = sum(f.stat().st_size for f in repo_dir.rglob('*') if f.is_file())
        print(f"💾 Size: {size / 1024:.1f} KB")

if __name__ == "__main__":
    show_repo_status()
