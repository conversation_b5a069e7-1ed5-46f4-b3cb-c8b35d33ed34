#!/usr/bin/env python3
import os
import json
from pathlib import Path

def show_repo_status():
    # Shows status of all external repositories
    external_dir = Path("docs/external")

    if not external_dir.exists():
        print("❌ External repositories not found")
        return

    print("📊 External repositories status:")
    print("=" * 50)

    for repo_dir in external_dir.iterdir():
        if not repo_dir.is_dir():
            continue

        metadata_file = repo_dir / ".repo_metadata.json"

        print(f"\n📁 {repo_dir.name}")
        print("-" * 30)

        if metadata_file.exists():
            with open(metadata_file) as f:
                metadata = json.load(f)

            print(f"🔗 URL: {metadata['repo_url']}")
            print(f"🔄 Method: {metadata['sync_method']}")
            print(f"⏰ Last sync: {metadata['last_sync']}")

            # Use actual_files if available, otherwise files
            files_to_check = metadata.get('actual_files', metadata.get('files', []))
            print(f"📄 Files: {len(files_to_check)}")

            # Check if files exist
            missing_files = []
            for file_path in files_to_check:
                local_file = repo_dir / file_path.strip('/')
                if not local_file.exists():
                    missing_files.append(file_path)

            if missing_files:
                print(f"⚠️  Missing files: {missing_files}")
            else:
                print("✅ All files present")

        else:
            print("❓ No metadata (legacy Git clone?)")

            # Check for .git directory
            git_dir = repo_dir / ".git"
            if git_dir.exists():
                print("📦 Git repository detected")
            else:
                print("📄 File-only repository")

        # Show size
        size = sum(f.stat().st_size for f in repo_dir.rglob('*') if f.is_file())
        print(f"💾 Size: {size / 1024:.1f} KB")

        # Check for /docs/ → /documents/ renaming
        if metadata_file.exists():
            with open(metadata_file) as f:
                metadata = json.load(f)

            original_files = metadata.get('original_files', [])
            docs_files = [f for f in original_files if '/docs/' in f]

            if docs_files:
                print(f"📁 Renamed {len(docs_files)} /docs/ files to /documents/ (hosting compatibility)")

if __name__ == "__main__":
    show_repo_status()
