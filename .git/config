[core]
	repositoryformatversion = 0
	filemode = true
	bare = false
	logallrefupdates = true
	ignorecase = true
	precomposeunicode = true
[remote "origin"]
	url = https://github.com/coralogix/documentation.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "sergiy/redesign-fixes"]
	vscode-merge-base = origin/master
	gk-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/redesign-fixes
[branch "sergiy/detail-darkmode-fix"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/detail-darkmode-fix
	gk-merge-base = origin/master
[branch "sergiy/buttons-nav"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/buttons-nav
	gk-merge-base = origin/master
[branch "sergiy/submodules-parse"]
	vscode-merge-base = origin/master
[branch "sergiy/additional-content-titles"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/additional-content-titles
[branch "sergiy/trigger-deploy"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/trigger-deploy
[branch "sergiy/additional-content-title-fix"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/additional-content-title-fix
[branch "sergiy/additional-title-fix"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/additional-title-fix
[branch "sergiy/design-fixes"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/design-fixes
[branch "sergiy/external-repo-parsing"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/external-repo-parsing
[branch "sergiy/ci-fix"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/ci-fix
[branch "sergiy/external-examples-titles"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/external-examples-titles
[branch "sergiy/button-ui-fix"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/button-ui-fix
[branch "sergiy/test-search-exclude"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/test-search-exclude
[branch "sergiy/json-scheme"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/json-scheme
[branch "sergiy/json-scheme-fix"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/json-scheme-fix
[branch "sergiy/popular-docs-changes"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/popular-docs-changes
[branch "master"]
	remote = origin
	merge = refs/heads/master
	vscode-merge-base = origin/master
[branch "sergiy/dmitriy-revert"]
	remote = origin
	merge = refs/heads/sergiy/dmitriy-revert
	vscode-merge-base = origin/master
[branch "sergiy/json-schema-apply"]
	remote = origin
	merge = refs/heads/sergiy/json-schema-apply
	vscode-merge-base = origin/master
[branch "sergiy/global-categories"]
	remote = origin
	merge = refs/heads/sergiy/global-categories
[branch "sergiy/inarray-redirect"]
	remote = origin
	merge = refs/heads/sergiy/inarray-redirect
[branch "continuous-profiling"]
	remote = origin
	merge = refs/heads/continuous-profiling
[branch "sergiy/profiling-img-fix"]
	remote = origin
	merge = refs/heads/sergiy/profiling-img-fix
	vscode-merge-base = origin/master
[branch "sergiy/platform-tooltip"]
	remote = origin
	merge = refs/heads/sergiy/platform-tooltip
[branch "sergiy/toc-depth"]
	remote = origin
	merge = refs/heads/sergiy/toc-depth
[branch "global-categories-test"]
	remote = origin
	merge = refs/heads/global-categories-test
[branch "sergiy/submodules-parsing-alerts"]
	remote = origin
	merge = refs/heads/sergiy/submodules-parsing-alerts
[branch "sergiy/submodules-parsing-spec-tags"]
	remote = origin
	merge = refs/heads/sergiy/submodules-parsing-spec-tags
	vscode-merge-base = origin/master
[branch "sergiy/last-upd-fix"]
	remote = origin
	merge = refs/heads/sergiy/last-upd-fix
[branch "sergiy/img-zoom"]
	remote = origin
	merge = refs/heads/sergiy/img-zoom
[branch "PLTD-1115/sync-operator-docs"]
	remote = origin
	merge = refs/heads/PLTD-1115/sync-operator-docs
	vscode-merge-base = origin/PLTD-1115/sync-operator-docs
[branch "sergiy/docs-path-replace"]
	remote = origin
	merge = refs/heads/sergiy/docs-path-replace
[branch "sergiy/coralogix-operator-meta-fix"]
	remote = origin
	merge = refs/heads/sergiy/coralogix-operator-meta-fix
[branch "ai-spm-upd"]
	remote = origin
	merge = refs/heads/ai-spm-upd
[branch "ai-spm-upd_cleared"]
	remote = origin
	merge = refs/heads/ai-spm-upd_cleared
[branch "test"]
	remote = origin
	merge = refs/heads/test
[branch "ai-instrumentation"]
	remote = origin
	merge = refs/heads/ai-instrumentation
[branch "sergiy/pip-ci-fix"]
	remote = origin
	merge = refs/heads/sergiy/pip-ci-fix
[branch "sergiy/pip-ci-fix2"]
	remote = origin
	merge = refs/heads/sergiy/pip-ci-fix2
[branch "sergiy/ci-changes"]
	remote = origin
	merge = refs/heads/sergiy/ci-changes
[branch "sergiy/ci-debug"]
	remote = origin
	merge = refs/heads/sergiy/ci-debug
[branch "sergiy/deinit-submodules"]
	remote = origin
	merge = refs/heads/sergiy/deinit-submodules
[submodule "docs/external/coralogix-aws-shipper"]
	url = https://github.com/coralogix/coralogix-aws-shipper.git
	active = true
[submodule "docs/external/terraform-coralogix-aws"]
	url = https://github.com/coralogix/terraform-coralogix-aws.git
	active = true
[submodule "docs/external/coralogix-operator"]
	url = https://github.com/coralogix/coralogix-operator.git
	active = true
[branch "sergiy/latex"]
	remote = origin
	merge = refs/heads/sergiy/latex
[branch "sergiy/favicon-fix"]
	remote = origin
	merge = refs/heads/sergiy/favicon-fix
[branch "variables-update"]
	remote = origin
	merge = refs/heads/variables-update
[merge "ours"]
	driver = true
[submodule]
	recurse = false
