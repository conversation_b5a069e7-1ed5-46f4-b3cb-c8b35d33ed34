[core]
	repositoryformatversion = 0
	filemode = true
	bare = false
	logallrefupdates = true
	ignorecase = true
	precomposeunicode = true
[remote "origin"]
	url = https://github.com/coralogix/documentation.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[submodule "docs/external/coralogix-aws-shipper"]
	active = true
	url = https://github.com/coralogix/coralogix-aws-shipper.git
[submodule "docs/external/terraform-coralogix-aws"]
	active = true
	url = https://github.com/coralogix/terraform-coralogix-aws.git
[branch "sergiy/redesign-fixes"]
	vscode-merge-base = origin/master
	gk-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/redesign-fixes
[branch "sergiy/detail-darkmode-fix"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/detail-darkmode-fix
	gk-merge-base = origin/master
[branch "sergiy/buttons-nav"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/buttons-nav
	gk-merge-base = origin/master
[branch "sergiy/submodules-parse"]
	vscode-merge-base = origin/master
[branch "sergiy/additional-content-titles"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/additional-content-titles
[branch "sergiy/trigger-deploy"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/trigger-deploy
[branch "sergiy/additional-content-title-fix"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/additional-content-title-fix
[branch "sergiy/additional-title-fix"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/additional-title-fix
[branch "sergiy/design-fixes"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/design-fixes
[branch "sergiy/external-repo-parsing"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/external-repo-parsing
[branch "sergiy/ci-fix"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/ci-fix
[branch "sergiy/external-examples-titles"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/external-examples-titles
[branch "sergiy/button-ui-fix"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/button-ui-fix
[branch "sergiy/test-search-exclude"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/test-search-exclude
[branch "sergiy/json-scheme"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/json-scheme
[branch "sergiy/json-scheme-fix"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/json-scheme-fix
[branch "sergiy/popular-docs-changes"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/sergiy/popular-docs-changes
[branch "master"]
	remote = origin
	merge = refs/heads/master
	vscode-merge-base = origin/master
[branch "sergiy/dmitriy-revert"]
	remote = origin
	merge = refs/heads/sergiy/dmitriy-revert
	vscode-merge-base = origin/master
[branch "sergiy/json-schema-apply"]
	remote = origin
	merge = refs/heads/sergiy/json-schema-apply
	vscode-merge-base = origin/master
[branch "sergiy/global-categories"]
	remote = origin
	merge = refs/heads/sergiy/global-categories
[branch "sergiy/inarray-redirect"]
	remote = origin
	merge = refs/heads/sergiy/inarray-redirect
[branch "continuous-profiling"]
	remote = origin
	merge = refs/heads/continuous-profiling
[branch "sergiy/profiling-img-fix"]
	remote = origin
	merge = refs/heads/sergiy/profiling-img-fix
	vscode-merge-base = origin/master
[branch "sergiy/platform-tooltip"]
	remote = origin
	merge = refs/heads/sergiy/platform-tooltip
[branch "sergiy/toc-depth"]
	remote = origin
	merge = refs/heads/sergiy/toc-depth
[submodule "docs/external/cx-ios-sdk"]
	url = https://github.com/coralogix/cx-ios-sdk.git
	active = true
[branch "global-categories-test"]
	remote = origin
	merge = refs/heads/global-categories-test
[branch "sergiy/submodules-parsing-alerts"]
	remote = origin
	merge = refs/heads/sergiy/submodules-parsing-alerts
[branch "sergiy/submodules-parsing-spec-tags"]
	remote = origin
	merge = refs/heads/sergiy/submodules-parsing-spec-tags
[branch "sergiy/last-upd-fix"]
	remote = origin
	merge = refs/heads/sergiy/last-upd-fix
[submodule "docs/external/telemetry-shippers"]
	url = https://github.com/coralogix/telemetry-shippers.git
	active = true
