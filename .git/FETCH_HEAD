cb563b9fd4837a9e9da6a3ffe98fc50c18e4b624		branch 'master' of https://github.com/coralogix/terraform-coralogix-aws
941bd08cf42a81de151cc33cd91610819394091d	not-for-merge	branch 'Adding-default-to-custom_url-variable' of https://github.com/coralogix/terraform-coralogix-aws
b319d6a0223952fb3505f769244a81fc83cee7e4	not-for-merge	branch 'CDS-1342' of https://github.com/coralogix/terraform-coralogix-aws
b614587925de958b922280931f9724ea4e417c3a	not-for-merge	branch 'CDS-1833' of https://github.com/coralogix/terraform-coralogix-aws
61deadc3a55693e9e079481263d701c1788f9c85	not-for-merge	branch 'CDS-1861' of https://github.com/coralogix/terraform-coralogix-aws
02ba898a1f49e92dd92fea2bc049b629c41cc806	not-for-merge	branch 'CDS-1895' of https://github.com/coralogix/terraform-coralogix-aws
e69bf7c99ad667d9b1f3e5840dc63d8f75584882	not-for-merge	branch 'FEAT/fix_examples' of https://github.com/coralogix/terraform-coralogix-aws
a3414d2aee49580743260f5fd42830e325a5e356	not-for-merge	branch 'SRE-2283' of https://github.com/coralogix/terraform-coralogix-aws
ec48b75360b2c7de33ca583998c74d2a2d6cbb29	not-for-merge	branch 'add-ap3-region' of https://github.com/coralogix/terraform-coralogix-aws
e98825641f385d7ed5089c1c5c9b65c9d4234178	not-for-merge	branch 'add-dlq-support' of https://github.com/coralogix/terraform-coralogix-aws
c8c0f44c91b0c36b266cf2c2a84bbfd2d4ef79af	not-for-merge	branch 'add-private-link-endpoint' of https://github.com/coralogix/terraform-coralogix-aws
72083cd305a1e58a5e162d282fd20ee50ba55533	not-for-merge	branch 'add-tech-writers' of https://github.com/coralogix/terraform-coralogix-aws
95cfba9aad3a417fad0f61d7cc9284dbc178c49f	not-for-merge	branch 'addOptionalParameters' of https://github.com/coralogix/terraform-coralogix-aws
6a387b50025ea004ce96f4220a31f92fd2b00159	not-for-merge	branch 'amit-o-patch-1' of https://github.com/coralogix/terraform-coralogix-aws
b3ddc0c29247adbee0d88e15cea07c4149baa674	not-for-merge	branch 'as-firehose-policy' of https://github.com/coralogix/terraform-coralogix-aws
961d1379c064a7c50e2e079b4b60f47f3b7a7bc6	not-for-merge	branch 'as-firehose-update-variable-metric_enable' of https://github.com/coralogix/terraform-coralogix-aws
cd46c9ac28ba82fdd172889bdb12e510df141ba3	not-for-merge	branch 'assumrole-shipper-update' of https://github.com/coralogix/terraform-coralogix-aws
cb447a0d6bbfaed683c979aa9edf96aa43c44118	not-for-merge	branch 'bug-fix' of https://github.com/coralogix/terraform-coralogix-aws
7aaaf1fb422ab5a147d3dd60ba019dca6a30c960	not-for-merge	branch 'cds-1050' of https://github.com/coralogix/terraform-coralogix-aws
3d08b27353d0cbe1873fdf7441ea8bedffebb46d	not-for-merge	branch 'cds-1099' of https://github.com/coralogix/terraform-coralogix-aws
54d1487d364f705d3db2c7f4b573be27d97080ec	not-for-merge	branch 'cds-1099-force-flush' of https://github.com/coralogix/terraform-coralogix-aws
004a09d5121d4e5e80c0d139a9b3dc0362fec5bd	not-for-merge	branch 'cds-1241' of https://github.com/coralogix/terraform-coralogix-aws
7d71423c6f71e93ce79fd9e8997f3ac5ae89735b	not-for-merge	branch 'cds-1580' of https://github.com/coralogix/terraform-coralogix-aws
44a022360b274133a916d50afeea7a7fa5226ece	not-for-merge	branch 'changelog-check-test' of https://github.com/coralogix/terraform-coralogix-aws
e23dbe2696950948649a6dcc787154e1d5fce7eb	not-for-merge	branch 'changing-aws-iam-policy-to-variables' of https://github.com/coralogix/terraform-coralogix-aws
7065c16b29b87285aaa8fdd93e87c7b07a3338b0	not-for-merge	branch 'cloudwatch-permission-update' of https://github.com/coralogix/terraform-coralogix-aws
82c229fa5709a85612033ede8525e8473d9a071a	not-for-merge	branch 'custom-secret' of https://github.com/coralogix/terraform-coralogix-aws
00b066262b8bf502c86c241e86a204c29046d4b4	not-for-merge	branch 'dev' of https://github.com/coralogix/terraform-coralogix-aws
32ed7c00859f1f527693ecbfc93186970f571049	not-for-merge	branch 'docs-update-readme' of https://github.com/coralogix/terraform-coralogix-aws
425ab26413bc39882485b9ea6f54ae383bbf4ddb	not-for-merge	branch 'docs-update-readme-1' of https://github.com/coralogix/terraform-coralogix-aws
0a8097c0f2072a53124751e7888c3ceec7fbaa03	not-for-merge	branch 'docs-update-readme-v' of https://github.com/coralogix/terraform-coralogix-aws
4a8634f0b717fe019b3cc4dd7fea6bdade4dd349	not-for-merge	branch 'ecr-integration' of https://github.com/coralogix/terraform-coralogix-aws
470654fa442a48b2a355c1a4628dd31a92750ad0	not-for-merge	branch 'ecs-ec2-config-update' of https://github.com/coralogix/terraform-coralogix-aws
134c84d3eb23b1ddd8693defda3b4165d9b6afe4	not-for-merge	branch 'ecs-ec2-otel-unique-name' of https://github.com/coralogix/terraform-coralogix-aws
4ca76e4eb6888783f1c58f98790511acd1b4e16d	not-for-merge	branch 'ecs-ec2-release' of https://github.com/coralogix/terraform-coralogix-aws
f1c772e86bac085f0b57ca35396605471d3d6746	not-for-merge	branch 'eventbridgeModule' of https://github.com/coralogix/terraform-coralogix-aws
fb925024b1548955a4482720e3b40f8765129dbc	not-for-merge	branch 'feat-resource-metadata-sqs' of https://github.com/coralogix/terraform-coralogix-aws
f212998a9618cfb1f5c55330b929c42822d6e573	not-for-merge	branch 'feature/NGSTN-464-resource-metadata-lambda-filtering' of https://github.com/coralogix/terraform-coralogix-aws
c95f92aa73ba4ff9ea61f0b5b38798ea8f08f98a	not-for-merge	branch 'feature/cds-1390-firehose' of https://github.com/coralogix/terraform-coralogix-aws
acedfbd622afbd33ee1cd30eb7d660f010ee2da9	not-for-merge	branch 'feature/cds-1676' of https://github.com/coralogix/terraform-coralogix-aws
9191dd0ec0499ed95c7c64ebef1ad05c2d4f246b	not-for-merge	branch 'feature/firehose-logs-update' of https://github.com/coralogix/terraform-coralogix-aws
1752822b4478be890f2b6d7ad7c2698e723e2495	not-for-merge	branch 'feature/re-design' of https://github.com/coralogix/terraform-coralogix-aws
04b72615b689440b98803d43bef25557bca9f4b1	not-for-merge	branch 'firehose-outputs' of https://github.com/coralogix/terraform-coralogix-aws
8a7b046eb9302ba2849b4529858f9cf85e74d718	not-for-merge	branch 'firehose-update' of https://github.com/coralogix/terraform-coralogix-aws
b41a0389988573a48113122a6c384ba959cd2178	not-for-merge	branch 'fix-awsprovider-config' of https://github.com/coralogix/terraform-coralogix-aws
ad29a1ef72603f71fbb8af12950ec257e801a9db	not-for-merge	branch 'fix-firehose-metrics-type-default' of https://github.com/coralogix/terraform-coralogix-aws
06bb4f7a79b06ffe70c62b65310ee8c24a681538	not-for-merge	branch 'fix/delivery-stream-log-enable-tag' of https://github.com/coralogix/terraform-coralogix-aws
4c56f5df1e18bb379281a21904ae77afc5c58848	not-for-merge	branch 'fix/firehose-url-update' of https://github.com/coralogix/terraform-coralogix-aws
6bfbaa9c95b72dd4038552820370b5841a771e95	not-for-merge	branch 'fix/logLevel' of https://github.com/coralogix/terraform-coralogix-aws
7f79ca52238db3c83cd275a2e3dd5be57a644313	not-for-merge	branch 'fix/opensource-compliance' of https://github.com/coralogix/terraform-coralogix-aws
9a8f637ddeef06c1e9c3370537d08ffb569c27eb	not-for-merge	branch 'fixUnclosedResource' of https://github.com/coralogix/terraform-coralogix-aws
dc8cfa096edce0696130428754982bd41d292968	not-for-merge	branch 'fix_firehose_inline_policy' of https://github.com/coralogix/terraform-coralogix-aws
1653188be4d55ff28b7e1340de47f67b5bfc98f4	not-for-merge	branch 'fix_kinesis_api_key' of https://github.com/coralogix/terraform-coralogix-aws
c07896ee438ce7c585e15ee67ed9eeca1d732adf	not-for-merge	branch 'govcloud-support' of https://github.com/coralogix/terraform-coralogix-aws
901515cce349563174ec080bee55b2c0044391fa	not-for-merge	branch 'hotfix/docs' of https://github.com/coralogix/terraform-coralogix-aws
54784faf671ef542ab7412d170fa9a31847ae447	not-for-merge	branch 'lambda-secretLayer' of https://github.com/coralogix/terraform-coralogix-aws
7c7e6aac193cb0e50bf489a267a1d098b49f4f3f	not-for-merge	branch 'msk-integ-files-created' of https://github.com/coralogix/terraform-coralogix-aws
0a6f083329049d136ec71ccbcce5427ef2e6109c	not-for-merge	branch 'msk-kafka-integration' of https://github.com/coralogix/terraform-coralogix-aws
8d356fc59151fa30d65e951fdbddff09986e7a65	not-for-merge	branch 'new-csv-variable' of https://github.com/coralogix/terraform-coralogix-aws
688b360bf09a70b099557318f183b5283d9ca891	not-for-merge	branch 'new-rust-module' of https://github.com/coralogix/terraform-coralogix-aws
80faaecf602d15d915b0d8d0d323c7f0d65ebcf5	not-for-merge	branch 'otelcol_ecs_ec2_fix' of https://github.com/coralogix/terraform-coralogix-aws
9cc107fd75408bc75cf1f43a2b4fc7cc7bda9373	not-for-merge	branch 'overrideApplicationName' of https://github.com/coralogix/terraform-coralogix-aws
ab70a9765f2522fb4daadc6e4fed3952e6b9c193	not-for-merge	branch 'readmeSources' of https://github.com/coralogix/terraform-coralogix-aws
39ee28e2572196974b380c00417363fca537a2a6	not-for-merge	branch 'readme_update' of https://github.com/coralogix/terraform-coralogix-aws
164b5740c8816de59e30c2182178f1b86009957c	not-for-merge	branch 'run-e2e-test' of https://github.com/coralogix/terraform-coralogix-aws
cda228b27570fb0364f498b4c298f60dfc56363d	not-for-merge	branch 'rust-module-sqs' of https://github.com/coralogix/terraform-coralogix-aws
c64c96b82a49cb85737c0017801105e1449ea400	not-for-merge	branch 'secret-manager-update' of https://github.com/coralogix/terraform-coralogix-aws
31471bf40adb1076657f3f5b540d2c2bdb49e559	not-for-merge	branch 'split-firehose-logs-metrics' of https://github.com/coralogix/terraform-coralogix-aws
81680fb0f69b8842e0a2693f898a43d3a9c83826	not-for-merge	branch 'update-domain' of https://github.com/coralogix/terraform-coralogix-aws
245af36354e9191e6f9a477f37888ca9a702b24e	not-for-merge	branch 'update-ecs-ec2-metric-collection' of https://github.com/coralogix/terraform-coralogix-aws
bd17df3cbc6d8675d460077c4a9802d661aa93d7	not-for-merge	branch 'update-notication-condition' of https://github.com/coralogix/terraform-coralogix-aws
576d530ac9a6b66e4618b9832f12db76469ff277	not-for-merge	branch 'update-runtime' of https://github.com/coralogix/terraform-coralogix-aws
c5c30163f144c72dde1d6ec0dabc52c642246758	not-for-merge	branch 'update-sns-creation-condition' of https://github.com/coralogix/terraform-coralogix-aws
c8bf6bb07c0ff4848508aaab792f0c621501cbb4	not-for-merge	branch 'updateDefaultSources' of https://github.com/coralogix/terraform-coralogix-aws
5bac8006fe628bb42e9df1ebddcbabedfae991ad	not-for-merge	branch 'v2.0.0' of https://github.com/coralogix/terraform-coralogix-aws
80dab06ed4cafeff6a7f1742fca046b597cbdd84	not-for-merge	branch 'variables-update' of https://github.com/coralogix/terraform-coralogix-aws
689d4de40e5bd46719cee9c50329f7dbf8f7d602	not-for-merge	branch 'ya-added-eventbridge-tf' of https://github.com/coralogix/terraform-coralogix-aws
