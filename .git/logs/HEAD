0000000000000000000000000000000000000000 2a43cc7f2a9c3c436867788624ed552048d5bf1f Ser<PERSON><PERSON> <<EMAIL>> 1740035108 +0200	clone: from https://github.com/coralogix/documentation.git
2a43cc7f2a9c3c436867788624ed552048d5bf1f 2a43cc7f2a9c3c436867788624ed552048d5bf1f Sergiy <PERSON> <<EMAIL>> 1740059108 +0200	reset: moving to HEAD
2a43cc7f2a9c3c436867788624ed552048d5bf1f 2a43cc7f2a9c3c436867788624ed552048d5bf1f Sergiy <PERSON> <<EMAIL>> 1740062820 +0200	checkout: moving from master to sergiy/redesign-fixes
2a43cc7f2a9c3c436867788624ed552048d5bf1f 2a43cc7f2a9c3c436867788624ed552048d5bf1f Sergiy <PERSON> <<EMAIL>> 1740320473 +0200	reset: moving to HEAD
2a43cc7f2a9c3c436867788624ed552048d5bf1f 2a43cc7f2a9c3c436867788624ed552048d5bf1f Sergiy Palamar <<EMAIL>> 1740320513 +0200	checkout: moving from sergiy/redesign-fixes to master
2a43cc7f2a9c3c436867788624ed552048d5bf1f 670d505977af98b664ca810280b0a52c8fb2948a Sergiy Palamar <<EMAIL>> 1740320520 +0200	pull --tags origin master: Fast-forward
670d505977af98b664ca810280b0a52c8fb2948a 670d505977af98b664ca810280b0a52c8fb2948a Sergiy Palamar <<EMAIL>> 1740321341 +0200	checkout: moving from master to sergiy/detail-darkmode-fix
670d505977af98b664ca810280b0a52c8fb2948a 570ca130f0e95c51de688825048db9039ff7b642 Sergiy Palamar <<EMAIL>> 1740321362 +0200	commit: Details block dark mode UI fix
570ca130f0e95c51de688825048db9039ff7b642 2a43cc7f2a9c3c436867788624ed552048d5bf1f Sergiy Palamar <<EMAIL>> 1740386163 +0200	checkout: moving from sergiy/detail-darkmode-fix to sergiy/redesign-fixes
2a43cc7f2a9c3c436867788624ed552048d5bf1f 51568b1ae63bfb8c9062fbf2cc0129add2019d54 Sergiy Palamar <<EMAIL>> 1740388434 +0200	commit: Redesign fixes
51568b1ae63bfb8c9062fbf2cc0129add2019d54 991c90232ad4b74aef07ee8bc50ec7b235bcb870 Sergiy Palamar <<EMAIL>> 1740390587 +0200	commit: Button text fix
991c90232ad4b74aef07ee8bc50ec7b235bcb870 670d505977af98b664ca810280b0a52c8fb2948a Sergiy Palamar <<EMAIL>> 1740398313 +0200	checkout: moving from sergiy/redesign-fixes to master
670d505977af98b664ca810280b0a52c8fb2948a 58a92e1ede16df3b273560777f4bd2f6a46d6dc3 Sergiy Palamar <<EMAIL>> 1740398317 +0200	pull --tags origin master: Fast-forward
58a92e1ede16df3b273560777f4bd2f6a46d6dc3 58a92e1ede16df3b273560777f4bd2f6a46d6dc3 Sergiy Palamar <<EMAIL>> 1740411853 +0200	checkout: moving from master to sergiy/buttons-nav
58a92e1ede16df3b273560777f4bd2f6a46d6dc3 77b5c5dc3aac9d9fbbf1c7bbcec02371b2c11f7f Sergiy Palamar <<EMAIL>> 1740412238 +0200	commit: Buttons navigation implemented
77b5c5dc3aac9d9fbbf1c7bbcec02371b2c11f7f 58a92e1ede16df3b273560777f4bd2f6a46d6dc3 Sergiy Palamar <<EMAIL>> 1741343541 +0200	checkout: moving from sergiy/buttons-nav to master
58a92e1ede16df3b273560777f4bd2f6a46d6dc3 c738cf57b3fed0cf14d85c728e02bd01dc646c9e Sergiy Palamar <<EMAIL>> 1741343546 +0200	pull --tags origin master: Fast-forward
c738cf57b3fed0cf14d85c728e02bd01dc646c9e c738cf57b3fed0cf14d85c728e02bd01dc646c9e Sergiy Palamar <<EMAIL>> 1741343582 +0200	checkout: moving from master to sergiy/submodules-parse
c738cf57b3fed0cf14d85c728e02bd01dc646c9e c738cf57b3fed0cf14d85c728e02bd01dc646c9e Sergiy Palamar <<EMAIL>> 1741593866 +0200	reset: moving to HEAD
c738cf57b3fed0cf14d85c728e02bd01dc646c9e c738cf57b3fed0cf14d85c728e02bd01dc646c9e Sergiy Palamar <<EMAIL>> 1741593871 +0200	checkout: moving from sergiy/submodules-parse to master
c738cf57b3fed0cf14d85c728e02bd01dc646c9e 1c60ed6e61467395ad1e91e72394b34b570b95ef Sergiy Palamar <<EMAIL>> 1741593884 +0200	pull --tags origin master: Fast-forward
1c60ed6e61467395ad1e91e72394b34b570b95ef 1c60ed6e61467395ad1e91e72394b34b570b95ef Sergiy Palamar <<EMAIL>> 1741596425 +0200	checkout: moving from master to sergiy/additional-content-titles
1c60ed6e61467395ad1e91e72394b34b570b95ef 2b8dc15923fdd433226fede2959f83ff42553870 Sergiy Palamar <<EMAIL>> 1741596526 +0200	commit: Additional content title
2b8dc15923fdd433226fede2959f83ff42553870 f2c8a14510941ccd00640c90ff2700a8b10aa8c9 Sergiy Palamar <<EMAIL>> 1741596597 +0200	commit: Comment removed
f2c8a14510941ccd00640c90ff2700a8b10aa8c9 61aced8223ac942ff0b051c65d26fe5f37ff5d67 Sergiy Palamar <<EMAIL>> 1741596708 +0200	commit (amend): Comments removed
61aced8223ac942ff0b051c65d26fe5f37ff5d67 42eccd7dae0f0825a7baa3f794e790241c2a85a3 Sergiy Palamar <<EMAIL>> 1741596764 +0200	commit: Fix
42eccd7dae0f0825a7baa3f794e790241c2a85a3 1c60ed6e61467395ad1e91e72394b34b570b95ef Sergiy Palamar <<EMAIL>> 1741601355 +0200	checkout: moving from sergiy/additional-content-titles to master
1c60ed6e61467395ad1e91e72394b34b570b95ef 1c60ed6e61467395ad1e91e72394b34b570b95ef Sergiy Palamar <<EMAIL>> 1741601373 +0200	checkout: moving from master to sergiy/trigger-deploy
1c60ed6e61467395ad1e91e72394b34b570b95ef 893c04ed00f04bf304e57196ab27bd6c9c919787 Sergiy Palamar <<EMAIL>> 1741601446 +0200	commit: Trigger deployment CI workflow
893c04ed00f04bf304e57196ab27bd6c9c919787 893c04ed00f04bf304e57196ab27bd6c9c919787 Sergiy Palamar <<EMAIL>> 1741604002 +0200	checkout: moving from sergiy/trigger-deploy to sergiy/trigger-deploy
893c04ed00f04bf304e57196ab27bd6c9c919787 51667355f0d01fb932f5d3d9c39f0b9f97b95e88 Sergiy Palamar <<EMAIL>> 1741604096 +0200	commit: Remove [//]: # (delete) ... [//]: # (/delete) blocks
51667355f0d01fb932f5d3d9c39f0b9f97b95e88 1c60ed6e61467395ad1e91e72394b34b570b95ef Sergiy Palamar <<EMAIL>> 1741619761 +0200	checkout: moving from sergiy/trigger-deploy to master
1c60ed6e61467395ad1e91e72394b34b570b95ef 6732aeb5addce951552285963eee1b9175545fab Sergiy Palamar <<EMAIL>> 1741619763 +0200	pull --tags origin master: Fast-forward
6732aeb5addce951552285963eee1b9175545fab 6732aeb5addce951552285963eee1b9175545fab Sergiy Palamar <<EMAIL>> 1741620183 +0200	checkout: moving from master to sergiy/additionald-content-title-fix
6732aeb5addce951552285963eee1b9175545fab 6732aeb5addce951552285963eee1b9175545fab Sergiy Palamar <<EMAIL>> 1741620205 +0200	checkout: moving from sergiy/additionald-content-title-fix to sergiy/additionald-content-title-fix
6732aeb5addce951552285963eee1b9175545fab 6732aeb5addce951552285963eee1b9175545fab Sergiy Palamar <<EMAIL>> 1741620210 +0200	checkout: moving from sergiy/additionald-content-title-fix to sergiy/additionald-content-title-fix
6732aeb5addce951552285963eee1b9175545fab 6732aeb5addce951552285963eee1b9175545fab Sergiy Palamar <<EMAIL>> 1741620213 +0200	checkout: moving from sergiy/additionald-content-title-fix to sergiy/additionald-content-title-fix
6732aeb5addce951552285963eee1b9175545fab 0000000000000000000000000000000000000000 Sergiy Palamar <<EMAIL>> 1741620254 +0200	Branch: renamed refs/heads/sergiy/additionald-content-title-fix to refs/heads/sergiy/additional-content-title-fix
0000000000000000000000000000000000000000 6732aeb5addce951552285963eee1b9175545fab Sergiy Palamar <<EMAIL>> 1741620254 +0200	Branch: renamed refs/heads/sergiy/additionald-content-title-fix to refs/heads/sergiy/additional-content-title-fix
6732aeb5addce951552285963eee1b9175545fab 89f2b5067006f9b03879442e97e1db9fbf479ff5 Sergiy Palamar <<EMAIL>> 1741620311 +0200	commit: Additional content title fix
89f2b5067006f9b03879442e97e1db9fbf479ff5 6732aeb5addce951552285963eee1b9175545fab Sergiy Palamar <<EMAIL>> 1741621873 +0200	checkout: moving from sergiy/additional-content-title-fix to master
6732aeb5addce951552285963eee1b9175545fab 76eae957ef985d8fda3d0f1bcbc73dd7b7477e2b Sergiy Palamar <<EMAIL>> 1741621877 +0200	pull --tags origin master: Fast-forward
76eae957ef985d8fda3d0f1bcbc73dd7b7477e2b a648e8737c68a396b3a236a809fe4c620f670996 Sergiy Palamar <<EMAIL>> 1741686711 +0200	pull --tags origin master: Fast-forward
a648e8737c68a396b3a236a809fe4c620f670996 a648e8737c68a396b3a236a809fe4c620f670996 Sergiy Palamar <<EMAIL>> 1741698432 +0200	checkout: moving from master to sergiy/additional-title-fix
a648e8737c68a396b3a236a809fe4c620f670996 a6cdd34b1ef3bc032618e43d037d60071effd2ab Sergiy Palamar <<EMAIL>> 1741698483 +0200	commit: Additional content title comma fix
a6cdd34b1ef3bc032618e43d037d60071effd2ab a648e8737c68a396b3a236a809fe4c620f670996 Sergiy Palamar <<EMAIL>> 1741704171 +0200	checkout: moving from sergiy/additional-title-fix to master
a648e8737c68a396b3a236a809fe4c620f670996 a648e8737c68a396b3a236a809fe4c620f670996 Sergiy Palamar <<EMAIL>> 1741704190 +0200	checkout: moving from master to sergiy/design-fixes
a648e8737c68a396b3a236a809fe4c620f670996 9aa64bb783a9e9826d6443f15bc40298f575e743 Sergiy Palamar <<EMAIL>> 1741704206 +0200	commit: Design fixes
9aa64bb783a9e9826d6443f15bc40298f575e743 a648e8737c68a396b3a236a809fe4c620f670996 Sergiy Palamar <<EMAIL>> 1742201941 +0200	checkout: moving from sergiy/design-fixes to master
a648e8737c68a396b3a236a809fe4c620f670996 a648e8737c68a396b3a236a809fe4c620f670996 Sergiy Palamar <<EMAIL>> 1742201967 +0200	checkout: moving from master to sergiy/external-repo-parsing
a648e8737c68a396b3a236a809fe4c620f670996 f0f26199502451647e2e315e15dd0e6456524f0a Sergiy Palamar <<EMAIL>> 1742202873 +0200	commit: Submodules examples generating
f0f26199502451647e2e315e15dd0e6456524f0a a648e8737c68a396b3a236a809fe4c620f670996 Sergiy Palamar <<EMAIL>> 1742300286 +0200	checkout: moving from sergiy/external-repo-parsing to master
a648e8737c68a396b3a236a809fe4c620f670996 ef619089d7e1b7baccaf4ad039dc60c488fe958d Sergiy Palamar <<EMAIL>> 1742300290 +0200	pull --tags origin master: Fast-forward
ef619089d7e1b7baccaf4ad039dc60c488fe958d ed1b0fcdf5c210e853f52fdf8e51a80ffba19fc8 Sergiy Palamar <<EMAIL>> 1742301975 +0200	pull --tags origin master: Fast-forward
ed1b0fcdf5c210e853f52fdf8e51a80ffba19fc8 ed1b0fcdf5c210e853f52fdf8e51a80ffba19fc8 Sergiy Palamar <<EMAIL>> 1742302236 +0200	checkout: moving from master to sergiy/ci-fix
ed1b0fcdf5c210e853f52fdf8e51a80ffba19fc8 f5f219a53181b00b2bcac7c17b65dd50b33b5e1f Sergiy Palamar <<EMAIL>> 1742302409 +0200	commit: npm init fix
f5f219a53181b00b2bcac7c17b65dd50b33b5e1f 08490af09d4a5d6ec7c6e4c9c7f382197f2a55a2 Sergiy Palamar <<EMAIL>> 1742302571 +0200	commit: Typo fixed
08490af09d4a5d6ec7c6e4c9c7f382197f2a55a2 ba77c5c2a1eb3f3c0dd24dc00161ddfef2df2ae2 Sergiy Palamar <<EMAIL>> 1742303398 +0200	commit: init-submodules fix
ba77c5c2a1eb3f3c0dd24dc00161ddfef2df2ae2 ed1b0fcdf5c210e853f52fdf8e51a80ffba19fc8 Sergiy Palamar <<EMAIL>> 1742304990 +0200	checkout: moving from sergiy/ci-fix to master
ed1b0fcdf5c210e853f52fdf8e51a80ffba19fc8 b242ea1dd8bc1d77f5d3756967f62dc25dbb3d34 Sergiy Palamar <<EMAIL>> 1742304995 +0200	pull --tags origin master: Fast-forward
b242ea1dd8bc1d77f5d3756967f62dc25dbb3d34 b242ea1dd8bc1d77f5d3756967f62dc25dbb3d34 Sergiy Palamar <<EMAIL>> 1742305014 +0200	checkout: moving from master to sergiy/external-examples-titles
b242ea1dd8bc1d77f5d3756967f62dc25dbb3d34 62ebad2346ffabfb268bbcd1f5dc95d170404b75 Sergiy Palamar <<EMAIL>> 1742305033 +0200	commit: External examples short titles
62ebad2346ffabfb268bbcd1f5dc95d170404b75 b242ea1dd8bc1d77f5d3756967f62dc25dbb3d34 Sergiy Palamar <<EMAIL>> 1742316041 +0200	checkout: moving from sergiy/external-examples-titles to master
b242ea1dd8bc1d77f5d3756967f62dc25dbb3d34 b18d050b9e70f3ac74f9449e721ab505698821ef Sergiy Palamar <<EMAIL>> 1742316046 +0200	pull --tags origin master: Fast-forward
b18d050b9e70f3ac74f9449e721ab505698821ef 822b5e277d1adaedc40968f22dd5665d685400aa Sergiy Palamar <<EMAIL>> 1742380066 +0200	pull --tags origin master: Fast-forward
822b5e277d1adaedc40968f22dd5665d685400aa 13fec9a910c6ff2bfd2d093b78904f58090862e9 Sergiy Palamar <<EMAIL>> 1742981918 +0200	pull --tags origin master: Fast-forward
13fec9a910c6ff2bfd2d093b78904f58090862e9 13fec9a910c6ff2bfd2d093b78904f58090862e9 Sergiy Palamar <<EMAIL>> 1742982250 +0200	checkout: moving from master to sergiy/button-ui-fix
13fec9a910c6ff2bfd2d093b78904f58090862e9 1588efec942f6287bb72af7c6673622d22f74c4a Sergiy Palamar <<EMAIL>> 1742982313 +0200	commit: Home page button UI fix
1588efec942f6287bb72af7c6673622d22f74c4a 13fec9a910c6ff2bfd2d093b78904f58090862e9 Sergiy Palamar <<EMAIL>> 1742993227 +0200	checkout: moving from sergiy/button-ui-fix to master
13fec9a910c6ff2bfd2d093b78904f58090862e9 1c78797f13ebc27595269adb98c054267ea19121 Sergiy Palamar <<EMAIL>> 1742993230 +0200	pull --tags origin master: Fast-forward
1c78797f13ebc27595269adb98c054267ea19121 74819fdbd9fa75fe30ed80dd70627ed78af0b10a Sergiy Palamar <<EMAIL>> 1743177129 +0200	pull --tags origin master: Fast-forward
74819fdbd9fa75fe30ed80dd70627ed78af0b10a 33b1c589668c41996a64418060244525db4d1b24 Sergiy Palamar <<EMAIL>> 1743241917 +0200	pull --tags origin master: Fast-forward
33b1c589668c41996a64418060244525db4d1b24 ff9bf5c386ed8ebfb505669b0025903d8adaadf9 Sergiy Palamar <<EMAIL>> 1743408523 +0300	pull --tags origin master: Fast-forward
ff9bf5c386ed8ebfb505669b0025903d8adaadf9 ff9bf5c386ed8ebfb505669b0025903d8adaadf9 Sergiy Palamar <<EMAIL>> 1743409119 +0300	checkout: moving from master to sergiy/test-search-exclude
ff9bf5c386ed8ebfb505669b0025903d8adaadf9 bd41cdeb31ccccab2f8abd2906029f71801af495 Sergiy Palamar <<EMAIL>> 1743409150 +0300	commit: test.md excluded from search
bd41cdeb31ccccab2f8abd2906029f71801af495 bd41cdeb31ccccab2f8abd2906029f71801af495 Sergiy Palamar <<EMAIL>> 1743443247 +0300	reset: moving to HEAD
bd41cdeb31ccccab2f8abd2906029f71801af495 bd41cdeb31ccccab2f8abd2906029f71801af495 Sergiy Palamar <<EMAIL>> 1743443350 +0300	reset: moving to HEAD
bd41cdeb31ccccab2f8abd2906029f71801af495 ff9bf5c386ed8ebfb505669b0025903d8adaadf9 Sergiy Palamar <<EMAIL>> 1743443359 +0300	checkout: moving from sergiy/test-search-exclude to master
ff9bf5c386ed8ebfb505669b0025903d8adaadf9 930cb124392502d730dbc5896b82cd38c7f73b95 Sergiy Palamar <<EMAIL>> 1743443363 +0300	pull --tags origin master: Fast-forward
930cb124392502d730dbc5896b82cd38c7f73b95 930cb124392502d730dbc5896b82cd38c7f73b95 Sergiy Palamar <<EMAIL>> 1743443382 +0300	checkout: moving from master to sergiy/json-scheme
930cb124392502d730dbc5896b82cd38c7f73b95 8ba237616ed99adaa98d4d427dc463cd5b69121c Sergiy Palamar <<EMAIL>> 1743443492 +0300	commit: JSON scheme custom block
8ba237616ed99adaa98d4d427dc463cd5b69121c ea561ed18782020bf602dceeb7db9c509bd3a073 Sergiy Palamar <<EMAIL>> 1743443559 +0300	commit: CSS cleared
ea561ed18782020bf602dceeb7db9c509bd3a073 ea561ed18782020bf602dceeb7db9c509bd3a073 Sergiy Palamar <<EMAIL>> 1743589124 +0300	reset: moving to HEAD
ea561ed18782020bf602dceeb7db9c509bd3a073 930cb124392502d730dbc5896b82cd38c7f73b95 Sergiy Palamar <<EMAIL>> 1743589124 +0300	checkout: moving from sergiy/json-scheme to master
930cb124392502d730dbc5896b82cd38c7f73b95 9dc9c43e4568603f3a11f346e7e16221c3f0144a Sergiy Palamar <<EMAIL>> 1743589128 +0300	pull --tags origin master: Fast-forward
9dc9c43e4568603f3a11f346e7e16221c3f0144a 9dc9c43e4568603f3a11f346e7e16221c3f0144a Sergiy Palamar <<EMAIL>> 1743589141 +0300	checkout: moving from master to sergiy/json-scheme-fix
9dc9c43e4568603f3a11f346e7e16221c3f0144a a61d0c8af8cbf7e7fe3db0267b7c51b7f2e95ebd Sergiy Palamar <<EMAIL>> 1743589288 +0300	commit: JSON scheme fix
a61d0c8af8cbf7e7fe3db0267b7c51b7f2e95ebd ed21ddcc50bbd564e44f58d8ce4c7e781c5d5ece Sergiy Palamar <<EMAIL>> 1743589357 +0300	commit: console.log removed
ed21ddcc50bbd564e44f58d8ce4c7e781c5d5ece 9dc9c43e4568603f3a11f346e7e16221c3f0144a Sergiy Palamar <<EMAIL>> 1743589835 +0300	checkout: moving from sergiy/json-scheme-fix to master
9dc9c43e4568603f3a11f346e7e16221c3f0144a a0293678e970c295570072a2f222c69fca56e8ba Sergiy Palamar <<EMAIL>> 1743589840 +0300	pull --tags origin master: Fast-forward
a0293678e970c295570072a2f222c69fca56e8ba a781f5662e8271a15d9184de7aff8ae810545ace Sergiy Palamar <<EMAIL>> 1743590052 +0300	commit: Cora AI popular docs changes
a781f5662e8271a15d9184de7aff8ae810545ace a781f5662e8271a15d9184de7aff8ae810545ace Sergiy Palamar <<EMAIL>> 1743590084 +0300	checkout: moving from master to sergiy/popular-docs-changes
a781f5662e8271a15d9184de7aff8ae810545ace a781f5662e8271a15d9184de7aff8ae810545ace Sergiy Palamar <<EMAIL>> 1743590196 +0300	reset: moving to HEAD
a781f5662e8271a15d9184de7aff8ae810545ace a781f5662e8271a15d9184de7aff8ae810545ace Sergiy Palamar <<EMAIL>> 1743590218 +0300	reset: moving to HEAD
a781f5662e8271a15d9184de7aff8ae810545ace a781f5662e8271a15d9184de7aff8ae810545ace Sergiy Palamar <<EMAIL>> 1743590304 +0300	reset: moving to a781f5662e8271a15d9184de7aff8ae810545ace
a781f5662e8271a15d9184de7aff8ae810545ace a781f5662e8271a15d9184de7aff8ae810545ace Sergiy Palamar <<EMAIL>> 1743590326 +0300	reset: moving to a781f5662e8271a15d9184de7aff8ae810545ace
a781f5662e8271a15d9184de7aff8ae810545ace a781f5662e8271a15d9184de7aff8ae810545ace Sergiy Palamar <<EMAIL>> 1743699263 +0300	checkout: moving from sergiy/popular-docs-changes to master
a781f5662e8271a15d9184de7aff8ae810545ace a88b7d71360428bf98de3ccab913ecb6ec37b2e9 Sergiy Palamar <<EMAIL>> 1743699277 +0300	merge origin/master: Merge made by the 'ort' strategy.
a88b7d71360428bf98de3ccab913ecb6ec37b2e9 a88b7d71360428bf98de3ccab913ecb6ec37b2e9 Sergiy Palamar <<EMAIL>> 1743699473 +0300	checkout: moving from master to tmp
a88b7d71360428bf98de3ccab913ecb6ec37b2e9 b3e67e58feef1191faf9270af3f70ab916a48b26 Sergiy Palamar <<EMAIL>> 1743699489 +0300	checkout: moving from tmp to master
b3e67e58feef1191faf9270af3f70ab916a48b26 b3e67e58feef1191faf9270af3f70ab916a48b26 Sergiy Palamar <<EMAIL>> 1743699650 +0300	checkout: moving from master to sergiy/dmitriy-revert
b3e67e58feef1191faf9270af3f70ab916a48b26 999880f03b1e8043dfc12c46f55e6ba7396fccbf Sergiy Palamar <<EMAIL>> 1743699691 +0300	commit: APM onboarding changes reverted
999880f03b1e8043dfc12c46f55e6ba7396fccbf b3e67e58feef1191faf9270af3f70ab916a48b26 Sergiy Palamar <<EMAIL>> 1744010915 +0300	checkout: moving from sergiy/dmitriy-revert to master
b3e67e58feef1191faf9270af3f70ab916a48b26 8e3930835b45d1ad386514e5a4373accc3260014 Sergiy Palamar <<EMAIL>> 1744010922 +0300	merge origin/master: Fast-forward
8e3930835b45d1ad386514e5a4373accc3260014 8e3930835b45d1ad386514e5a4373accc3260014 Sergiy Palamar <<EMAIL>> 1744010962 +0300	checkout: moving from master to sergiy/json-schema-apply
8e3930835b45d1ad386514e5a4373accc3260014 062e683e8844ab7db42e27938cf3df3cb3c68c81 Sergiy Palamar <<EMAIL>> 1744012837 +0300	commit: JSON schema applied
062e683e8844ab7db42e27938cf3df3cb3c68c81 8e3930835b45d1ad386514e5a4373accc3260014 Sergiy Palamar <<EMAIL>> 1744184705 +0300	checkout: moving from sergiy/json-schema-apply to master
8e3930835b45d1ad386514e5a4373accc3260014 f7b007cf29553b2fd685dded15e7b28f65716cbb Sergiy Palamar <<EMAIL>> 1744184710 +0300	merge origin/master: Fast-forward
f7b007cf29553b2fd685dded15e7b28f65716cbb 9baee1cec58b4ee07a088598ca295954806606e0 Sergiy Palamar <<EMAIL>> 1744702909 +0300	merge origin/master: Fast-forward
9baee1cec58b4ee07a088598ca295954806606e0 de8be42acfe318158153e5326f1b4da6acfa0fb6 Sergiy Palamar <<EMAIL>> 1744726856 +0300	merge origin/master: Fast-forward
de8be42acfe318158153e5326f1b4da6acfa0fb6 2bd30e5a0cc0a744f732f2f4728fbde064095173 Sergiy Palamar <<EMAIL>> 1744794124 +0300	merge origin/master: Fast-forward
2bd30e5a0cc0a744f732f2f4728fbde064095173 ece967332f7b2504679e60c543ddbbf9dea449aa Sergiy Palamar <<EMAIL>> 1744875682 +0300	merge origin/master: Fast-forward
ece967332f7b2504679e60c543ddbbf9dea449aa ece967332f7b2504679e60c543ddbbf9dea449aa Sergiy Palamar <<EMAIL>> 1745231752 +0300	checkout: moving from master to sergiy/global-categories
ece967332f7b2504679e60c543ddbbf9dea449aa edaf42582f8279f86afbf1dcde0c54c4e28f9fd7 Sergiy Palamar <<EMAIL>> 1745231779 +0300	commit: Global categories upgrades
edaf42582f8279f86afbf1dcde0c54c4e28f9fd7 9a26d1b092e9697aec74c8e6a7907c5e46f8bd05 Sergiy Palamar <<EMAIL>> 1745231790 +0300	merge origin/master: Merge made by the 'ort' strategy.
9a26d1b092e9697aec74c8e6a7907c5e46f8bd05 ece967332f7b2504679e60c543ddbbf9dea449aa Sergiy Palamar <<EMAIL>> 1745308848 +0300	checkout: moving from sergiy/global-categories to master
ece967332f7b2504679e60c543ddbbf9dea449aa 6863c264ad7b9a2af2407bcb71c288b26798d031 Sergiy Palamar <<EMAIL>> 1745308856 +0300	merge origin/master: Fast-forward
6863c264ad7b9a2af2407bcb71c288b26798d031 2614a7ab2b103a01d96af293872018b5de2d560a Sergiy Palamar <<EMAIL>> 1745834234 +0300	merge origin/master: Fast-forward
2614a7ab2b103a01d96af293872018b5de2d560a 2614a7ab2b103a01d96af293872018b5de2d560a Sergiy Palamar <<EMAIL>> 1745911600 +0300	checkout: moving from master to sergiy/inarray-redirect
2614a7ab2b103a01d96af293872018b5de2d560a 49c2e9fc828f8b19ffe43545f8b64f4d4298c587 Sergiy Palamar <<EMAIL>> 1745911640 +0300	commit: inArray redirect added
49c2e9fc828f8b19ffe43545f8b64f4d4298c587 2614a7ab2b103a01d96af293872018b5de2d560a Sergiy Palamar <<EMAIL>> 1746017403 +0300	checkout: moving from sergiy/inarray-redirect to master
2614a7ab2b103a01d96af293872018b5de2d560a 281c858dcda035428238b53e0c5e8555bb488b91 Sergiy Palamar <<EMAIL>> 1746017411 +0300	merge origin/master: Fast-forward
281c858dcda035428238b53e0c5e8555bb488b91 7b1f4cdcfe8daeb0bcf1d973ce9ae5419afa0c7d Sergiy Palamar <<EMAIL>> 1746017427 +0300	checkout: moving from master to continuous-profiling
7b1f4cdcfe8daeb0bcf1d973ce9ae5419afa0c7d 8d128230e66b94083796058869c86ae094aa1797 Sergiy Palamar <<EMAIL>> 1746018061 +0300	commit: Images fixes
8d128230e66b94083796058869c86ae094aa1797 281c858dcda035428238b53e0c5e8555bb488b91 Sergiy Palamar <<EMAIL>> 1746018131 +0300	checkout: moving from continuous-profiling to master
281c858dcda035428238b53e0c5e8555bb488b91 12a8c9ee86f7a45a80a2b60f0a6b32c341c0c73a Sergiy Palamar <<EMAIL>> 1746018136 +0300	merge origin/master: Fast-forward
12a8c9ee86f7a45a80a2b60f0a6b32c341c0c73a 12a8c9ee86f7a45a80a2b60f0a6b32c341c0c73a Sergiy Palamar <<EMAIL>> 1746018158 +0300	checkout: moving from master to sergiy/profiling-img-fix
12a8c9ee86f7a45a80a2b60f0a6b32c341c0c73a 843c0b2477fa652027334271ed6994d197e16da5 Sergiy Palamar <<EMAIL>> 1746018162 +0300	cherry-pick: Images fixes
843c0b2477fa652027334271ed6994d197e16da5 12a8c9ee86f7a45a80a2b60f0a6b32c341c0c73a Sergiy Palamar <<EMAIL>> 1746085100 +0300	checkout: moving from sergiy/profiling-img-fix to master
12a8c9ee86f7a45a80a2b60f0a6b32c341c0c73a b388b260665fccbb406aec46c2588ee091742fe9 Sergiy Palamar <<EMAIL>> 1746085113 +0300	merge origin/master: Fast-forward
b388b260665fccbb406aec46c2588ee091742fe9 b388b260665fccbb406aec46c2588ee091742fe9 Sergiy Palamar <<EMAIL>> 1746085163 +0300	checkout: moving from master to sergiy/platform-tooltip
b388b260665fccbb406aec46c2588ee091742fe9 82a75f55735d0eda082dacfcc1dc71b36279a1a9 Sergiy Palamar <<EMAIL>> 1746085234 +0300	commit: Global category dropdown tooltip changed
82a75f55735d0eda082dacfcc1dc71b36279a1a9 b388b260665fccbb406aec46c2588ee091742fe9 Sergiy Palamar <<EMAIL>> 1746426638 +0300	checkout: moving from sergiy/platform-tooltip to master
b388b260665fccbb406aec46c2588ee091742fe9 36384e2aafc2f5ca8b08606bd467530bd569a46f Sergiy Palamar <<EMAIL>> 1746426643 +0300	merge origin/master: Fast-forward
36384e2aafc2f5ca8b08606bd467530bd569a46f 36384e2aafc2f5ca8b08606bd467530bd569a46f Sergiy Palamar <<EMAIL>> 1746429871 +0300	checkout: moving from master to sergiy/toc-depth
36384e2aafc2f5ca8b08606bd467530bd569a46f 46ee31256a66afc89ecdbb8c4ec13e44d2f3e5fc Sergiy Palamar <<EMAIL>> 1746429891 +0300	commit: ToC depth changed
46ee31256a66afc89ecdbb8c4ec13e44d2f3e5fc 46ee31256a66afc89ecdbb8c4ec13e44d2f3e5fc Sergiy Palamar <<EMAIL>> 1746441014 +0300	reset: moving to HEAD
46ee31256a66afc89ecdbb8c4ec13e44d2f3e5fc 36384e2aafc2f5ca8b08606bd467530bd569a46f Sergiy Palamar <<EMAIL>> 1746441027 +0300	checkout: moving from sergiy/toc-depth to master
36384e2aafc2f5ca8b08606bd467530bd569a46f 281c858dcda035428238b53e0c5e8555bb488b91 Sergiy Palamar <<EMAIL>> 1746442042 +0300	checkout: moving from master to global-categories-test
281c858dcda035428238b53e0c5e8555bb488b91 9984ec951ba06e2a635288652e5878e534a0357b Sergiy Palamar <<EMAIL>> 1746446396 +0300	merge origin/global-categories-test: Fast-forward
9984ec951ba06e2a635288652e5878e534a0357b aa8fa8097fbe0dce45e83c7a1957349bbaf37bd5 Sergiy Palamar <<EMAIL>> 1746457462 +0300	commit: Global categories fixes
aa8fa8097fbe0dce45e83c7a1957349bbaf37bd5 36384e2aafc2f5ca8b08606bd467530bd569a46f Sergiy Palamar <<EMAIL>> 1746457688 +0300	checkout: moving from global-categories-test to master
36384e2aafc2f5ca8b08606bd467530bd569a46f 36384e2aafc2f5ca8b08606bd467530bd569a46f Sergiy Palamar <<EMAIL>> 1746457735 +0300	checkout: moving from master to sergiy/submodules-parsing-changes
36384e2aafc2f5ca8b08606bd467530bd569a46f 1071a480fbcecb5cf10ae8d7fb281a1e7c661c31 Sergiy Palamar <<EMAIL>> 1746458870 +0300	commit: Submodules parsing: convert GitHub alerts to mkdocs
1071a480fbcecb5cf10ae8d7fb281a1e7c661c31 0000000000000000000000000000000000000000 Sergiy Palamar <<EMAIL>> 1746458888 +0300	Branch: renamed refs/heads/sergiy/submodules-parsing-changes to refs/heads/sergiy/submodules-parsing-alerts
0000000000000000000000000000000000000000 1071a480fbcecb5cf10ae8d7fb281a1e7c661c31 Sergiy Palamar <<EMAIL>> 1746458888 +0300	Branch: renamed refs/heads/sergiy/submodules-parsing-changes to refs/heads/sergiy/submodules-parsing-alerts
1071a480fbcecb5cf10ae8d7fb281a1e7c661c31 1071a480fbcecb5cf10ae8d7fb281a1e7c661c31 Sergiy Palamar <<EMAIL>> 1746459050 +0300	checkout: moving from sergiy/submodules-parsing-alerts to sergiy/submodules-parsing-spec-tags
1071a480fbcecb5cf10ae8d7fb281a1e7c661c31 7d7b1da69043568681456ff6a86baf1c23c1c454 Sergiy Palamar <<EMAIL>> 1746460327 +0300	commit: Submodules parsing: use HTML comment tag instead of [//]
7d7b1da69043568681456ff6a86baf1c23c1c454 7d7b1da69043568681456ff6a86baf1c23c1c454 Sergiy Palamar <<EMAIL>> 1746520787 +0300	reset: moving to HEAD
7d7b1da69043568681456ff6a86baf1c23c1c454 36384e2aafc2f5ca8b08606bd467530bd569a46f Sergiy Palamar <<EMAIL>> 1746524483 +0300	checkout: moving from sergiy/submodules-parsing-spec-tags to master
36384e2aafc2f5ca8b08606bd467530bd569a46f 53744e229c159cb45b9ff478f0c140fed811131e Sergiy Palamar <<EMAIL>> 1746524489 +0300	merge origin/master: Fast-forward
53744e229c159cb45b9ff478f0c140fed811131e 53744e229c159cb45b9ff478f0c140fed811131e Sergiy Palamar <<EMAIL>> 1746524519 +0300	checkout: moving from master to sergiy/md-linter-prettier
53744e229c159cb45b9ff478f0c140fed811131e 53744e229c159cb45b9ff478f0c140fed811131e Sergiy Palamar <<EMAIL>> 1746538387 +0300	reset: moving to HEAD
53744e229c159cb45b9ff478f0c140fed811131e 53744e229c159cb45b9ff478f0c140fed811131e Sergiy Palamar <<EMAIL>> 1746538428 +0300	reset: moving to HEAD
53744e229c159cb45b9ff478f0c140fed811131e aa8fa8097fbe0dce45e83c7a1957349bbaf37bd5 Sergiy Palamar <<EMAIL>> 1746538442 +0300	checkout: moving from sergiy/md-linter-prettier to global-categories-test
aa8fa8097fbe0dce45e83c7a1957349bbaf37bd5 3696911d17b7f46a4ddbb4d1ccdf8586ea83c321 Sergiy Palamar <<EMAIL>> 1746538870 +0300	commit: Show all global categories items in navbar
3696911d17b7f46a4ddbb4d1ccdf8586ea83c321 53744e229c159cb45b9ff478f0c140fed811131e Sergiy Palamar <<EMAIL>> 1746609356 +0300	checkout: moving from global-categories-test to master
53744e229c159cb45b9ff478f0c140fed811131e 70495a9c912f65185835ce68164fc53ab8a1fac6 Sergiy Palamar <<EMAIL>> 1746609364 +0300	merge origin/master: Fast-forward
70495a9c912f65185835ce68164fc53ab8a1fac6 7d7b1da69043568681456ff6a86baf1c23c1c454 Sergiy Palamar <<EMAIL>> 1746611469 +0300	checkout: moving from master to sergiy/submodules-parsing-spec-tags
7d7b1da69043568681456ff6a86baf1c23c1c454 20d5e8dc789bee0b07698e9cd9e7a41b56900f7f Sergiy Palamar <<EMAIL>> 1746611508 +0300	commit (merge): Merge branch 'master' into sergiy/submodules-parsing-spec-tags
20d5e8dc789bee0b07698e9cd9e7a41b56900f7f 70495a9c912f65185835ce68164fc53ab8a1fac6 Sergiy Palamar <<EMAIL>> 1746619461 +0300	checkout: moving from sergiy/submodules-parsing-spec-tags to master
70495a9c912f65185835ce68164fc53ab8a1fac6 5772461b37a2f32f3ea4b519fa4393d16a63b306 Sergiy Palamar <<EMAIL>> 1746619469 +0300	merge origin/master: Fast-forward
5772461b37a2f32f3ea4b519fa4393d16a63b306 5772461b37a2f32f3ea4b519fa4393d16a63b306 Sergiy Palamar <<EMAIL>> 1746619481 +0300	checkout: moving from master to sergiy/last-upd-fix
5772461b37a2f32f3ea4b519fa4393d16a63b306 3a6ef30065725c2ed65504140ce2111f0cea3585 Sergiy Palamar <<EMAIL>> 1746619510 +0300	commit: Last updated date fixed
3a6ef30065725c2ed65504140ce2111f0cea3585 20d5e8dc789bee0b07698e9cd9e7a41b56900f7f Sergiy Palamar <<EMAIL>> 1746621122 +0300	checkout: moving from sergiy/last-upd-fix to sergiy/submodules-parsing-spec-tags
20d5e8dc789bee0b07698e9cd9e7a41b56900f7f 31320b9b7c6f925be8c7fc0fd695325ba3a470a9 Sergiy Palamar <<EMAIL>> 1746624554 +0300	commit: Submodules parsing: split functionality implemented
31320b9b7c6f925be8c7fc0fd695325ba3a470a9 31320b9b7c6f925be8c7fc0fd695325ba3a470a9 Sergiy Palamar <<EMAIL>> 1746624573 +0300	merge master: updating HEAD
31320b9b7c6f925be8c7fc0fd695325ba3a470a9 7511556d7935847facad0fb33e9cef54ecc901d8 Sergiy Palamar <<EMAIL>> 1746624599 +0300	merge refs/heads/master: Merge made by the 'ort' strategy.
7511556d7935847facad0fb33e9cef54ecc901d8 7511556d7935847facad0fb33e9cef54ecc901d8 Sergiy Palamar <<EMAIL>> 1746691814 +0300	reset: moving to HEAD
7511556d7935847facad0fb33e9cef54ecc901d8 d9042635aaeabde9c68ccede37ce61e2d1943d32 Sergiy Palamar <<EMAIL>> 1746691818 +0300	checkout: moving from sergiy/submodules-parsing-spec-tags to master
d9042635aaeabde9c68ccede37ce61e2d1943d32 c6c68d6cc12da00536b0ab2b3bdc9f6b8a953458 Sergiy Palamar <<EMAIL>> 1746691823 +0300	merge origin/master: Fast-forward
c6c68d6cc12da00536b0ab2b3bdc9f6b8a953458 c6c68d6cc12da00536b0ab2b3bdc9f6b8a953458 Sergiy Palamar <<EMAIL>> 1746691853 +0300	checkout: moving from master to sergiy/img-zoom
c6c68d6cc12da00536b0ab2b3bdc9f6b8a953458 04ac63ef52a4d7b656bb58e3ccb4df119fe352dc Sergiy Palamar <<EMAIL>> 1746692067 +0300	commit: Lightbox lib configured (image zoom)
04ac63ef52a4d7b656bb58e3ccb4df119fe352dc 04ac63ef52a4d7b656bb58e3ccb4df119fe352dc Sergiy Palamar <<EMAIL>> 1746692437 +0300	reset: moving to HEAD
04ac63ef52a4d7b656bb58e3ccb4df119fe352dc c6c68d6cc12da00536b0ab2b3bdc9f6b8a953458 Sergiy Palamar <<EMAIL>> 1746692452 +0300	checkout: moving from sergiy/img-zoom to master
c6c68d6cc12da00536b0ab2b3bdc9f6b8a953458 4efe2f56a26cfa8f0e0cab08d59fbea81fe0e07b Sergiy Palamar <<EMAIL>> 1746692462 +0300	checkout: moving from master to PLTD-1115/sync-operator-docs
4efe2f56a26cfa8f0e0cab08d59fbea81fe0e07b 38ac01ce536d1cf76f1509071ee0cb38cbf70dea Sergiy Palamar <<EMAIL>> 1746694416 +0300	commit: Submodules fix
38ac01ce536d1cf76f1509071ee0cb38cbf70dea 98337926e6c3cc2c7e6abd1e766ae168ed0e783a Sergiy Palamar <<EMAIL>> 1746699902 +0300	commit: coralogix-operator submodule config fix
98337926e6c3cc2c7e6abd1e766ae168ed0e783a c6c68d6cc12da00536b0ab2b3bdc9f6b8a953458 Sergiy Palamar <<EMAIL>> 1747047002 +0300	checkout: moving from PLTD-1115/sync-operator-docs to master
c6c68d6cc12da00536b0ab2b3bdc9f6b8a953458 867f8e5850671915446d9ba71d6a307dfc4c9463 Sergiy Palamar <<EMAIL>> 1747047007 +0300	merge origin/master: Fast-forward
867f8e5850671915446d9ba71d6a307dfc4c9463 5d799107b49cfe7ae5719b5c02329a4f02536f41 Sergiy Palamar <<EMAIL>> 1747117970 +0300	merge origin/master: Fast-forward
5d799107b49cfe7ae5719b5c02329a4f02536f41 98337926e6c3cc2c7e6abd1e766ae168ed0e783a Sergiy Palamar <<EMAIL>> 1747128500 +0300	checkout: moving from master to PLTD-1115/sync-operator-docs
98337926e6c3cc2c7e6abd1e766ae168ed0e783a 86fc936cc116c1d3ddec17b5e7e3314a9b4c5d0d Sergiy Palamar <<EMAIL>> 1747128505 +0300	merge origin/PLTD-1115/sync-operator-docs: Fast-forward
86fc936cc116c1d3ddec17b5e7e3314a9b4c5d0d 5d799107b49cfe7ae5719b5c02329a4f02536f41 Sergiy Palamar <<EMAIL>> 1747316129 +0300	checkout: moving from PLTD-1115/sync-operator-docs to master
5d799107b49cfe7ae5719b5c02329a4f02536f41 559d8c856d44f09ccdbd7a80ea5cfe9bc6a6e768 Sergiy Palamar <<EMAIL>> 1747316134 +0300	merge origin/master: Fast-forward
559d8c856d44f09ccdbd7a80ea5cfe9bc6a6e768 7511556d7935847facad0fb33e9cef54ecc901d8 Sergiy Palamar <<EMAIL>> 1747316305 +0300	checkout: moving from master to sergiy/submodules-parsing-spec-tags
7511556d7935847facad0fb33e9cef54ecc901d8 0d0fa1bf1f55a89f831a24c530f7c4e8c4896924 Sergiy Palamar <<EMAIL>> 1747316517 +0300	merge master: Merge made by the 'ort' strategy.
0d0fa1bf1f55a89f831a24c530f7c4e8c4896924 559d8c856d44f09ccdbd7a80ea5cfe9bc6a6e768 Sergiy Palamar <<EMAIL>> 1747317715 +0300	checkout: moving from sergiy/submodules-parsing-spec-tags to master
559d8c856d44f09ccdbd7a80ea5cfe9bc6a6e768 88979018ad3a5169d5c5c48f5c01a520bd87027e Sergiy Palamar <<EMAIL>> 1747317719 +0300	merge origin/master: Fast-forward
88979018ad3a5169d5c5c48f5c01a520bd87027e 88979018ad3a5169d5c5c48f5c01a520bd87027e Sergiy Palamar <<EMAIL>> 1747320113 +0300	checkout: moving from master to sergiy/docs-path-replace
88979018ad3a5169d5c5c48f5c01a520bd87027e f7fa45ba185920c09bf6a000dbe3728df832d8ae Sergiy Palamar <<EMAIL>> 1747320183 +0300	commit: Submodules: replace 'docs' to 'documents' in path
f7fa45ba185920c09bf6a000dbe3728df832d8ae 6a3759fc3b5230fe3604901cd81802828b10034e Sergiy Palamar <<EMAIL>> 1747320208 +0300	commit (merge): Merge branch 'master' into sergiy/docs-path-replace
6a3759fc3b5230fe3604901cd81802828b10034e de7570b74a4b5d0cc3fcb968218eedf6e524817c Sergiy Palamar <<EMAIL>> 1747320766 +0300	checkout: moving from sergiy/docs-path-replace to master
de7570b74a4b5d0cc3fcb968218eedf6e524817c d960249e62fb75678530b8ade6004f233ad09440 Sergiy Palamar <<EMAIL>> 1747320770 +0300	merge origin/master: Fast-forward
d960249e62fb75678530b8ade6004f233ad09440 d960249e62fb75678530b8ade6004f233ad09440 Sergiy Palamar <<EMAIL>> 1747320997 +0300	checkout: moving from master to sergiy/coralogix-operator-meta-fix
d960249e62fb75678530b8ade6004f233ad09440 873df82bf01253d62148716ec103e236f6bf11a1 Sergiy Palamar <<EMAIL>> 1747321024 +0300	commit: coralogix-operator external docs meta data fix
873df82bf01253d62148716ec103e236f6bf11a1 d960249e62fb75678530b8ade6004f233ad09440 Sergiy Palamar <<EMAIL>> 1747387990 +0300	checkout: moving from sergiy/coralogix-operator-meta-fix to master
d960249e62fb75678530b8ade6004f233ad09440 28db52a9d9e7179e4a57a2fb52816676fd9fdfb6 Sergiy Palamar <<EMAIL>> 1747387998 +0300	merge origin/master: Fast-forward
28db52a9d9e7179e4a57a2fb52816676fd9fdfb6 11f598711f7ea9526e0607851c848917c3885977 Sergiy Palamar <<EMAIL>> 1747388011 +0300	checkout: moving from master to ai-spm-upd
11f598711f7ea9526e0607851c848917c3885977 28db52a9d9e7179e4a57a2fb52816676fd9fdfb6 Sergiy Palamar <<EMAIL>> 1747388741 +0300	checkout: moving from ai-spm-upd to master
28db52a9d9e7179e4a57a2fb52816676fd9fdfb6 28db52a9d9e7179e4a57a2fb52816676fd9fdfb6 Sergiy Palamar <<EMAIL>> 1747388769 +0300	checkout: moving from master to ai-spm-upd_cleared
28db52a9d9e7179e4a57a2fb52816676fd9fdfb6 b0fc2a248c83870f1f1313a5843d87d1072702f1 Sergiy Palamar <<EMAIL>> 1747388967 +0300	commit: AI-SPM update
b0fc2a248c83870f1f1313a5843d87d1072702f1 11f598711f7ea9526e0607851c848917c3885977 Sergiy Palamar <<EMAIL>> 1747389438 +0300	checkout: moving from ai-spm-upd_cleared to ai-spm-upd
11f598711f7ea9526e0607851c848917c3885977 b0fc2a248c83870f1f1313a5843d87d1072702f1 Sergiy Palamar <<EMAIL>> 1747389547 +0300	checkout: moving from ai-spm-upd to ai-spm-upd_cleared
b0fc2a248c83870f1f1313a5843d87d1072702f1 11f598711f7ea9526e0607851c848917c3885977 Sergiy Palamar <<EMAIL>> 1747389627 +0300	checkout: moving from ai-spm-upd_cleared to ai-spm-upd
11f598711f7ea9526e0607851c848917c3885977 b0fc2a248c83870f1f1313a5843d87d1072702f1 Sergiy Palamar <<EMAIL>> 1747389646 +0300	checkout: moving from ai-spm-upd to ai-spm-upd_cleared
b0fc2a248c83870f1f1313a5843d87d1072702f1 b927329bbbbcebae1ca35a67dac234ea4fe94674 Sergiy Palamar <<EMAIL>> 1747389677 +0300	commit: ai-observability
b927329bbbbcebae1ca35a67dac234ea4fe94674 28db52a9d9e7179e4a57a2fb52816676fd9fdfb6 Sergiy Palamar <<EMAIL>> 1747390853 +0300	checkout: moving from ai-spm-upd_cleared to master
28db52a9d9e7179e4a57a2fb52816676fd9fdfb6 1d3cb02ecf0b9bdaa6bda94f9d3b80a1a9686b6a Sergiy Palamar <<EMAIL>> 1747397676 +0300	merge origin/master: Fast-forward
1d3cb02ecf0b9bdaa6bda94f9d3b80a1a9686b6a 1d3cb02ecf0b9bdaa6bda94f9d3b80a1a9686b6a Sergiy Palamar <<EMAIL>> 1747399235 +0300	checkout: moving from master to test
1d3cb02ecf0b9bdaa6bda94f9d3b80a1a9686b6a b085f716210c943b3d22f6da8f0dd3634361f42d Sergiy Palamar <<EMAIL>> 1747401526 +0300	checkout: moving from test to ai-instrumentation
b085f716210c943b3d22f6da8f0dd3634361f42d 1d3cb02ecf0b9bdaa6bda94f9d3b80a1a9686b6a Sergiy Palamar <<EMAIL>> 1747640782 +0300	checkout: moving from ai-instrumentation to master
1d3cb02ecf0b9bdaa6bda94f9d3b80a1a9686b6a 0d0fa1bf1f55a89f831a24c530f7c4e8c4896924 Sergiy Palamar <<EMAIL>> 1747650507 +0300	checkout: moving from master to sergiy/submodules-parsing-spec-tags
0d0fa1bf1f55a89f831a24c530f7c4e8c4896924 29942c636608da6672e9e596fa5b7eb22e8acf6f Sergiy Palamar <<EMAIL>> 1747650512 +0300	merge master: Merge made by the 'ort' strategy.
29942c636608da6672e9e596fa5b7eb22e8acf6f 60262ab2eb3adeb1b7123ff1808fee6206b6a54f Sergiy Palamar <<EMAIL>> 1747650724 +0300	commit: Submodules: use master branch
60262ab2eb3adeb1b7123ff1808fee6206b6a54f 1d3cb02ecf0b9bdaa6bda94f9d3b80a1a9686b6a Sergiy Palamar <<EMAIL>> 1747651009 +0300	checkout: moving from sergiy/submodules-parsing-spec-tags to master
1d3cb02ecf0b9bdaa6bda94f9d3b80a1a9686b6a fb2d7123463f7f4f352831302c660d21965eec8b Sergiy Palamar <<EMAIL>> 1747654472 +0300	merge origin/master: Fast-forward
fb2d7123463f7f4f352831302c660d21965eec8b fb2d7123463f7f4f352831302c660d21965eec8b Sergiy Palamar <<EMAIL>> 1747655584 +0300	checkout: moving from master to sergiy/pip-ci-fix
fb2d7123463f7f4f352831302c660d21965eec8b 31e1a7d9699c92638e93491d9841ab633f091c26 Sergiy Palamar <<EMAIL>> 1747655594 +0300	commit: PIP CI fix
31e1a7d9699c92638e93491d9841ab633f091c26 2cf4692255634cd109def8e0000da401620d94bc Sergiy Palamar <<EMAIL>> 1747656652 +0300	commit: PIP version downgrade
2cf4692255634cd109def8e0000da401620d94bc fb2d7123463f7f4f352831302c660d21965eec8b Sergiy Palamar <<EMAIL>> 1747659975 +0300	checkout: moving from sergiy/pip-ci-fix to master
fb2d7123463f7f4f352831302c660d21965eec8b e11f9bbfd059988defa7cf5f74a19ac6d903c18f Sergiy Palamar <<EMAIL>> 1747659978 +0300	merge origin/master: Fast-forward
e11f9bbfd059988defa7cf5f74a19ac6d903c18f e11f9bbfd059988defa7cf5f74a19ac6d903c18f Sergiy Palamar <<EMAIL>> 1747659990 +0300	checkout: moving from master to sergiy/pip-ci-fix2
e11f9bbfd059988defa7cf5f74a19ac6d903c18f e5ab8ff4c36950169a1b3947fa846cbdcf663ca8 Sergiy Palamar <<EMAIL>> 1747659999 +0300	commit: PIP CI fix
e5ab8ff4c36950169a1b3947fa846cbdcf663ca8 e11f9bbfd059988defa7cf5f74a19ac6d903c18f Sergiy Palamar <<EMAIL>> 1747660338 +0300	checkout: moving from sergiy/pip-ci-fix2 to master
e11f9bbfd059988defa7cf5f74a19ac6d903c18f ec044fbba59cf9f13b310b185ad8ee6be2836f9f Sergiy Palamar <<EMAIL>> 1747660343 +0300	merge origin/master: Fast-forward
ec044fbba59cf9f13b310b185ad8ee6be2836f9f ec044fbba59cf9f13b310b185ad8ee6be2836f9f Sergiy Palamar <<EMAIL>> 1747660355 +0300	checkout: moving from master to sergiy/ci-changes
ec044fbba59cf9f13b310b185ad8ee6be2836f9f 0aa66d757aa1badbeb7867c85060714ec5f74968 Sergiy Palamar <<EMAIL>> 1747660383 +0300	commit: Redundant npm install-deps removed
0aa66d757aa1badbeb7867c85060714ec5f74968 ec044fbba59cf9f13b310b185ad8ee6be2836f9f Sergiy Palamar <<EMAIL>> 1747665205 +0300	checkout: moving from sergiy/ci-changes to master
ec044fbba59cf9f13b310b185ad8ee6be2836f9f 331e91d3b1603973653d9ca137095cbd649d3c08 Sergiy Palamar <<EMAIL>> 1747665233 +0300	merge origin/master: Fast-forward
331e91d3b1603973653d9ca137095cbd649d3c08 331e91d3b1603973653d9ca137095cbd649d3c08 Sergiy Palamar <<EMAIL>> 1747665295 +0300	checkout: moving from master to sergiy/ci-debug
331e91d3b1603973653d9ca137095cbd649d3c08 ea950fcd94df8041e6d380da962b72f28d14f90a Sergiy Palamar <<EMAIL>> 1747665299 +0300	commit: CI debug
ea950fcd94df8041e6d380da962b72f28d14f90a 9ffc47d21591d87c257f1b116e31d152277629cc Sergiy Palamar <<EMAIL>> 1747665609 +0300	commit: CI debug fix
9ffc47d21591d87c257f1b116e31d152277629cc 50cd659d1077121d073103875fc2bacabcdf30b3 Sergiy Palamar <<EMAIL>> 1747665801 +0300	commit (merge): Merge branch 'master' into sergiy/ci-debug
50cd659d1077121d073103875fc2bacabcdf30b3 b8faa876953db154942d5298c1119e0d09580e79 Sergiy Palamar <<EMAIL>> 1747667767 +0300	merge master: Merge made by the 'ort' strategy.
b8faa876953db154942d5298c1119e0d09580e79 83625a0b9b0d9574fedba1a55b0ffbf6fe52cef2 Sergiy Palamar <<EMAIL>> 1747667787 +0300	commit: CI debug fix
83625a0b9b0d9574fedba1a55b0ffbf6fe52cef2 de7f6b22b8ed19868327aa6b02a15bf4c339b7cd Sergiy Palamar <<EMAIL>> 1747668955 +0300	commit: CI submodules debug
de7f6b22b8ed19868327aa6b02a15bf4c339b7cd 7d1ccb80f61ccbb6b0d32f5e2a8a705387fa9677 Sergiy Palamar <<EMAIL>> 1747668973 +0300	checkout: moving from sergiy/ci-debug to master
7d1ccb80f61ccbb6b0d32f5e2a8a705387fa9677 417681162ae517377eae52a40e9bab8a8bc7b67d Sergiy Palamar <<EMAIL>> 1747668978 +0300	merge origin/master: Fast-forward
417681162ae517377eae52a40e9bab8a8bc7b67d 417681162ae517377eae52a40e9bab8a8bc7b67d Sergiy Palamar <<EMAIL>> 1747670521 +0300	checkout: moving from master to sergiy/deinit-submodules
417681162ae517377eae52a40e9bab8a8bc7b67d a5e1b24f16ba38649101d44f79ccd1f3b80f5b4a Sergiy Palamar <<EMAIL>> 1747670599 +0300	commit: Submodules deinit
a5e1b24f16ba38649101d44f79ccd1f3b80f5b4a d826d4439f583c4a259e9f6639bc02ec3af88fe3 Sergiy Palamar <<EMAIL>> 1747727722 +0300	commit: CI debug data removed
d826d4439f583c4a259e9f6639bc02ec3af88fe3 440256db46a1a593a18008b5d91207332c0380bb Sergiy Palamar <<EMAIL>> 1747727754 +0300	commit (merge): Merge branch 'master' into sergiy/deinit-submodules
440256db46a1a593a18008b5d91207332c0380bb 556442d96c119b304b403faf06762b2a228d0cde Sergiy Palamar <<EMAIL>> 1748424135 +0300	checkout: moving from sergiy/deinit-submodules to master
556442d96c119b304b403faf06762b2a228d0cde 55a4b31dfb955522a2f5564c5031f3bcefa93cc3 Sergiy Palamar <<EMAIL>> 1748424141 +0300	merge origin/master: Fast-forward
55a4b31dfb955522a2f5564c5031f3bcefa93cc3 55a4b31dfb955522a2f5564c5031f3bcefa93cc3 Sergiy Palamar <<EMAIL>> 1748424155 +0300	checkout: moving from master to sergiy/latex
55a4b31dfb955522a2f5564c5031f3bcefa93cc3 cf6b5f1cdc40ca51ec4a965dfba2b9d034675984 Sergiy Palamar <<EMAIL>> 1748424710 +0300	commit: MathJax (LaTeX) support added
cf6b5f1cdc40ca51ec4a965dfba2b9d034675984 55a4b31dfb955522a2f5564c5031f3bcefa93cc3 Sergiy Palamar <<EMAIL>> 1748435718 +0300	checkout: moving from sergiy/latex to master
55a4b31dfb955522a2f5564c5031f3bcefa93cc3 55a4b31dfb955522a2f5564c5031f3bcefa93cc3 Sergiy Palamar <<EMAIL>> 1748435736 +0300	checkout: moving from master to sergiy/favicon-fix
55a4b31dfb955522a2f5564c5031f3bcefa93cc3 26fd9c544ee90d05b78b76a67925d608697c4ee0 Sergiy Palamar <<EMAIL>> 1748435750 +0300	commit: Favicon fix
26fd9c544ee90d05b78b76a67925d608697c4ee0 7078b250c5749b52b74aa19d571fe5ca3fea6a7e Sergiy Palamar <<EMAIL>> 1748435764 +0300	merge origin/master: Merge made by the 'ort' strategy.
7078b250c5749b52b74aa19d571fe5ca3fea6a7e 55a4b31dfb955522a2f5564c5031f3bcefa93cc3 Sergiy Palamar <<EMAIL>> 1748521658 +0300	checkout: moving from sergiy/favicon-fix to master
55a4b31dfb955522a2f5564c5031f3bcefa93cc3 8b5e1a84c708541ce5cfcdfa0a20d195fa1c4519 Sergiy Palamar <<EMAIL>> 1748521663 +0300	merge origin/master: Fast-forward
8b5e1a84c708541ce5cfcdfa0a20d195fa1c4519 f7d3725eb721e1a6e1e98c4d7564194a3be157cf Sergiy Palamar <<EMAIL>> 1748521672 +0300	checkout: moving from master to variables-update
f7d3725eb721e1a6e1e98c4d7564194a3be157cf f7d3725eb721e1a6e1e98c4d7564194a3be157cf Sergiy Palamar <<EMAIL>> 1748521702 +0300	reset: moving to HEAD
