from pygments.lexer import RegexLexer
from pygments.token import Keyword, Name, String, Number, Operator, Punctuation, Text

__all__ = ("DataPrimeLexer")

class DataPrimeLexer(RegexLexer):
    name = 'DataPrime'
    aliases = ['dataprime']
    filenames = ['*.dp']

    tokens = {
        'root': [
            # Registered keywords like source and variable-like terms like logs or spans
            (r'\bsource\b', Keyword),  # Registered keyword
            (r'\b(?:logs|spans)\b', Name.Namespace),  # Variable data sources
            
            # Commands and Operators (distinct highlighting for commands)
            (r'\b(?:countby|extract|stats|table|rename|filter|dedup|sort|top|bottom|join|mvexpand|fields|remove|fillnull|replace|limit|orderby|distinct|enrich|aggregate|explode|append|choose|convert|timeround|groupby|text|redact|lucene|find|wildfind)\b', Keyword),
            
            # Boolean keywords
            (r'\b(?:AND|OR|NOT)\b', Keyword.Reserved),

            # Pipe operator
            (r'\|', Operator),

            # Comparison and Arithmetic Operators
            (r'[<>]=?|!=|=|\+', Operator),
            (r'[*/%-]', Operator),

            # Functions and Type Casts
            (r'\b(?:kv|coalesce|if|case|round|floor|ceil|log|abs|len|upper|lower|ipprefix|isuuid|formatTimestamp|parseInterval)\b', Name.Function),  # Recognize kv() as a function
            (r':\b(?:string|number|boolean|interval|timestamp)\b', Name.Class),  # Unique color for type casts like :string

            # Enum constants for severity levels
            (r'\b(?:CRITICAL|ERROR|WARNING|VERBOSE|INFO|DEBUG)\b', Name.Constant),  # Severity enums styled as constants

            # Keypath Variables
            (r'\$[a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*', Name.Variable),  # $d.Category, $m.applicationname

            # Numbers (integer and float)
            (r'\b\d+(\.\d+)?\b', Number),
            
            # String literals and interpolated strings
            (r'"(?:\\.|[^\\"])*"', String),
            (r"'(?:\\.|[^\\'])*'", String),
            (r'`([^\\`{}\n]|\\.)*`', String.Interpol),
            
            # Punctuation
            (r'[\(\),\[\]]', Punctuation),

            # Regex literals
            (r'/([^\\/\n]|\\.)+/', String.Regex),
            
            # Whitespace
            (r'\s+', Text)
        ]
    }

# Example usage:
# from pygments import highlight
# from pygments.formatters import HtmlFormatter
# code = 'source logs | filter $m.severity >= CRITICAL | filter $l.subsystemname == "archive"'
# print(highlight(code, DataPrimeLexer(), HtmlFormatter()))
