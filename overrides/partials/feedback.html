{% if config.extra.analytics %}
  {% set feedback = config.extra.analytics.feedback %}
{% endif %}

{% if page.meta and page.meta.hide %}
  {% if "feedback" in page.meta.hide %}
    {% set feedback = None %}
  {% endif %}
{% endif %}

{% if feedback %}
  <form class="md-feedback" name="feedback">
    <fieldset>
      <div class="md-feedback__title">
        {{ feedback.title }}
      </div>

      <div class="md-feedback__inner">
        <div class="md-feedback__list">
          {% for rating in feedback.ratings %}
            <button class="md-feedback__button" type="submit" title="{{ rating.name }}" data-md-value="{{ rating.data }}">
              <img src="{{ config.site_url + rating.icon }}" alt="{{ rating.name }}">
              {{ rating.name }}
            </button>
          {% endfor %}

          <div class="feedback-note-form" hidden>
            <textarea placeholder="What did you like?"></textarea>

            <div class="md-typeset feedback-note-form-footer">
              <button class="md-button md-button--secondary note-form-cancel-button extra-small">Skip</button>
              <button class="md-button md-button--primary note-form-submit-button extra-small">Send</button>
            </div>
          </div>
        </div>

        <div class="md-feedback__note">
          {% for rating in feedback.ratings %}
            <div data-md-value="{{ rating.data }}" hidden>
              {% set url = "/" ~ page.url %}

              {% if page.meta and page.meta.title %}
                {% set title = page.meta.title | urlencode %}
              {% else %}

              {% set title = page.title | urlencode %}
                {% endif %}
              {{ rating.note.format(url = url, title = title) }}
            </div>
          {% endfor %}
        </div>
      </div>

      <div class="md-typeset write-feedback" hidden>
        <button class="md-button extra-small link write-feedback-button">
          Write a feedback
        </button>
      </div>
    </fieldset>
  </form>
{% endif %}