{% set toc = page.toc %}

{% if toc %}
  <nav class="md-nav md-nav--secondary toc-main" aria-label="{{ title }}">
    {% if page.meta.show_blocks and 'global_category_selector' in page.meta.show_blocks %}
      <div class="global-category-selector-wrapper">
        {% include "partials/global-category-selector.html" %}
      </div>
    {% endif %}

    {% if page.meta.show_blocks and 'domain_selector' in page.meta.show_blocks %}
      {% include "partials/domain-selector.html" %}
    {% endif %}

    {% set first = toc | first %}
    {% if first and first.level == 1 %}
      {% set toc = first.children %}
    {% endif %}

    <div class="md-nav-inner">
      <label class="md-nav__title" for="__toc">
        <span class="md-nav__icon md-icon"></span>
        On this page
      </label>

      <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
        {% for toc_item in toc %}
          {% include "partials/toc-item.html" %}
        {% endfor %}
      </ul>
    </div>

    {% include "partials/feedback.html" %}

    <div class="md-typeset">
      <a
        href="{{ config.extra.main_site_signup_url }}"
        class="md-button md-button--primary small"
        data-start-trial-button
      >
        Start Free Trial
      </a>
    </div>
  </nav>
{% endif %}