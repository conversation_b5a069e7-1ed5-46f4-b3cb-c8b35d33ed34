<div class="md-nav--primary nav-home">
  {% include "partials/theme-switcher-mobile.html" %}

  <a class="nav-home-page-link" href="{{ config.site_url }}">Home</a>

  <div class="nav-home-sections-list">
    {% for nav_item in nav %}
      {% if nav_item.title != 'Home' %}
        {% set index_page = nav_item.children | first %}
        <div class="nav-home-sections-list-item" data-nav-target="#__nav_{{ loop.index }}">
          {% if index_page.meta %}
            <img
              class="header-nav-item-icon"
              src="{{ index_page.meta.icon_url }}"
              alt="{{ nav_item.title }}"
            >
          {% endif %}
          {{ nav_item.title }}
        </div>
      {% endif %}
    {% endfor %}
  </div>
</div>
