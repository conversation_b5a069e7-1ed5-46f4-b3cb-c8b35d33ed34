{% import "partials/breadcrumbs-item.html" as item with context %}

{% if page.meta and page.meta.hide %}
  {% set hidden = "hidden" if "path" in page.meta.hide %}
{% endif %}

{% macro render_excluded_child(nav_item) %}
  {% if nav_item.title in page.meta.parent_sections and nav_item.children %}
    {% for nav_item_inner in nav_item.children %}
      {% if nav_item_inner.title in page.meta.parent_sections %}
        {{ item.render(nav_item_inner) }}
        <span class="breadcrumb-separator">/</span>
      {% endif %}

      {{ render_excluded_child(nav_item_inner) }}
    {% endfor %}
  {% endif %}
{% endmacro %}

<nav class="breadcrumbs" aria-label="{{ lang.t('nav') }}" {{ hidden }}>
  <span class="breadcrumb-item home">
    <a href="{{ nav.homepage.url|url }}">Home</a>
  </span>

  <span class="breadcrumb-separator">/</span>

  {% if page.meta and page.meta.parent_sections %}
    {% for nav_item in nav %}
      {{ render_excluded_child(nav_item) }}
    {% endfor %}
  {% endif %}

  {% for nav_item in page.ancestors | reverse %}
    {{ item.render(nav_item) }}
    <span class="breadcrumb-separator">/</span>
  {% endfor %}

  <span class="breadcrumb-item active">{{ page.title }}</span>
</nav>
