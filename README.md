# Coralogix Open Source Integrations\n\nCoralogix Open Source Integrations repository is Coralogix's way to ship our best practices when it comes to interaction with our platform, as well as collaborating with our users.\nCurrently we support:\n\nLogging integrations, [Fluentd](https://www.fluentd.org/) and [Fluentbit](https://fluentbit.io/),\n\nMetrics integrations, [Prometheus](https://prometheus.io/),\n\nTracing integrations, [OpenTelemetry](https://opentelemetry.io/).\n\nPlease see [#Getting Started](README.md#getting-started) for more information about the existing integrations.\n\n## Getting Started\n\nThis repository contains directories for each integration type, logs and metrics, and open-telemetry that can send all.\n\nInside each integration type there are multiple ways to install our integrations, using helm, installing kubernetes manifests directly, using a docker image or installing a service.\n\n## Helm/Kubernetes integrations prerequisite\n\nAll K8s integrations, both helm and manifests, require a `secret` called `coralogix-keys` with the relevant `private key` under a secret key called `PRIVATE_KEY`,\ninside the `same namespace` that the integration is installed in.\n\n* The `private key` appears under 'Data Flow' --> 'API Keys' in Coralogix UI:\n\n```bash\nkubectl create secret generic coralogix-keys \\n  -n <the-namespace-of-the-integrations> \\n  --from-literal=PRIVATE_KEY=<private-key>\n```\n\nfor more information regarding the coralogix private key please visit [here](https://coralogix.com/docs/private-key/)\n\nThe created secret should look like this:\n\n```yaml\napiVersion: v1\ndata:\n  PRIVATE_KEY: <encrypted-private-key>\nkind: Secret\nmetadata:\n  name: coralogix-keys\n  namespace: <the-integration-namespace>\ntype: Opaque \n```\n\n## Installation\n\n### Helm\n\nIn the 'logs' integrations inside the 'k8s-helm' there are two supported `helm charts`, one using the `Coralogix` output plugin,\nand another one using the `http` output plugin.\nWe recommend using the `http` chart, since it's an open source plugin, and therefore it is more community driven.\nIMPORTANT:\n`Coralogix` output plugin version is fixed in Fluent-Bit image 3.2.4. The Plugin will not be supported in supirior versions.\n\nUnder each integration there is an 'image' directory which our GitHub Actions workflows use in order to build the image and publish it to DockerHub.\n\nOur Helm charts repository can be added to the local repos list with the following command. It will create a repository named `coralogix-charts-virtual`. If you wish to change it to anything else, be sure to adapt your commands in the other segments referring to this repository.\n\n```bash\nhelm repo add coralogix-charts-virtual https://cgx.jfrog.io/artifactory/coralogix-charts-virtual\n```\n\nIn order to get the updated helm charts from the added repository, please run:\n\n```bash\nhelm repo update\n```\n\nFor installation of each integration, please go inside each intergation's directory:\n- [Fluentd-HTTP chart](https://github.com/coralogix/telemetry-shippers/blob/master/logs/fluentd/k8s-helm/http/README.md)\n- [Fluent-bit-HTTP chart](https://github.com/coralogix/telemetry-shippers/blob/master/logs/fluent-bit/k8s-helm/http/README.md)\n- [Prometheus operator chart](https://github.com/coralogix/telemetry-shippers/blob/master/metrics/prometheus/operator/README.md)\n- [OpenTelementry-Agent chart](https://github.com/coralogix/telemetry-shippers/blob/master/otel-agent/README.md)\n\n### Kubernetes\n\nOur k8s manifests integration allow you to install without the use of Helm, specifically for those times were using helm is impossible.\n\nFor installation of each integration, please go inside each intergation's directory:\n- [Fluentd-HTTP](https://github.com/coralogix/telemetry-shippers/blob/master/logs/fluentd/k8s-manifest/README.md)\n- [Fluent-bit-HTTP](https://github.com/coralogix/telemetry-shippers/blob/master/logs/fluent-bit/k8s-manifest/README.md)\n\n### These integrations were checked on Kubernetes 1.20+.