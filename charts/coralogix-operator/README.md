# coralogix-operator\n\n![Version: 0.5.0](https://img.shields.io/badge/Version-0.5.0-informational?style=flat-square) ![Type: application](https://img.shields.io/badge/Type-application-informational?style=flat-square) ![AppVersion: 0.5.0](https://img.shields.io/badge/AppVersion-0.5.0-informational?style=flat-square)\n\nCoralogix Operator Helm Chart\n\n**Homepage:** <https://github.com/coralogix/coralogix-operator>\n\n## <AUTHOR> <EMAIL> |  |\n\n## Source Code\n\n* <https://github.com/coralogix/coralogix-operator>\n\n## Requirements\n\nKubernetes: `>=1.16.0-0`\n\n## Values\n\n| Key | Type | Default | Description |\n|-----|------|---------|-------------|\n| additionalLabels | object | `{}` | Custom labels to add into metadata |\n| affinity | object | `{}` | ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/ |\n| coralogixOperator | object | `{"domain":"","image":{"pullPolicy":"IfNotPresent","repository":"coralogixrepo/coralogix-operator","tag":""},"labelSelector":{},"leaderElection":{"enabled":true},"namespaceSelector":{},"prometheusRules":{"enabled":true},"reconcileIntervalSeconds":{"alert":"","alertScheduler":"","apiKey":"","customRole":"","dashboard":"","dashboardsFolder":"","group":"","integration":"","outboundWebhook":"","prometheusRule":"","recordingRuleGroupSet":"","ruleGroup":"","scope":"","tcoLogsPolicies":"","tcoTracesPolicies":"","view":"","viewFolder":""},"region":"","resources":{},"securityContext":{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"readOnlyRootFilesystem":true}}` | Coralogix operator container config |\n| coralogixOperator.domain | string | `""` | Coralogix Account Domain |\n| coralogixOperator.image | object | `{"pullPolicy":"IfNotPresent","repository":"coralogixrepo/coralogix-operator","tag":""}` | Coralogix operator Image |\n| coralogixOperator.labelSelector | object | `{}` | A selector to filter custom resources (by the custom resources' labels). {} matches all custom resources. Cannot be set to nil. |\n| coralogixOperator.leaderElection | object | `{"enabled":true}` | Enable leader election for controller manager. Enabling this will ensure there is only one active controller manager. |\n| coralogixOperator.namespaceSelector | object | `{}` | A selector to filter namespaces (by the namespace's labels). {} matches all namespaces. Cannot be set to nil. |\n| coralogixOperator.reconcileIntervalSeconds | object | `{"alert":"","alertScheduler":"","apiKey":"","customRole":"","dashboard":"","dashboardsFolder":"","group":"","integration":"","outboundWebhook":"","prometheusRule":"","recordingRuleGroupSet":"","ruleGroup":"","scope":"","tcoLogsPolicies":"","tcoTracesPolicies":"","view":"","viewFolder":""}` | The interval in seconds to reconcile each custom resource |\n| coralogixOperator.region | string | `""` | Coralogix Account Region |\n| coralogixOperator.resources | object | `{}` | resource config for Coralogix operator |\n| coralogixOperator.securityContext | object | `{"allowPrivilegeEscalation":false,"capabilities":{"drop":["ALL"]},"readOnlyRootFilesystem":true}` | Security context for Coralogix operator container |\n| deployment.podLabels | object | `{}` | Pod labels for Coralogix operator |\n| deployment.replicas | int | `1` | How many coralogix-operator pods to run |\n| fullnameOverride | string | `""` | Provide a name to substitute for the full names of resources |\n| imagePullSecrets | list | `[]` |  |\n| nameOverride | string | `""` | Provide a name in place of coralogix-operator for `app:` labels |\n| nodeSelector | object | `{}` | ref: https://kubernetes.io/docs/user-guide/node-selection/ |\n| podAnnotations | object | `{}` | Annotations to add to the operator pod |\n| secret | object | `{"annotations":{},"create":true,"data":{"apiKey":""},"labels":{},"secretKeyReference":{}}` | Configuration for Coralogix operator secret |\n| secret.annotations | object | `{}` | Annotations to add to the Coralogix operator secret |\n| secret.create | bool | `true` | Indicates if the Coralogix operator secret should be created |\n| secret.data | object | `{"apiKey":""}` | Coralogix operator secret data |\n| secret.labels | object | `{}` | Labels to add to the Coralogix operator secret |\n| secret.secretKeyReference | object | `{}` | secret.data and secret.secretKeyReference should be mutually exclusive. |\n| securityContext | object | `{"fsGroup":2000,"runAsGroup":2000,"runAsNonRoot":true,"runAsUser":2000,"seccompProfile":{"type":"RuntimeDefault"}}` | ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/ |\n| serviceAccount | object | `{"annotations":{},"create":true,"name":""}` | ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/ |\n| serviceAccount.annotations | object | `{}` | Annotations to add to the service account |\n| serviceAccount.create | bool | `true` | Specifies whether a service account should be created |\n| serviceAccount.name | string | `""` | If not set and create is true, a name is generated using the fullname template |\n| serviceMonitor | object | `{"create":true,"labels":{}}` | Service monitor for Prometheus to use. |\n| serviceMonitor.create | bool | `true` | Specifies whether a service monitor should be created. |\n| serviceMonitor.labels | object | `{}` | Additional labels to add for ServiceMonitor  |\n| tolerations | list | `[]` | ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/ |\n