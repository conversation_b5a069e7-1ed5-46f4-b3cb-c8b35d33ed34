# CXO Observer\n\n## Overview\n`cxo-observer` is a CLI tool that collects Kubernetes resources related to the Coralogix Operator installation, including both core operator components and custom resources (CRs) managed by the operator.\nAdditionally, it retrieves logs from the operator’s pods to provide comprehensive visibility.\nIt is useful for support, debugging, and exporting the current state of your Coralogix Operator installation.\nThe output is compressed into a `.tar.gz` file containing files organized by resource group, version, kind, and namespace, to be easily inspected and shared.\nSupport requests and issues reports should include the output of this tool, including the affected resources.\n\n### Features\n- Collects Kubernetes resources created by the Coralogix Helm chart (e.g. Deployment, CRDs, ServiceAccount).\n- Collects Coralogix custom resources across the entire cluster by default, with optional filtering by namespace and label selectors.\n- Collects logs from the Coralogix Operator pods.\n\n### Controlling Log Verbosity Per Resource\nTo debug a specific custom resource without flooding logs with unrelated information, you can increase its log verbosity by adding the following annotation directly to the resource:\n\n```yaml\n  annotations:\n    app.coralogix.com/log-verbosity: "0"\n```\n\nThis annotation instructs the Coralogix Operator to log all activity related to the specific resource—including detailed API requests made to the Coralogix backend.\n\n\n## Installation\n### Prerequisites\n\n- [Go](https://golang.org/doc/install) 1.16 or later\n\n```bash\ngo install github.com/coralogix/coralogix-operator/tools/cxo-observer@<your-operator-version>\n```\n\n## Usage\n\n```bash\ncxo-observer [flags]\n```\n\nExample:\n```bash\ncxo-observer --chart-namespace=observability --namespace-selector=production,staging --label-selector=team=backend,app=api\n```\n\nIf no `--namespace-selector` nor `--label-selector` is provided, all custom resources across the entire cluster will be collected.\n\n### Flags\n```bash\n$ cxo-observer -h\nUsage of cxo-observer:\n  -chart-name string\n        The name of Coralogix Operator Helm chart release. (default "coralogix-operator")\n  -chart-namespace string\n        The namespace of Coralogix Operator Helm chart release.\n  -kubeconfig string\n        Paths to a kubeconfig. Only required if out-of-cluster.\n  -label-selector string\n        A comma-separated list of key=value labels to filter custom resources.\n  -namespace-selector string\n        A comma-separated list of namespaces to filter custom resources.\n  -zap-devel\n        Development Mode defaults(encoder=consoleEncoder,logLevel=Debug,stackTraceLevel=Warn). Production Mode defaults(encoder=jsonEncoder,logLevel=Info,stackTraceLevel=Error)\n  -zap-encoder value\n        Zap log encoding (one of 'json' or 'console')\n  -zap-log-level value\n        Zap Level to configure the verbosity of logging. Can be one of 'debug', 'info', 'error', or any integer value > 0 which corresponds to custom debug levels of increasing verbosity\n  -zap-stacktrace-level value\n        Zap Level at and above which stacktraces are captured (one of 'info', 'error', 'panic').\n  -zap-time-encoding value\n        Zap time encoding (one of 'epoch', 'millis', 'nano', 'iso8601', 'rfc3339' or 'rfc3339nano'). Defaults to 'epoch'.\n```\n\n### Output Structure\n```text\noutput/\n├── operator-resources/\n│   ├── crds/\n│   │   ├── <crd-name>.yaml\n│   │   └── ...\n│   ├── deployment.yaml\n│   ├── service.yaml   \n│   └── ...\n├── custom-resources/\n│   ├── <namespace>/\n│   │   └── <group>/<version>/<kind>/<name>.yaml\n│── logs/\n│   ├── <operator-pod-name>.log\n```