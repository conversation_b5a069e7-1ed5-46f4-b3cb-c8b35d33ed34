---
title: "Alibaba Cloud ActionTrail"
date: "2024-02-04"
description: "The integration of Coralogix with ActionTrail on Alibaba Cloud offers a powerful solution for tracking and analyzing cloud-based actions."
---

# Alibaba Cloud ActionTrail

The integration of Coralogix with ActionTrail on Alibaba Cloud offers a powerful solution for tracking and analyzing cloud-based actions. By integrating Coralogix with ActionTrail, users can gain valuable insights into the operational activities within their Alibaba Cloud resources. This integration provides a comprehensive view of resource usage, improving efficiency and simplifying issue troubleshooting. Additionally, it enhances security by enabling real-time monitoring and alerting for potential security threats. 

## Prerequisites

- Permission to create a custom webhook URL with your Coralogix account.
- Permission to access EventBridge and IAM on Alibaba Cloud.

## Creating a custom Coralogix webhook URL

1. Access the Coralogix UI and navigate to **Data Flow** > **Contextual Data**.
2. [Create a custom webhook URL](../../incoming-webhooks/generic-incoming-webhooks/index.md) for connecting your ActionTrail to Coralogix.

## Creating a custom event bus on ActionTrail

You must create a custom event bus for forwarding your logs to Coralogix.

### Locating the project and Logstore names for ActionTrail

1. Log into Alibaba Cloud.
2. Navigate to **ActionTrail > Trails** and select the trail you want to integrate with Coralogix. Then, click the **Event Delivery** tab.

   ![Untitled](images/image-1.png)

### Creating a custom event bus

1. Navigate to **EventBridge > Event Buses**.

2. In the **Custom Event Buses** section, click **Quickly Create**.

     ![Untitled](images/image-2.png)

1. Add the bus name and description. Then, click **Next Step**.

     ![Untitled](images/image-3.png)

1. Provide a name to the event source and define the event provider as `Simple Log Service`. Then, click **Next Step**.

     ![Untitled](images/image-4.png)

1. Choose the project name and the Logstore. They must be same as defined in the [Locating the project and Logstore names for ActionTrail](#locating-the-project-and-logstore-names-for-actiontrail) section. Click **Authorization** to create a role for the bus. Then, click **Next Step**.

     ![Untitled](images/image-5.png)

1. Provide the event rule name, and keep the pattern content unchanged, `{}` to accept all Trail logs. Then, click **Next Step**.

     ![Untitled](images/image-6.png)

1. Set the target service type to `HTTPS`.

    ![Untitled](images/image-7.png)

1. In the **URL** field, enter the custom webhook URL, as defined in the [Creating a custom Coralogix webhook URL](#creating-a-custom-coralogix-webhook-url) section above. Set the body to `Partial Event` and enter `$.data.event` in the **Body** field.
2. Click **Create** to finish.

   ![Screenshot at Feb 05 11-25-35.png](images/image-8.png)