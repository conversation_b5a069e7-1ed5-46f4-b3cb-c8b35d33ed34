---
title: "Bitdefender"
date: "2024-02-04"
description: "Iitdefender is a leading cybersecurity company that offers a comprehensive suite of security products and services. Integrate Coralogix with Bitdefenders’ GravityZone service for comprehensive event streaming functionality. "
---

# Bitdefender

Bitdefender is a leading cybersecurity company that offers a comprehensive suite of security products and services. Their offerings include antivirus software, internet security solutions, and endpoint protection. Utilizing advanced machine learning algorithms and cutting-edge technologies, Bitdefender effectively detects and prevents online threats such as viruses, malware, spyware, and phishing attacks. Integrate Coralogix with Bitdefenders’ GravityZone service for comprehensive event streaming functionality. 

## Prerequisites

- Permission to create a custom webhook URL with Coralogix account.
- Bitdefender GravityZone (cloud) account with admin access privileges (for obtaining the API key.)
- A Mac or Linux terminal for running Curl commands.

## Creating a custom Coralogix webhook URL

1. Access the Coralogix UI and navigate to **Data Flow** > **Contextual Data**.
2. [Create a custom webhook URL](../../incoming-webhooks/generic-incoming-webhooks/index.md) for sending GravityZone Control Center events to Coralogix.

## **Generating Bitdefender GravityZone API key**

1. Log in to GravityZone Control Center.
2. Click the username at the upper right-hand corner and choose **My Account**.
3. Go to the **API keys** section and click **Add** in the table.
4. Enable **Event Push Service API**.
    
    !!! note

        You can enable other APIs to source more information from the Bitdefender GravityZone.
    
5. Click **Save**.
    
    !!! note

        To prevent the leaking of sensitive information, do not share or distribute your own generated API keys.

    
6. Copy the Access URL from the **Control Center API** section. This key is required for configuring the Event Push Service API.

## **Configuring the Event Push Service API**

Follow this procedure to set up the subscription for the GravityZone Control Center events that you want to view in Coralogix.

1. Open a Mac or Linux terminal.
2.  Run the echo command followed by the Bitdefender GravityZone API key with a colon (`:`) to encode an API key in a base64 string. Use this encoded string as a token for POST authorization.
    - Linux:

        ```ruby
        > echo –n '<API key' | base64 –w 0
        ```

    - MAC:

        ```ruby
        > echo –n 'API key' | base64 –b 0
        ```

1. Run the following Curl commands and edit the settings in bold:

    ``` 
    curl -k -X POST \
    **https://cloudgz.gravityzone.bitdefender.com/api**/v1.0/jsonrpc/push \
    -H 'authorization: Basic **<base64 encoded key>**' \
    -H 'cache-control: no-cache' \
    -H 'content-type: application/json' \
    -d '{"params": {"status": 1,"serviceType": "jsonRPC","serviceSettings": {"url": "**Coralogix generic webhook URL**","requireValidSslCertificate": false, "authorization":"cx-api-key=**<api key generated with webhook URL>**"},"subscribeToEventTypes": {"modules": true,"sva": true,"registration": true,"supa-update-status": true,"av": true,"aph": true,"fw": true,"avc": true,"uc": true,"dp": true,"hd": true,"sva-load": true,"task-status": true,"exchange-malware": true,"network-sandboxing": true,"adcloud": true,"exchange-user-credentials": true}},"jsonrpc": "2.0","method": "setPushEventSettings","id": "1"}'
    ```

    Return value example: `{"id":"1","jsonrpc":"2.0","result":true}`

1. To start sending events immediately, edit the parameters in bold and run the following command:

    ```
    curl -k -X POST \
    **https://cloudgz.gravityzone.bitdefender.com/api**/v1.0/jsonrpc/push \
    -H 'authorization: Basic **<base64 encoded key>**' \
    -H '**cache**-control: **no**-**cache**' \
    -H '**content**-**type**: application/**json**' \
    -d '{"params": {}, "jsonrpc": "2.0", "method": "getPushEventSettings", "id": "2"}'
    ```

1. To test the integration, edit the parameters in bold and run the following command:

    ```
    curl -k -X POST \
    **https://cloudgz.gravityzone.bitdefender.com/api**/v1.0/jsonrpc/push \
    -H 'authorization: Basic **<base64 encoded key>**' \
    -H '**cache**-control: **no**-**cache**' \
    -H '**content**-**type**: application/**json**' \
    -d '{"params": {"eventType": "av"}, "jsonrpc": "2.0", "method": "sendTestPushEvent", "id": "3"}'
    ```

!!! note

    Copy your GravityZone URL from the My account page of your Bitdefender GravityZone portal.

