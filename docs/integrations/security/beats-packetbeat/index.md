---
title: "Beats: Packetbeat"
date: "2020-04-19"
coverImage: "Beats_Large.png"
---

_Coralogix_ provides a seamless integration with `Packetbeat` so you can send your network usage logs from anywhere and parse them according to your needs.

This document includes cluster dependent URL's. Each URL has a variable part (in Italic). Please match this part with a row entry within the following table. Copy the table row entry located under the column that matches the top level domain of your Coralogix account (.com, .in etc.). Replace the variable part of the URL with this entry.

\[table id=51 /\]

## Prerequisites

- Have `Packetbeat` installed, for more information on how to install: [https://www.elastic.co/guide/en/beats/packetbeat/current/packetbeat-installation.html](https://www.elastic.co/guide/en/beats/packetbeat/current/packetbeat-installation.html)

- Install our SSL certificate to your system for providing a secure connection. You can download it by this link:  
    _SSL Certificates_

## General

**Private Key** – Your [Send Your Data - API Key](../../../user-guides/account-management/api-keys/send-your-data-api-key/index.md) is a unique ID that represents your company.

**Company Id** – A unique number that represents your company. You can get your company id from the settings tab in the _Coralogix_ dashboard.

**Application Name** – The name of your main application, for example, a company named _"SuperData"_ would probably insert the _"SuperData"_ string parameter or if they want to debug their test environment they might insert the _"SuperData– Test"_.

**SubSystem Name** – Your application probably has multiple subsystems, for example: Backend servers, Middleware, Frontend servers etc. in order to help you examine the data you need, inserting the subsystem parameter is vital.

## [](https://github.com/coralogix/integrations-docs/blob/master/integrations/packetbeat/README.rst#configuration)Configuration

Open your `Packetbeat` configuration file and configure it to use `Logstash`. For more information about configuring `Packetbeat` to use `Logstash` please refer to the [documentation](https://www.elastic.co/guide/en/beats/packetbeat/current/config-packetbeat-logstash.html).

Point your `Packetbeat` to output to the _Coralogix_ Logstash server:

```
logstashserver.Cluster URL:5015

In addition, you should add the Coralogix configuration from the General section.
```

Here is a basic example of **packetbeat.yml** for watching HTTP packages:

``` yaml
#========================== Network targets to watch ===========================

packetbeat.interfaces.device: any
packetbeat.protocols:
- type: http
  ports: [80, 8080, 8000, 5000, 8002]
  hide_keywords: ["pass", "password", "passwd"]
  send_headers: ["User-Agent", "Cookie", "Set-Cookie"]
  split_cookie: true
  real_ip_header: "X-Forwarded-For"

fields_under_root: true
fields:
  PRIVATE_KEY: "YOUR_PRIVATE_KEY"
  COMPANY_ID: YOUR_COMPANY_ID
  APP_NAME: "APP_NAME"
  SUB_SYSTEM: "SUB_NAME"

#----------------------------- Logstash output --------------------------------

output.logstash:
    enabled: true
hosts: ["logstashserver.Cluster URL:5015"] 
ttl: 60s
ssl.certificate_authorities: ["<path to folder with certificates>\\ca.crt"]
```
