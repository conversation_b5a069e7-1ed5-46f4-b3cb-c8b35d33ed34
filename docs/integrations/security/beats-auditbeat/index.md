---
title: "Beats: Auditbeat"
date: "2019-03-31"
coverImage: "Beats_Large.png"
---

Coralogix provides seamless integration with `Auditbeat` so you can send your audit data from anywhere into Coralogix.

## [](https://github.com/coralogix/integrations-docs/blob/master/integrations/auditbeat/README.rst#prerequisites)Prerequisites

- Select the URLs in the table associated with your Coralogix [domain](../../../user-guides/account-management/account-settings/coralogix-domain/index.md).

\[table id=51 /\]

- [Install](https://www.elastic.co/guide/en/beats/auditbeat/current/auditbeat-installation.html) `Auditbeat`.

- Install our SSL certificate to your system for providing a secure connection. You can download it by following table in this section (Column SSL Certificate).

## [](https://github.com/coralogix/integrations-docs/blob/master/integrations/auditbeat/README.rst#general)General

**Private Key** – Your [Send Your Data - API Key](../../../user-guides/account-management/api-keys/send-your-data-api-key/index.md) is a unique ID that represents your company.

**Company Id** – A unique number which represents your company. You can get your company id from the settings tab in the _Coralogix_ dashboard.

**Application Name** – The name of your main application, for example, a company named _"SuperData"_ would probably insert the _"SuperData"_ string parameter or if they want to debug their test environment they might insert the _"SuperData– Test"_.

**SubSystem Name** – Your application probably has multiple subsystems, for example: Backend servers, Middleware, Frontend servers etc. in order to help you examine the data you need, inserting the subsystem parameter is vital.

## [](https://github.com/coralogix/integrations-docs/blob/master/integrations/auditbeat/README.rst#configuration)Configuration

### [](https://github.com/coralogix/integrations-docs/blob/master/integrations/auditbeat/README.rst#on-host-machine)At the Host machine

Open your `Auditbeat` configuration file and configure it to use `Logstash`. For more information about configuring `Auditbeat` to use `Logstash` please refer to: [https://www.elastic.co/guide/en/beats/auditbeat/current/logstash-output.html](https://www.elastic.co/guide/en/beats/auditbeat/current/logstash-output.html)

Point your `Auditbeat` to output to _Coralogix_ logstash server:

If your r Coralogix [domain](../../../user-guides/account-management/account-settings/coralogix-domain/index.md) ends with '.com' use:

```
logstashserver.coralogix.com:5015
```

If your r Coralogix [domain](../../../user-guides/account-management/account-settings/coralogix-domain/index.md) ends with '.in' use:

```
logstashserver.app.coralogix.in:5015
```

In addition, you should add _Coralogix_ configuration from the **General** section.

Here is a basic example of an **auditbeat.yml** file for watching some folders on your server:

``` yaml
#============================= Auditbeat Modules ===============================

auditbeat.modules:
- module: file_integrity
  enabled: true
  paths:
  - /bin
  - /usr/bin
  - /sbin
  - /usr/sbin
  - /etc

fields_under_root: true
fields:
  PRIVATE_KEY: "YOUR_PRIVATE_KEY"
  COMPANY_ID: YOUR_COMPANY_ID
  APP_NAME: "APP_NAME"
  SUB_SYSTEM: "SUB_NAME"

#----------------------------- Logstash output --------------------------------

output.logstash:
  enabled: true
# If your Coralogix domain ends with '.com' use logstashserver.coralogix.com:5015
# If your Coralogix domain ends with '.in' use logstashserver.app.coralogix.in:5015
  hosts: ["appropriate-log-stash-server"]
  ttl: 60s
  ssl.certificate_authorities: ["<path to folder with certificates>/ca.crt"]
```

### [](https://github.com/coralogix/integrations-docs/blob/master/integrations/auditbeat/README.rst#with-docker)Using Docker

Build a Docker image with your **auditbeat.yml**:

``` dockerfile
FROM docker.elastic.co/beats/auditbeat:6.6.2

LABEL description="Auditbeat filesystem audit data collector"

# Adding configuration file and SSL certificates for Auditbeat
COPY auditbeat.yml /usr/share/auditbeat/auditbeat.yml
COPY ca.crt /etc/ssl/certs/Coralogix.crt

# Changing permission of configuration file
USER root
RUN chown root:auditbeat /usr/share/auditbeat/auditbeat.yml

# Return to deploy user
USER auditbeat
```

## [](https://github.com/coralogix/integrations-docs/blob/master/integrations/auditbeat/README.rst#usage)Usage

You can deploy with _Docker-compose_:

``` yaml
version: '3.6'
services:
  auditbeat:
    image: docker.elastic.co/beats/auditbeat:6.6.2
    container_name: auditbeat
    volumes:
      - ./auditbeat.yml:/usr/share/auditbeat/auditbeat.yml:ro
      - ./ca.crt:/etc/ssl/certs/Coralogix.crt:ro
```

Important: Don't forget to change the owner of **auditbeat.yml** file to _root_ (uid=1000).
