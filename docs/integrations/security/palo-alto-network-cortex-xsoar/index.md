---
title: "Palo Alto Network’s Cortex XSOAR"
date: "2020-10-27"
coverImage: "Cortex-1000X1000.png"
---

If you ever need to handle security incidents you know how difficult it can be. More often than not, the system that detected the incident lacks the contextual information needed to figure out whether it's a false positive or something that needs to be investigated further. Other systems typically don't contain the full information either about the discovered incident. Also, automation would be of great help to tell the system: "Hey, if you see this particular incident from a similar IP address go to my firewall and block it and then inform me when you're done". This is where Cortex XSOAR comes in.

If these challenges sound familiar to you, then what you need is an orchestration and automation tool like **Palo-Alto's Cortex [XSOAR](https://www.paloaltonetworks.com/cortex/xsoar).** This tool is built for making the life of the security analyst a lot easier. It allows you to configure multiple plugins for interacting with multiple systems both automatically and manually.

Together with the flexibility and the reach of security-related information provided by Coralogix, you can easily analyze security alerts from many different sources, correlate the various Indicators of Compromise (IOCs), form a coherent evidence-based timeline, and react to the incident and even automate the handling of similar incidents. XSOAR provides all of that from a single pane of glass.

## **Benefits**

1. The ability to automatically create an incident in Cortex XSOAR for every alert in Coralogix that was sent to a Demisto Webhook.

3. The ability to search for data that you have in your Coralogix account, both automatically as part of a playbook as well as manually directly from the Cortex XSOAR war room.

5. The ability to tag a timestamp in Coralogix both automatically as part of a playbook as well as manually directly from the Cortex XSOAR war room.

## Use Cases

There are countless use-cases for this integration so we'll just provide you with a few examples to get you started.

1. Use [Coralogix STA](https://coralogix.com/blog/sta-new-features/) to collect and automatically analyze network traffic and have a firewall, like CheckPoint to automatically block access to addresses related to an attack detected by the STA.

3. Investigate a security incident, either detected by Coralogix or by any other installed system, that supposedly involved both internal and external actors and you would like to form a timeline that would include security-related events as well code changes of the company's product to figure out whether there's a correlation between code changes and various security-related activities.

5. Investigate a security incident either detected by Coralogix or by any other installed system and you need some information related to AWS CloudTrail, Coralogix STA, Database logs, Code and configuration changes, servers logs, or any other type of data that is stored on your Coralogix account and you'd like to search for it without leaving the Cortex XSOAR war room.

## Integration

The process for adding the Coralogix integration pack is quite simple and straightforward:

1. Navigate to **Cortex XSOAR Marketplace**.

3. Search for Coralogix.  
    ![](images/Kazam_screenshot_00275-700x613.png)

5. Click on **Install** on the top right corner and then on **Install** at the bottom right corner.  
    ![](images/Kazam_screenshot_00278-700x181.png)

7. Once it is installed, click on **Settings** > **Integrations** and then on **Add instance** on the right-hand side and fill in the following parameters:

| Parameter Name            | Description                       | Required                                                          | Default Value                                                     |
|---------------------------|-----------------------------------|-------------------------------------------------------------------|-------------------------------------------------------------------|
| Name                      | The name of the Coralogix integration instance (can be any name you like)        | Yes                | N/A  |
| Fetches incidents         | Whether or not to fetch incidents via this integration | No                                           | Do not fetch |
| Coralogix WebAPI Endpoint URL | The Coralogix WebAPI URL              | Yes (Don't change it unless instructed to do so by Coralogix personnel)| https://webapi.ClusterURL.com                        |
| Private Key               | Your Coralogix account Send-Your-Data API key               | Yes                                     | N/A  |
| Application Name (for tags) | The Coralogix application name that will be assigned to the tags created by this instance  | Yes    | Cortex XSOAR |
| Subsystem Name (for tags)   | The Coralogix subsystem name that will be assigned to the tags created by this instance |   Yes | Cortex XSOAR |
| Coralogix ES-API Endpoint URL | The Coralogix ES-API URL |   Yes | Elasticsearch-API |
| Basic incidents query | The Lucene query for fetching incidents. If not specified, will return Coralogix alerts that were sent to the Demisto webhook | No | N/A |
| Incidents Application Name | Limits the incidents query to only return incidents of a specific application name |  No |  N/A |
| Incidents Severity | Limits the incidents query to only return incidents of a specific severity |  No |  N/A |
| Incidents Name Field | The Coralogix field value that should be used as the incident's name. If not specified, the integration will use the `alert_name` field |  No |  N/A |
| Incidents first fetch days | The number of days to look back for incidents |  No |  3 |
| Maximum number of incidents to fetch at a single call | Maximum number of incidents to retrieve at each call to Coralogix |  No |  50 |


After configuring these parameters you should be able to do the following:

1. Automatically fetch incidents from Coralogix (based on the Demisto webhook) by checking the box next to "Fetches incidents" in the integration instance settings.

3. Search for information in your Coralogix account directly from the Cortex XSOAR war room by using the command `!coralogix-search` for example:

```
!coralogix-search query="security.rcode_name:\"NXDOMAIN\"" using="Coralogix_instance_1"
```

```

```

1. Tag interesting timestamps on the Coralogix timeline directly from the Cortex XSOAR war room by using the command `!coralogix-tag` for example:

```
!coralogix-tag name="Data leak started" timestamp="2020-12-31T23:59:59"
```

![](images/Kazam_screenshot_00282-700x130.png)

![](images/Kazam_screenshot_00281-700x130.png)

Also, just like with any other integration of Cortex XSOAR, you can create any playbook you'd like and combine these operations with operations available from other integrations to automatically respond to security-related incidents.

Hoping you found this content helpful.
