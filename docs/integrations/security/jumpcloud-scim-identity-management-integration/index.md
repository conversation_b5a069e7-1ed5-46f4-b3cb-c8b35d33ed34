---
title: "JumpCloud SCIM Identity Management"
date: "2023-01-19"
coverImage: "jumpcloud.png"
description: "Send your logs to Coralogix using the JumpCloud SCIM Identity Management Integration."
---

Send your logs to Coralogix using the [JumpCloud SCIM Identity Management Integration](http://Custom SCIM Identity Management integration).

The Custom SCIM Identity Management integration allows you to provision, update, and deprovision users and groups from JumpCloud in applications that support SCIM. Leverage this integration to centralize user lifecycle management, sync user data, manage groups, and control access and authorization from the JumpCloud Admin Portal.

## Configuration

**STEP 1. Single Sign-On**

Login to JumpCloud portal. Select **SSO** \> **Coralogix SSO application**

![JumpCloud SCIM Identity Management Integration SSO](images/image-30.png)

**STEP 2. Service Provider Configuration**

- Navigate to the **Identity Management** tab.

![JumpCloud SCIM Identity Management Integration Base URL](images/image-31-1024x583.png)

- Input the **Base URL** as one of the following values for **SCIM Connector Base URL**. The URL should correspond to the Coralogix [domain](../../../user-guides/account-management/account-settings/coralogix-domain/index.md) where your data is stored.

| Region | Tenant URL |
| --- | --- |
| US1 | [https://ng-api-http.coralogix.us/scim](https://ng-api-http.coralogix.us/scim) |
| EU1 | [https://ng-api-http.coralogix.com/scim](https://ng-api-http.coralogix.com/scim) |
| EU2 | [https://ng-api-http.eu2.coralogix.com/scim](https://ng-api-http.eu2.coralogix.com/scim) |
| AP1 (IN) | [https://ng-api-http.app.coralogix.in/scim](https://ng-api-http.app.coralogix.in/scim) |
| AP2 (SG) | [https://ng-api-http.coralogixsg.com/scim](https://ng-api-http.coralogixsg.com/scim) |

- Add the **Token Key**. This can be found in your Coralogix team settings > **Configure SAML** > **Provisioning Token**

![JumpCloud SCIM Identity Management Integration Configure SAML](images/image-32-298x1024.png)

![JumpCloud SCIM Identity Management Integration SAML Configuration](images/image-33-886x1024.png)

- Input the email address of a user that belongs to one of the relevant groups in your SSO under **Test User Email**.

![JumpCloud SCIM Identity Management Integration User Email](images/image-35-1024x583.png)

**STEP 3. Test Connection**

- Click **Test Connection.**  You should see the following popup confirming the connection was successful:

![JumpCloud SCIM Identity Management Integration Successful Connection](images/image-36.png)

- Ensure **Group management** is **enabled**.

**STEP 4. Activation**

- Click **Activate.**

![JumpCloud SCIM Identity Management Integration Activation](images/image-37-1024x599.png)

- Click **Save.**

**Step 5. Assign your groups to the Coralogix SSO application**

- Go to JumpCloud "User Groups".

- For each group that should be managed by SCIM, attach it to the Coralogix SSO application as shown below.

![](images/image-10-1024x498.png)

## **Support**

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
