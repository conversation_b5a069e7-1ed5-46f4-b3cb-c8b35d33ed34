---
title: "<PERSON> Plugin"
date: "2019-12-05"
coverImage: "Jenkins-1000X1000.png"
show_blocks:
  - domain_selector
---

The Coralogix Jenkins plugin facilitates the transmission of audit, security, and pipeline console logs to Coralogix, while pushing tags to the Coralogix platform.  
**Notes:**

- To send metrics to Coralogix, use our [Jenkins Telemetry (Otel) integration](../jenkins-telemetry/index.md).

- The Coralogix plugin will work on Jenkins version 2.277.2 and above. If your Jenkins isn't in the mentioned versions, you may try our Curl commands located further in the doc.

## Installation

- Download the `hpi` file from [GitHub releases](https://github.com/coralogix/jenkins-coralogix-plugin/releases)

- Put the `hpi` file in the directory `$JENKINS_HOME/plugins`

- Restart Jenkins

[![Coralogix Plugin](images/coralogix_plugin.png)](https://github.com/coralogix/jenkins-coralogix-plugin/blob/master/docs/images/coralogix_plugin.png)

## Configuration

Go to `<PERSON><PERSON>`, open `Configure system`, find the `Coralogix` section and configure your account [Send-Your-Data API key](../../../../user-guides/account-management/api-keys/send-your-data-api-key/index.md) for sending system/audit/security logs.

You can configure as many keys as you want.

[![Coralogix Configuration](images/coralogix_global_configuration.png)](https://github.com/coralogix/jenkins-coralogix-plugin/blob/master/docs/images/coralogix_global_configuration.png)

**Notes**:

- **Send system logs** won't be checked, as it's not currently supported.

- If you use Private Link, you can change `Coralogix Region` to _Custom_ and overwrite it in `Custom Coralogix endpoint` field. An example of a Custom Coralogix endpoint is `private.coralogix.com`.

- If your Coralogix [domain](../../../../user-guides/account-management/account-settings/coralogix-domain/index.md) ends with a suffix other than '.com', overwrite the `Coralogix API endpoint` value by going to the `Advanced` section and inserting:

```
https://<span class="domain-value"></span>
```

## Credentials

Before usage, create Jenkins credentials with your Coralogix [Send-Your-Data API key](../../../../user-guides/account-management/api-keys/send-your-data-api-key/index.md) for your team. Navigate to  **Manage Jenkins > Manage Credentials** and click on Jenkins under **Stores scoped to Jenkins**.

[![Coralogix Configuration](images/coralogix_credentials.png)](https://github.com/coralogix/jenkins-coralogix-plugin/blob/master/docs/images/coralogix_credentials.png)

To use this API you also need to [create](../../../../user-guides/account-management/api-keys/api-keys/index.md) a personal or team API key. It’s recommended to use permission presets, as they are automatically updated with all relevant permissions. Alternatively, you can manually add individual permissions.

| Preset | Action | Description |
| --- | --- | --- |
| CICDIntegration | `VERSION-BENCHMARK-TAGS:READ` <br> `VERSION-BENCHMARKS-REPORTS:READ` <br> `VERSION-BENCHMARK-TAGS:UPDATE` | View Version Benchmark Tags <br> View Version Benchmark Reports <br> Modify Version Benchmark Tags | 


Insert the API key into the Coralogix API key field. This will allow us to use the "push tags" function since it will allow the creation of a tag each time there is a build in the pipe.

To push Tags, go under your config and click **Add build step**.

Select Push Tags to Coralogix and select the Tags key from your credentials.

![](images/Screenshot-2022-12-16-at-4.52.23-PM-1024x311.png)

[![Coralogix Configuration](images/coralogix_api_credentials.png)](https://github.com/coralogix/jenkins-coralogix-plugin/blob/master/docs/images/coralogix_api_credentials.png)

## Usage

This plugin supports sending build logs and tags for Freestyle project and Pipelines.

### Send logs

Send your build logs to Coralogix.

**Freestyle project**  
Check **Send build logs to Coralogix**, select Private Key, and provide the Application and Subsystem names.

![](images/image-1-1024x241.png)

### Pipeline

This is the Groovy implementation:

``` groovy
pipeline {
    agent any
    stages {
        stage('Test') {
            steps {
                echo "Hello world!"
            }
        }
    }
    post {
        always {
            coralogixSend privateKeyCredentialId: 'coralogix-production',
                          application: 'MyApp',
                          subsystem: "${env.JOB_NAME}",
                          splitLogs: true
        }
    }
}
```

### Curl

It's possible to use Curl command to create a log. Select the [Coralogix \[2\] Logs Endpoint](../../../coralogix-endpoints.md) associated with your Coralogix [domain](../../../../user-guides/account-management/account-settings/coralogix-domain/index.md).

  

``` bash
curl --location --request POST '<endpoint>' \
--header 'private_key: <privatekey>' \
--header 'Content-Type: application/json' \
--data-raw '{"applicationName": "prd", "subsystemName": "app", "text": "hello world", "severity": "3"}'
```

!!! note
    "text", "severity" are optional fields.

### Push tag

Push version tag to Coralogix.

**Freestyle project**  
Add build step Push Coralogix tag and configure:

- **Private Key** - your Coralogix account [Send-Your-Data API key](../../../../user-guides/account-management/api-keys/send-your-data-api-key/index.md)

- **Tag name** - version tag name

- **Application name** - your application name

- **Subsystem names** - your subsystem names

- **Icon(optional)** - your own tag picture

![](images/image.png)

### Pipeline

This is the `Groovy` representation of `Push Coralogix tag` build step:

``` groovy
pipeline {
    agent any
    stages {
        stage('Test') {
            steps {
                echo "Hello world!"
            }
        }
    }
    post {
        success {
            coralogixTag apiKeyCredentialId: 'coralogix-production-api',
                         tag: '1.0.0',
                         applications: [
                            [name: 'MyApp']
                         ],
                         subsystems: [
                            [name: 'staging'],
                            [name: 'production']
                         ],
                         icon: 'https://raw.githubusercontent.com/coralogix/jenkins-coralogix-plugin/master/docs/images/logo.svg'
        }
    }
}
```

### Curl

It's possible to use Curl command to create a tag, choose the right Endpoint, add the "APIkey" and run the command below.  

!!! note
    "timestamp" and "iconUrl" are optional fields.

| Domain | Endpoint |
| --- | --- |
| coralogix.com | https://webapi.coralogix.com/api/v1/external/tags/ |
| coralogix.us | https://webapi.coralogix.us/api/v1/external/tags/ |
| coralogix.in | https://webapi.app.coralogix.in/api/v1/external/tags/ |

``` bash
curl --location --request POST '<yourCoralogixEndpoint>' \
--header 'Authorization: Bearer <APIKey>' \
--header 'Content-Type: application/json' \
--data-raw '{
"name": "test tag",
"timestamp": <currentTimeinMilliseconds>
"application": ["prd", "dev"],
"subsystem": ["app", "mobile"],
"iconUrl": "<iconUrl>"
}'
```

## Upgrading[](https://github.com/coralogix/jenkins-coralogix-plugin#upgrading)

If upgrading from version <= 1.17, you will get an error in the Manage Jenkins UI:

[![Jenkins Error](images/jenkins_upgrade_error.png)](https://github.com/coralogix/jenkins-coralogix-plugin/blob/master/docs/images/jenkins_upgrade_error.png)

To clear this error, simply click "Manage" and then "Discard Unreadable Data" of the CoralogixConfiguration:

[![Jenkins Error](images/jenkins_discard_unreadable.png)](https://github.com/coralogix/jenkins-coralogix-plugin/blob/master/docs/images/jenkins_discard_unreadable.png)

## License[](https://github.com/coralogix/jenkins-coralogix-plugin#license)

The Coralogix Plugin is licensed under the Apache 2.0 License.

## Support

**Need help?**

Our world-class customer success team is available 24/7 to answer any questions that may come up. Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
