---
title: "SeriLog for .Net Core"
date: "2020-09-03"
coverImage: "SeriLog_large.png"
---

If you are using SeriLog as your logger in .Net Core, use Coralogix Sink to push logs to your Coralogix account.

## Setup

**STEP 1**. [Fetch SeriLog.Sinks.Coralogix from NuGet](https://www.nuget.org/packages/Coralogix.Serilog/).

**STEP 2**. Configure Coralogix Sinks with your [Send-Your-Data API key](../../../user-guides/account-management/api-keys/send-your-data-api-key/index.md), [ApplicationName](../../../user-guides/account-management/account-settings/application-and-subsystem-names/index.md), and [SubSystemName](../../../user-guides/account-management/account-settings/application-and-subsystem-names/index.md).

Here is an example:

``` cs
using Serilog.Sinks.Coralogix;
using Serilog;
....

var log = new LoggerConfiguration()
                .WriteTo.CoralogixSink(new CoralogixSinkOptions
                {
                    PrivateKey = "xxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxxx",
                    ApplicationName = "myApp",
                    SubsystemName = "mySubs"
                })
                .CreateLogger();
```

**STEP 3**. Set up your endpoint URL by selecting the [Coralogix Bulk Logs endpoint](../../coralogix-endpoints.md) associated with your Coralogix [domain](../../../user-guides/account-management/account-settings/coralogix-domain/index.md).

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
