---
title: "Java"
date: "2016-02-14"
coverImage: "Coralogix-JAVA-integration.jpg"
show_blocks:
  - domain_selector
---

We believe you should not be limited to application based file shippers.

So to support such cases where you want to integrate your application to Coralogix on the basic code level we provide easy-to-use SDKs.

Our SDKs will integrate with the basic logger functions and will also offer appenders support for Java.

We support Maven repository.

## Requirements

- Coralogix [Send-Your-Data API key](../../../user-guides/account-management/api-keys/send-your-data-api-key/index.md)

- Company ID - You can find your company Id from the 'Settings' -> 'Send Your Data' : company ID will be on the top right.

- Add the 'Coralogix SDK' to your project dependencies.

As we use Artifactory as our repository, you will need to also add a repository to your pom.xml file:

```xml
  <repositories>
    <repository>
        <id>coralogix</id>
        <name>coralogix repo</name>
        <url>https://cgx.jfrog.io/artifactory/maven</url>
    </repository>
  </repositories>
```

Then, add the dependency to your pom.xml file:

```xml
  <dependencies>
    <dependency>
      <groupId>com.coralogix.sdk</groupId>
      <artifactId>coralogix-sdk</artifactId>
      <version>2.0.10</version>
    </dependency>
  </dependencies>
```

Choose your region to customize and use the following API endpoint:

```
CORALOGIX_LOG_URL=https://{{ endpoints.domain_only }}/api/v1/logs
```

## Usage

### **Code Example for SDK versions 2.x**

``` java
import api.CoralogixLogger; //import the SDK

public class logging {

    public static void main(String[] args) {
        String privateKey = "*insert your Send-Your-Data API key*";
        String appName = "*Insert desired application name*";
        String subSystem = "*Insert desired subsystem name*";

        CoralogixLogger.configure(privateKey, appName, subSystem); //Create a connection object to Coralogix
        CoralogixLogger logger = new CoralogixLogger(logging.class.toString()); //Create a generic logger object which the Coralogix SDK

        logger.info("my info log"); //Test the log shipping
    }
}
```
!!! Note
    Line 10 above is required if you would like to use the appender, without it the Logback appender will not work.

### Code Example for SDK versions 1.x

For legacy support, you can still use the old SDK version.

``` java
import com.coralogix.sdk.api.CoralogixLogger; //import the SDK
import com.coralogix.sdk.api.CoralogixSDK; //import the SDK

public class logging {
    public static void main(String[] args) throws Exception{

        String privateKey = "*insert the private key you received after signup*";
        int companyId = "*insert the company ID you received after signup*";
        String appName = *companyId* + "*Insert desired Application name*";
        String subsystem = "*Insert desired subsystem name*";

        CoralogixLogger logger = CoralogixSDK.createAndConnectNewLogger(appName, subsystem, companyId, privateKey); //Create a connection object to Coralogix
        logger.sendCriticalLog("This is my serious test log 1"); //Test the log shipping
    }
}
```
