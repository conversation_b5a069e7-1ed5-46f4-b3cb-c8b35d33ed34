---
title: "Microsoft Azure Functions"
date: "2021-02-02"
coverImage: "azurefunctions-1000X1000.png"
description: "Coralogix provides a seamless integration with Microsoft Azure Cloud, so you can send your logs from anywhere and parse them according to your needs. We provide several trigger strategies with any of the following automatic integrations using Azure custom template deployments: Event Hub, Blob Storage, and Queue Storage."
---

Coralogix provides seamless integration with Microsoft Azure Cloud, so you can send your logs from anywhere and parse them according to your needs.

## Azure Resource Manager deployments

Coralogix provides several trigger strategies with any of the following **automatic** integrations using Azure resource manager (ARM) custom template deployments:

- [Event Hub](../event-hub-microsoft-azure-resource-manager/index.md)

- [Blob Storage](../blob-storage-via-event-grid-microsoft-azure-resource-manager/index.md)

– [Blob To OTel](../blob-to-otel-microsoft-azure-resource-manager/index.md)

- [Queue Storage](../queue-storage-microsoft-azure-resource-manager/index.md)

- [Diagnostic Data](../diagnostic-data-microsoft-azure-resource-manager/index.md)

## Optional configurations

In addition, we offer [optional configurations](../optional-configurations-microsoft-azure/index.md) for particular use-cases utilizing our Azure deployments. If you require resource monitoring in Azure storage accounts or Event Hubs that cannot be made public, [deploy our function apps with virtual network (VNet) support](../optional-configurations-microsoft-azure/index.md#storage-accounts-event-hubs-with-restricted-public-access).

## Terraform Modules

Using our Terraform modules, you can easily install and manage Coralogix integrations with Azure services as modules in your infrastructure code:

- [Event Hub](../azure-event-hub-terraform-module/index.md)

- [Blob Storage via Event Grid](../azure-blob-storage-via-event-grid-terraform-module/index.md)

- [Blob To OTel](../azure-blob-to-otel-terraform-module/index.md)

- [Queue Storage](../azure-queue-storage-terraform-module/index.md)

- [Diagnostic Data](../azure-diagnostic-data-terraform-module/index.md)

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
