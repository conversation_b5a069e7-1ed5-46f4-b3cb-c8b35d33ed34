---
title: "Event Hub: Microsoft Azure Resource Manager (ARM)"
date: "2023-02-13"
coverImage: "azurefunctions-1000X1000.png"
description: "Coralogix provides seamless integration with Azure cloud, allowing you to send your logs from anywhere and parse them according to your needs. Deploy the Event Hub integration to send Coralogix your JSON-formatted queue messages using the ARM template below."
---

Azure Event Hubs can ingest large volumes of data from various sources, such as applications, devices, and sensors. For monitoring purposes, you can configure your systems or applications to send relevant monitoring data as events to an Event Hub.

Coralogix provides seamless integration with Azure cloud, allowing you to send your logs from anywhere and parse them according to your needs. Deploy the Event Hub integration to send Coralogix your JSON-formatted queue messages using the ARM template below.

## [](https://github.com/coralogix/coralogix-azure-serverless/tree/master/EventHub#prerequisites)Prerequisites

- Azure account with an active subscription

## Azure Resource Manager Template Deployment

Sign into your Azure account and deploy the Event Hub integration by clicking [here](https://portal.azure.com/#create/Microsoft.Template/uri/https%3A%2F%2Fraw.githubusercontent.com%2Fcoralogix%2Fcoralogix-azure-serverless%2Fmaster%2FEventHub%2FARM%2FEventHub.json).

![](images/New-Event-Hub-641x1024.png)

### Fields

| Field | Description |
| --- | --- |
| Subscription | Azure subscription within which you wish to deploy the integration.<br/>Must be the same as the monitored Event Hub namespace |
| Resource Group | Resource group in which you wish to deploy the integration |
| Coralogix Region | Region associated with your<br/>[Coralogix domain](../../../user-guides/account-management/account-settings/coralogix-domain/index.md) |
| Custom URL | Custom URL associated with your Coralogix account. Ignore if you do not have a custom URL. |
| Coralogix Private Key | Coralogix [Send-Your-Data API key](../../../user-guides/account-management/api-keys/send-your-data-api-key/index.md) |
| Coralogix Application | Mandatory [metadata field](../../../user-guides/account-management/account-settings/application-and-subsystem-names/index.md) sent with each log and helps to classify it |
| Coralogix Subsystem | Mandatory [metadata field](../../../user-guides/account-management/account-settings/application-and-subsystem-names/index.md) sent with each log and helps to classify it |
| Event Hub Resource Group | Name of the resource group that contains the Event Hub |
| Event Hub Namespace | Name of the Event Hub namespace |
| Event Hub Instance Name | Name of the Event Hub instance to be monitored |
| Event Hub Shared Access Policy Name | Name of the shared access policy of the Event Hub namespace |
| Function App Service Plan Type | Type of service plan to use for the integration.<br/>Consumption is cheapest with support for 'public' Event Hubs. Use Premium if you need to use VNet to configure access to restricted Event Hubs. |

**Notes**:

- The Event Hub integration allows parsing of queue messages in JSON format.

- Other format messages will **not** be processed and submitted to the Coralogix platform.

## Optional Configuration Options

If your Event Hub has restricted access, review this [optional configuration documentation](../optional-configurations-microsoft-azure/index.md) to learn about VNet support options.

## Additional Resources
| | |
| --- | --- |
| Github | [Event Hub Documentation](https://github.com/coralogix/coralogix-azure-serverless/tree/master/EventHub) |
| Terraform | [Terraform Modules for Microsoft Azure Event Hub](../azure-event-hub-terraform-module/index.md) |
| Microsoft Azure Functions Manual Integrations | [Blob Storage](../blob-storage-via-event-grid-microsoft-azure-resource-manager/index.md)<br/>[Queue Storage](../queue-storage-microsoft-azure-resource-manager/index.md)|

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
