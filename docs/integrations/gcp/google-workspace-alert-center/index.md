---
title: "Google Workspace Alert Center"
date: "2024-03-13"
coverImage: "icon_cloud_192pt_clr-1.png"
description: "Google Workspace Alert Center offers real-time security alerts and insights that help you protect your organization from the latest threats, including phishing, malware, and other suspicious activity. The following tutorial will show you how to integrate Google Workspace Alert Center with Coralogix directly."
---

## Overview

**Google Workspace Alert Center** offers real-time security alerts and insights that help you protect your organization from the latest threats, including phishing, malware, and other suspicious activity.

The following tutorial will show you how to integrate Google Workspace Alert Center with Coralogix directly.

## Benefits

- Develop a centralized view of all security incidents in the organization.

- Contextualize and cross-reference the investigation with other activities.

- Set up custom dashboards to visualize security issues across products.

## Prerequisites

- Super admin permissions in Google Cloud

- An existing project within Google Cloud

## Setup

**1.** [Configure a Service Account](../gcp-getting-started/index.md) to facilitate automated intermediation.

**2.** Set up [Domain Wide Delegation](https://developers.google.com/identity/protocols/oauth2/service-account#delegatingauthority), to authorize your Service Account to read user data and send it to Coralogix. The OAuth Scope permission required is [`https://www.googleapis.com/auth/apps.alerts`](https://www.googleapis.com/auth/apps.alerts).

**3.** Navigate to **API & Services** > **Library screen**. Select **Google Workspace Alert Center API** and ensure it’s enabled.

**4.** From your Coralogix toolbar, navigate to **Data Flow** > **Integrations**. Select **Google Workspace Alert Center**.

**5.** Click **\+ ADD NEW**.

**6.** Pick the **ACCOUNT KEY/IMPERSONATION** authentication flow and click **NEXT** (available for version **0.1.0** and later).

**7.** (For key-based authentication only) If you haven’t already done so, click **GO TO GCP ACCOUNT** and create a key file. Then, click **NEXT**.

**8.** (For key-based authentication only) Click **SELECT FILE** and upload the key file **you previously created**. A confirmation will appear when the file is uploaded successfully. Click **NEXT**.

**9.** Fill in the settings:

- **Integration Name:** Enter a name for your integration. This field is automatically populated but can be changed if you want.

- Provide the email address of the [super-administrator](https://cloud.google.com/resource-manager/docs/super-admin-best-practices) for the domain associated with the service account you created.

- **Application Name**. Input your [application](../../../user-guides/account-management/account-settings/application-and-subsystem-names/index.md) name.

- **Subsystem Name**. The subsystem name, Google Workspace Alert Center, is read-only and cannot be modified.

- **GCP Project ID**. ID of a GCP project that you perform the setup for (for impersonation-based authentication only).
  
- **Service Account Email**. Email of the service account you configured at the beginning (for impersonation-based authentication only).


**10**. (For impersonation-based authentication only)

- Click **NEXT**

- Copy the email of the Coralogix principal

- Click **GO TO GCP ACCOUNT**

- Select the service account you created earlier, go to the **PERMISSIONS** tab, click **GRANT ACCESS**, paste the copied email into the **NEW PRINCIPAL** field, assign the `Service Account Token Creator` role and click **SAVE**

- Navigate back to the Coralogix portal

!!! Note 
    It may take a few minutes for the GCP IAM role change to take effect.

**11**. Click **COMPLETE** and finish the setup.

Please wait a few minutes before the integration takes effect, and your data is available on the platform.

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Contact us **via our in-app chat** or by sending us an email to [<EMAIL>](mailto:<EMAIL>).
