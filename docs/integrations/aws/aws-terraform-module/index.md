---
title: "AWS Terraform Module"
date: "2023-02-15"
coverImage: "<EMAIL>"
description: "Terraform simplifies the way we deploy our infrastructure and allows us to maintain it as code.Using our Terraform Modules, you can easily install and manage Coralogix integrations with AWS services as modules in your infrastructure code.Our modules are open source and available on our Github and in the Terraform Registry."
---

Terraform simplifies the way we deploy our infrastructure and allows us to maintain it as code.

Using our Terraform Modules, you can easily install and manage Coralogix integrations with AWS services as modules in your infrastructure code.

Our modules are open source and available on our [Github](https://github.com/coralogix/terraform-coralogix-aws/tree/master/modules/cloudtrail) and in the [Terraform Registry](https://registry.terraform.io/modules/coralogix/aws/coralogix/latest).

## Prerequisites

**STEP 1**. [Sign up](https://signup.coralogix.com/#/) for a Coralogix account. Set up your account on the Coralogix [domain](../../../user-guides/account-management/account-settings/coralogix-domain/index.md) corresponding to the region within which you would like your data stored.

**STEP 2**. For this functionality it is recommended to [Create](../../../user-guides/account-management/api-keys/api-keys/index.md) a team API key. Choose permission presets or individual permissions depending on the Terraform functionality you are planning to use.

**STEP 3**. [Install](https://developer.hashicorp.com/terraform/tutorials/aws-get-started/install-cli) Terraform.

## Installation

Install the AWS service of your choice:

- [CloudTrail](../aws-cloudtrail-terraform-module/index.md)

- [S3 Log Collection](../aws-s3-logs-collection-terraform-module/index.md)

- [CloudWatch](../aws-cloudwatch/aws-cloudwatch-terraform-module/index.md)

## **Support**

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at **[<EMAIL>](mailto:<EMAIL>)**.
