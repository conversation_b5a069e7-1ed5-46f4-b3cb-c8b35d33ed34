---
title: "AWS Load Balancer"
date: "2019-02-10"
coverImage: "AWS-Load-Balancer-1000X1000.png"
---

This tutorial demonstrates how to collect your AWS Elastic Load Balancers, Application Load Balancers, and Network Load Balancers using [Elastic Load Balancing](https://docs.aws.amazon.com/elasticloadbalancing/latest/userguide/what-is-load-balancing.html). The data, decrypted and retaining original timestamps, is sent to Coralogix. The process is installation-free and entails simply deploying a Lambda function.

Once ingested by Coralogix, view and analyze your logs on our platform using queries, alerts, visualizations, and ML capabilities.

## Prerequisites

- AWS account with permissions to create Lambdas and IAM roles

- An operating load balancer that writes its access log to S3

- An S3 bucket clear of any triggers and with a policy that grants the load balancer permission to write access logs to the bucket

## Installation

**STEP 1**. Navigate to [Coralogix Lambda creation](https://us-east-1.console.aws.amazon.com/lambda/home?region=us-east-1#/create/app?applicationId=arn:aws:serverlessrepo:eu-central-1:************:applications/Coralogix-S3).

**STEP 2**. Fill in the required parameters.

**STEP 3**. Check the checkbox: "I acknowledge that this app creates custom IAM roles and resource policies."

**STEP 4**. Deploy.

## Parameters & Descriptions

| Variable | Description |
| --- | --- |
| **Application Name** | Stack name of the application created via AWS CloudFormation.  
If your log is JSON format, use its dynamic value.  
Example: `$.level1.level2.value` |
| **NotificationEmail** | Failure notification email address |
| **ApplicationName** | [**Application name**](../../../user-guides/account-management/account-settings/application-and-subsystem-names/index.md) as it appears in your Coralogix UI |
| **BlockingPattern** | If you wish to block some of the logs adding a substring will act as selector.  
Default is empty to send all logs. |
| **BufferSize** | Buffer size for logs in the lambda function |
| **CoralogixRegion** | Region \[Europe, Europe2, India, Singapore, or US\] associated with your Coralogix account [**domain**](../../../user-guides/account-management/account-settings/coralogix-domain/index.md) |
| **Debug** | Coralogix logger debug mode |
| **FunctionArchitecture** | Function supports x86_64 or arm64 |
| **FunctionMemorySize** | Max memory for the function itself |
| **FunctionTimeout** | Maximum time in seconds the function may be allowed to run |
| **NewlinePattern** | Pattern for lines splitting. Default is `(?:\r\n|\r|\n)`. |
| **S3BucketName** | Name of the S3 bucket to watch |
| **S3KeyPrefix** | The S3 path prefix to watch, if you want to watch a particular subfolder within the bucket |
| **S3KeySuffix** | S3 path suffix to watch |
| **SamplingRate** | Sets the sampling rate |
| **SsmEnabled** | **True** if you want to store your coralogix private_key as a secret and **False** if you do not |
| **SubsystemName** | [**Subsystem name**](../../../user-guides/account-management/account-settings/application-and-subsystem-names/index.md) as it appears in your Coralogix UI. If your log is JSON format, can use its dynamic value, for example: `$.level1.level2.value`. |
| **CustomDomain** | Coralogix custom domain. Leave empty if you do not use a custom domain. |
| **PrivateKey** | Your Coralogix [**Send-Your-Data API Key**](../../../user-guides/account-management/api-keys/send-your-data-api-key/index.md) |
| **LayerARN** | Your Coralogix SSM Layer ARN |

## Additional Resources

| | |
| --- | --- |
| Blog | [How to Get the Most Out of Your ELB Logs](https://coralogix.com/blog/how-to-get-the-most-out-of-your-elb-logs/) |

## **Support**

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at **[<EMAIL>](mailto:<EMAIL>)**.
