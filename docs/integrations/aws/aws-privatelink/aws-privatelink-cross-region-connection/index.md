---
title: "Cross-Region Connectivity"
date: "2023-11-28"
description: "This tutorial provides step-by-step guidance on configuring AWS PrivateLink cross-region connectivity."
---

# AWS PrivateLink: Cross-Region Connectivity

Coralogix now supports [AWS PrivateLink for native cross-region connectivity](https://aws.amazon.com/blogs/networking-and-content-delivery/introducing-cross-region-connectivity-for-aws-privatelink/), making it dramatically easier to securely send observability data from multiple AWS regions to your Coralogix PrivateLink endpoint. This new capability eliminates the need for complex workarounds and simplifies cross-region integrations. Whether you're scaling globally or distributing workloads across regions, this enhancement reduces setup time and removes the need to duplicate infrastructure.

## Benefits of native cross-region PrivateLink over VPC peering

Previously, enabling PrivateLink across regions required complex manual setup: cross-region VPC peering, route table configuration, DNS management, and more.

Native support replaces that complexity with a simpler, built-in alternative:

- No VPC peering or AZ alignment required
- IAM-based access control
- Built-in high availability with automatic routing across healthy zones
- Lower operational overhead with fewer networking components to maintain

## How Coralogix uses native cross-region PrivateLink

You can now connect Coralogix to workloads in different AWS regions using a single, simplified PrivateLink configuration.

!!! Note
    Cross-region connectivity only works when both regions are in the same [AWS partition](https://docs.aws.amazon.com/whitepapers/latest/aws-fault-isolation-boundaries/partitions.html). For example, connecting `us-east-1` and `eu-west-1` is supported, but connections between `aws-us-gov` (AWS GovCloud) and `aws-cn` (AWS China) partitions are not.

## Prerequisites

Before configuring a cross-region endpoint, make sure you've reviewed the available [service endpoints](../aws-privatelink/index.md#privatelink-endpoints).

The IAM user or role creating the endpoint must have the following permission: `vpce:AllowMultiRegion`.

## Cross-Region endpoint configuration

**STEP 1. Create an IAM permission policy**

Create an IAM permission policy and attach it to the IAM user or service creating the endpoint. This is explicitly required even if you have all access configured.

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "AllowMultiRegionPrivateLink",
      "Effect": "Allow",
      "Action": [
        "vpce:AllowMultiRegion"
      ],
      "Resource": "*"
    }
  ]
}
```

**STEP 2. Create a cross-region VPC endpoint**

- Navigate to the VPC console in your region.

- Navigate to PrivateLink and "Lattice" > "Endpoints". Click "Create endpoint".

![cross-region-01.png](./images/cross-region-01.png)

- Enter the PrivateLink service name for the appropriate region.

- Select "PrivateLink ready partner services" as the category.

![cross-region-02.png](./images/cross-region-02.png)

- In the Service settings section, copy the service name of the Coralogix PrivateLink address where your target region is hosted into the "Service Name" section. To find the correct Coralogix PrivateLink service name, select the [PrivateLink endpoint](../aws-privatelink/index.md#privatelink-endpoints) associated with your Coralogix region and domain. The example below would be for the `coralogix.com` domain:

![cross-region-03.png](./images/cross-region-03.png)

- Under "Service Region", check the box "Enable cross-region endpoint".

- Click "Verify service" to confirm that the service exists and is accessible.

![cross-region-04.png](./images/cross-region-04.png)

**STEP 3. Configure VPC and subnets**

- In the Network Settings, select the VPC in your region where you wish to create the endpoint.

- Expand the "Additional settings" section and "Enable DNS name".

- Under "Subnets", choose at least two availability zones for redundancy and high availability.

![cross-region-05.png](./images/cross-region-05.png)

**STEP 4. Select a security group to enable traffic to this VPC endpoint**

  The security group **must** accept inbound traffic in port 443 (TCP).

![cross-region-06.png](./images/cross-region-06.png)

**STEP 5. Verify endpoint status**

- Wait for the endpoint status to change from "Pending" to "Available". This may take a few minutes.
- Once available, note the DNS names generated for your cross-region endpoint. These will include:
  - Private DNS names for each AZ (e.g., `vpce-0123456789abcdef-abcdefgh-&lt;region-az&gt;.&lt;region&gt;.vpce.amazonaws.com`)
  - A Coralogix [domain](../aws-privatelink/index.md#privatelink-endpoints)-based private DNS name: ingress.private.&lt;cx_domain&gt;.

!!! Note
    With enabled DNS name in Step 3. This propagates the private dns endpoint (e.g. `ingress.private.<region-domain>`) to the private hosted zone. You can separately configure a DNS name to your `vpce` DNS (must be aligned to the coralogix domain SSL). Run the following command in a workload within the VPC to view cert:
    ```bash
    openssl s_client -connect ingress.private.<region-domain>:443 -showcerts
    ```

![cross-region-07.png](./images/cross-region-07.png)

**STEP 6. Connect to the VPC network**

Validate that the endpoint is working with either the following:

1. Connected VPC to a running workload (e.g. ec2 instance) and enter the following command, adjusted per cross-region:

    ```bash
    # example US region
    # telnet ingress.private.coralogix.us
    telnet <ingress.private.<region-domain> 443
    ```

2. Try sending a test log using the [Coralogix REST API /singles](https://coralogix.com/docs/developer-portal/apis/log-ingestion/coralogix-rest-api-singles/)

## Best practices

For optimal performance, we recommend using at least two AZs for your VPC endpoint.

## Limitations & considerations

- Cross-region connectivity is only supported for Interface type VPC endpoints.
- Both your region and the Coralogix service region must be in the same AWS partition.
- Cross-region endpoints count toward your Interface VPC Endpoints per VPC quota.
