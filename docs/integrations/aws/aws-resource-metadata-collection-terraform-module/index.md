---
title: "AWS Resource Metadata Collection Terraform Module"
date: "2024-02-05"
coverImage: "<EMAIL>"
description: "Using Coralogix Terraform Modules, you can easily install and manage Coralogix integrations with AWS services as modules in your infrastructure code. This tutorial demonstrates how to install our Resource Metadata Collection Lambda."
---
# AWS Resource Metadata Collection Terraform Module

Using Coralogix Terraform modules, you can easily install and manage Coralogix integrations with AWS services as modules in your infrastructure code. This tutorial demonstrates how to install our [Resource Metadata Collection Lambda](../aws-resource-metadata-collection/index.md).

## Installation

Install our [Resource Metadata Collection Lambda](../aws-resource-metadata-collection/index.md) by adding one of the following declarations to your Terraform project:

### Original mode

```tf
module "resource-metadata" {
  source = "coralogix/aws/coralogix//modules/resource-metadata"

  coralogix_region    = "Europe"
  private_key         = "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXX"
}
```

### High volume mode

High volume mode is recommended for:

1. Environments with more than 5.000 Lambda functions in the target AWS region.
2. Environments with the requirement to collect metadata from multiple AWS accounts and regions.

```tf
module "resource-metadata" {
  source = "coralogix/aws/coralogix//modules/resource-metadata-sqs"

  coralogix_region            = "EU2"
  api_key                     = "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXX"
  event_mode                  = "EnabledCreateTrail"
  source_regions              = ["eu-west-1", "eu-west-2"]
  cross_account_iam_role_arns = ["arn:aws:iam::************:role/CrossAccountRole", "arn:aws:iam::************:role/AnotherCrossAccountRole"]
}
```

## Additional resources

Our modules are open-source and can be found in GitHub and the Coralogix Terraform Registry.

| | |
| --- | --- |
| Documentation | [Coralogix Terraform Provider](../../../developer-portal/infrastructure-as-code/terraform-provider/coralogix-terraform-provider/index.md) |
| Original Mode | [GitHub](https://github.com/coralogix/terraform-coralogix-aws/tree/master/modules/resource-metadata)<br/>[Terraform Registry](https://registry.terraform.io/modules/coralogix/aws/coralogix/latest/submodules/resource-metadata) |
| High Volume Mode | [GitHub](https://github.com/coralogix/terraform-coralogix-aws/tree/master/modules/resource-metadata-sqs)<br/>[Terraform Registry](https://registry.terraform.io/modules/coralogix/aws/coralogix/latest/submodules/resource-metadata-sqs) |

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at **[<EMAIL>](mailto:<EMAIL>)**.
