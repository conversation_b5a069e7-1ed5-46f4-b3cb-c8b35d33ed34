---
title: "AWS S3 Logs Collection Terraform Module"
date: "2022-05-26"
coverImage: "<EMAIL>"
description: "Use our Terraform Module for AWS S3 to easily integrate your S3 logs to Coralogix as a module in your infrastructure code."
---

Terraform simplifies the way we deploy our infrastructure and allows us to maintain it as code.

Using our Terraform Modules, you can easily install and manage Coralogix integrations with AWS services as modules in your infrastructure code.

Our modules are open source and available on our [Github](https://github.com/coralogix/terraform-coralogix-aws/tree/master/modules/coralogix-aws-shipper) and in the [Terraform Registry](https://registry.terraform.io/modules/coralogix/aws/coralogix/latest/submodules/coralogix-aws-shipper)

## Installation

Install our [S3 Log Collection Lambda](../forward-aws-logs-via-lambda-shipper/index.md) by adding this declaration to your Terraform project.

``` tf
module "coralogix-shipper-s3" {
  source = "coralogix/aws/coralogix//modules/coralogix-aws-shipper"

  coralogix_region   = "EU1"
  integration_type   = "S3"
  api_key            = "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXX"
  application_name   = "s3"
  subsystem_name     = "logs"
  s3_bucket_name     = "bucket-name"
}
```

**Notes**:

- Input your Coralogix [Send-Your-Data API key](../../../user-guides/account-management/api-keys/send-your-data-api-key/index.md) as `api_key`.

- Input [region](../../../user-guides/account-management/account-settings/coralogix-domain/index.md) (as `coralogix_region`) associated with your Coralogix account.

Execute the following:

``` bash
terraform init
terraform plan
terraform apply
```

Run `terraform destroy` when you no longer need these resources.

## Additional Resources
| | |
| --- | --- |
| Documentation | [Coralogix Terraform Provider](../../../developer-portal/infrastructure-as-code/terraform-provider/coralogix-terraform-provider/index.md) |

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
