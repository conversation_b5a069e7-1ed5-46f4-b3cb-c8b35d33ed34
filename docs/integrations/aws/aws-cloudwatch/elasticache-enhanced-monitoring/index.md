---
title: "ElastiCache Enhanced Monitoring for CloudWatch Metrics"
date: "2025-01-09"
description: "Coralogix offers ElastiCache enhanced monitoring, an extension to AWS metrics from CloudWatch using the Amazon ElastiCache API to collect tags and additional metrics."
---

## Overview

Coralogix offers ElastiCache enhanced monitoring, an extension to AWS Metrics from CloudWatch using the [Amazon ECS API](https://docs.aws.amazon.com/AmazonElastiCache/latest/APIReference/API_Operations.html) to collect tags and additional metrics.

This approach saves some calls to `resroucegrouptagging` API and adds all the new metrics and tags that are specified in this document under [Collected data](#collected-data).

## Prerequisites

- [AWS Metrics from CloudWatch](../aws-metrics-via-cloudwatch/index.md) integration with version `0.5.0` or higher.
- Policy permissions used for this integration:
  - `elasitcache:DescribeCacheClusters`
  - `elasticache:ListResourceTags`

## Configuration

- `ElastiCacheEnhancedMonitoring` toggle in the [AWS Metrics from CloudWatch](../aws-metrics-via-cloudwatch/index.md) integration configuration

## Collected data

### Metrics

| Metric Name                                           | Dimensions | Description |
| ---                                                   | --- | --- |
| amazonaws_com_AWS_ElastiCache_Cluster_AllocatedVCPU   | CacheClusterId | container instance metric |
| amazonaws_com_AWS_ElastiCache_Cluster_AllocatedMemory | CacheClusterId | container instance metric |
| amazonaws_com_AWS_ElastiCache_Cluster_NumNodes        | CacheClusterId | container instance metric |
| amazonaws_com_AWS_ElastiCache_Cluster_Status          | CacheClusterId | container instance metric |
| amazonaws_com_AWS_ElastiCache_Node_Status             | CacheClusterId, CacheNodeId | cluster node metric |

### Tags

- `CacheNodeType` - For example `cache.r6g.2xlarge`, `cache.m6g.8xlarge`, etc.
- `Engine` - The name of the cache engine (`memcached` or `redis`) to be used for elasti-cache cluster.
- `EngineVersion` - The version of the cache engine that is used in elasti-cache cluster.
- `CacheNodeId` - The cache node identifier. A node ID is a numeric identifier (0001, 0002, etc.).

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email to [<EMAIL>](mailto:<EMAIL>).
