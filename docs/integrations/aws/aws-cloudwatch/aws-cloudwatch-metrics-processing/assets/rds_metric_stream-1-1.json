{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "", "editable": true, "fiscalYearStartMonth": 0, "gnetId": null, "graphTooltip": 0, "id": 937, "iteration": 1671023250562, "links": [], "liveNow": false, "panels": [{"datasource": null, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 0}, "id": 17, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "avg(amazonaws_com_AWS_RDS_CPUUtilization{DBInstanceIdentifier=~\"$DBInstanceIdentifier\",DBInstanceIdentifier!=\"\"}) by (DBInstanceIdentifier)", "interval": "", "legendFormat": "{{DBInstanceIdentifier}}", "refId": "A"}], "title": "DB CPU Utilization %", "type": "timeseries"}, {"datasource": null, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 0}, "id": 60, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "avg(amazonaws_com_AWS_RDS_Deadlocks_sum{DBInstanceIdentifier=~\"$DBInstanceIdentifier\",DBInstanceIdentifier!=\"\"}) by (DBInstanceIdentifier)", "interval": "", "legendFormat": "{{DBInstanceIdentifier}}", "refId": "A"}], "title": "Deadlocks", "type": "timeseries"}, {"datasource": null, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"log": 10, "type": "log"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line+area"}}, "mappings": [], "max": 5000, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 3000}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 7}, "id": 19, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "avg(amazonaws_com_AWS_RDS_DatabaseConnections{DBInstanceIdentifier=~\"$DBInstanceIdentifier\",DBInstanceIdentifier!=\"\"}) by (DBInstanceIdentifier)", "interval": "", "legendFormat": "{{DBInstanceIdentifier}}", "refId": "A"}], "title": "Connections (Max 3000)", "type": "timeseries"}, {"datasource": null, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 7}, "id": 34, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "avg(amazonaws_com_AWS_RDS_RowLockTime{DBInstanceIdentifier=~\"$DBInstanceIdentifier\",DBInstanceIdentifier!=\"\"}) by (DBInstanceIdentifier)", "interval": "", "legendFormat": "{{DBInstanceIdentifier}}", "refId": "A"}], "title": "Row Lock Time", "type": "timeseries"}, {"datasource": null, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 15}, "id": 38, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "avg(amazonaws_com_AWS_RDS_CPUUtilization{DBClusterIdentifier!=\"\",Role!=\"\"}) by (Role,DBClusterIdentifier) ", "interval": "", "legendFormat": "{{Role}}>>{{DBClusterIdentifier}}", "refId": "A"}], "title": "DB CPU Utilization % (All Role)", "type": "timeseries"}], "refresh": "", "schemaVersion": 32, "style": "dark", "tags": [], "templating": {"list": [{"allValue": ".*", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": null, "definition": "label_values(DBInstanceIdentifier)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "DB Instance Identifier", "multi": true, "name": "DBInstanceIdentifier", "options": [], "query": {"query": "label_values(DBInstanceIdentifier)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-90d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "RDS  Dashboard by DB Instance Identifier (Metric Stream)", "uid": "AhM9WsM9a", "version": 3}