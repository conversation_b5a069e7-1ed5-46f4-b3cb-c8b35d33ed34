---
title: "AWS VPC Flow Logs Terraform Module"
date: "2023-05-24"
coverImage: "<EMAIL>"
description: "Using Coralogix Terraform Modules, you can easily install and manage Coralogix integrations with AWS services as modules in your infrastructure code. This tutorial demonstrates how to install the VPC Flow Logs collection Lambda."
---

Using Coralogix Terraform Modules, you can easily install and manage Coralogix integrations with AWS services as modules in your infrastructure code. This tutorial demonstrates how to install the [VPC Flow Logs collection Lambda](../forward-aws-logs-via-lambda-shipper/index.md).

Our modules are open source and available on our [GitHub](https://github.com/coralogix/terraform-coralogix-aws/tree/master/modules/coralogix-aws-shipper) and in the [Terraform Registry](https://registry.terraform.io/modules/coralogix/aws/coralogix/latest).

## Installation

Install our [](../forward-aws-logs-via-lambda-shipper/index.md)[VPC Flow Logs collection Lambda](../forward-aws-logs-via-lambda-shipper/index.md) by adding this declaration to your Terraform project:

``` tf
module "coralogix-shipper-vpc-flow-logs" {
  source = "coralogix/aws/coralogix//modules/coralogix-aws-shipper"
  integration_type   = "VpcFlow"
  api_key            = "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXX"
  application_name   = "vpcflow-application"
  subsystem_name     = "vpcflow-subsystem"
  s3_bucket_name     = "bucket name"
}
```
**Notes**:

- Input your Coralogix [Send-Your-Data API key](../../../user-guides/account-management/api-keys/api-keys/index.md) as `api_key`.

- Input [region](../../../user-guides/account-management/account-settings/coralogix-domain/index.md) (as `coralogix_region`) associated with your Coralogix account.

Execute the following:

``` bash
terraform init
terraform plan
terraform apply
```

Run `terraform destroy` when you no longer need these resources.

## Additional Resources

| | |
| --- | --- |
| Documentation | [AWS VPC Flow Logs](../forward-aws-logs-via-lambda-shipper/index.md)<br/>[Coralogix Terraform Provider](../../../developer-portal/infrastructure-as-code/terraform-provider/coralogix-terraform-provider/index.md) |
| External Documentation | [GitHub](https://github.com/coralogix/terraform-coralogix-aws/tree/master/modules/coralogix-aws-shipper)<br/>[Terraform Registry](https://registry.terraform.io/modules/coralogix/aws/coralogix/latest) |

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at **[<EMAIL>](mailto:<EMAIL>)**.
