---
title: "Cloudflare Logpush Terraform Module"
date: "2022-07-14"
coverImage: "<EMAIL>"
---

Terraform simplifies the way we deploy our infrastructure and allows us to maintain it as code.

Using our Terraform Modules, you can easily install and manage Cloudflare logpush integrations to Coralogix as modules in your infrastructure code.

Our modules are open source and available on our [Github](https://github.com/coralogix/terraform-coralogix-cloudflare) and in the [Terraform Registry](https://registry.terraform.io/modules/coralogix/cloudflare/coralogix/latest).

## Installation

This module will be creating a Logpush job which will send logs to your Coralogix account.

**Important**: as the internal endpoint has limit of 5MiB per request, either set the limit
  `max_upload_bytes = 5242880`, or use the Version v1.2.0+ of `terraform-coralogix-cloudflare` where it is hardcoded to `5_000_000`.
**Important**: Starting from version 1.0.11, `logpush_options` was replaced by  `output_options`.
  Also, note that this module requires `Terraform` Version 1.20+ .

To use the module, first, add the provider configure block to your Terraform project:

```vhdl
terraform {
  required_providers {
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 4.0"
    }
  }
}

provider "cloudflare" {
  email   = "<EMAIL>"
  api_key = "xxxxxxxxxxxxxxxxxxxxxx"
}

```

Important variables to change:

cloudflare.email - Your email used in Cloudflare.

cloudflare.api_key - Your API key for Cloudflare.

Then add the module block:

```vhdl
module "logpush-job" {
    source = "coralogix/cloudflare/coralogix//modules/logpush-job"
    coralogix_region   = "Europe"
    coralogix_private_key = "xxxxxxxxxxxxxxxxxxxxxxx"
    cloudflare_logpush_dataset = "http_requests"
    cloudflare_zone_id = "xxxxxxxxxxxxxxx" # to be used with zone-scoped datasets
    cloudflare_logpush_fields = ["EdgeStartTimestamp", "EdgePathingOp", "EdgePathingSrc", "EdgePathingStatus", "EdgeRateLimitAction", "EdgeRateLimitID", "EdgeRequestHost", "EdgeResponseBodyBytes", "EdgeResponseBytes", "EdgeResponseCompressionRatio", "EdgeResponseContentType", "EdgeResponseStatus", "EdgeServerIP", "EdgeTimeToFirstByteMs", "FirewallMatchesActions", "FirewallMatchesRuleIDs", "FirewallMatchesSources", "OriginDNSResponseTimeMs", "OriginIP", "OriginRequestHeaderSendDurationMs", "OriginResponseBytes", "OriginResponseDurationMs", "OriginResponseHTTPExpires", "OriginResponseHTTPLastModified", "OriginResponseHeaderReceiveDurationMs", "OriginResponseStatus", "OriginResponseTime", "OriginSSLProtocol", "OriginTCPHandshakeDurationMs", "OriginTLSHandshakeDurationMs", "ParentRayID", "RayID", "RequestHeaders", "ResponseHeaders", "SecurityLevel", "SmartRouteColoID", "UpperTierColoID", "WAFAction", "WAFFlags", "WAFMatchedVar", "WAFProfile", "WAFRuleID", "WAFRuleMessage", "WorkerCPUTime", "WorkerStatus", "WorkerSubrequest", "WorkerSubrequestCount", "ZoneID", "ZoneName"]
    cloudflare_zone_sample_rate = 0.1
}
```

Important variables to change:

- coralogix_region - Associated with your Coralogix [domain](../../../../user-guides/account-management/account-settings/coralogix-domain/index.md), possible options are \[Europe, Europe2, India, Singapore, US\]

- coralogix_private_key - The Coralogix [Send-Your-Data API key](../../../../user-guides/account-management/api-keys/send-your-data-api-key/index.md) which is used to validate your authenticity

- cloudflare_logpush_dataset - The Cloudflare logpush job data-set

- cloudflare_logpush_fields - The logpush dataset specific fields to log delimited with comma, leave empty to include all fields. The timestamp and its variants are included automatically.

- cloudflare_zone/account_id - Your zone/account ID, can be retrieved from Cloudflare dashboard or API.

- cloudflare_zone_sample_rate - Specifies sampling rate. In the module block example above, 10% of the logs are sent.

By default, the integration will set `application_name` as `Cloudflare`, and `subsystem_name` as the data set name. To overwrite these parameters, add the following:

- `header_CX-Application-Name` - application name override

- `header_CX-Subsystem-Name` - subsystem name override

We also have a [Coralogix Terraform Provider](../../../../developer-portal/infrastructure-as-code/terraform-provider/coralogix-terraform-provider/index.md) to help manage your Coralogix resources such as rules and alerts.

If you have any questions, feel free to reach out to our team via our in-app chat!

Values Table:

| Dataset                     | Fields                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
|-----------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| dns_logs                    | ["Timestamp", "ColoCode", "EDNSSubnet", "EDNSSubnetLength", "QueryName", "QueryType", "ResponseCached", "ResponseCode", "SourceIP"]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| firewall_events             | ["Action", "ClientASN", "ClientASNDescription", "ClientCountry", "ClientIP", "ClientIPClass", "ClientRefererHost", "ClientRefererPath", "ClientRefererQuery", "ClientRefererScheme", "ClientRequestHost", "ClientRequestMethod", "ClientRequestPath", "ClientRequestProtocol", "ClientRequestQuery", "ClientRequestScheme", "ClientRequestUserAgent", "ContentScanObjResults", "ContentScanObjSizes", "ContentScanObjTypes", "Datetime", "Description", "EdgeColoCode", "EdgeResponseStatus", "Kind", "LeakedCredentialCheckResult", "MatchIndex", "Metadata", "OriginResponseStatus", "OriginatorRayID", "RayID", "Ref", "RuleID", "Source"]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| http_requests               | ["BotDetectionIDs", "BotDetectionTags", "BotScore", "BotScoreSrc", "BotTags", "CacheCacheStatus", "CacheReserveUsed", "CacheResponseBytes", "CacheTieredFill", "ClientASN", "ClientCountry", "ClientDeviceType", "ClientIP", "ClientIPClass", "ClientMTLSAuthCertFingerprint", "ClientMTLSAuthStatus", "ClientRegionCode", "ClientRequestBytes", "ClientRequestHost", "ClientRequestMethod", "ClientRequestPath", "ClientRequestProtocol", "ClientRequestReferer", "ClientRequestScheme", "ClientRequestSource", "ClientRequestURI", "ClientRequestUserAgent", "ClientSSLCipher", "ClientSSLProtocol", "ClientSrcPort", "ClientTCPRTTMs", "ClientXRequestedWith", "ContentScanObjResults", "ContentScanObjSizes", "ContentScanObjTypes", "Cookies", "EdgeCFConnectingO2O", "EdgeColoCode", "EdgeColoID", "EdgeEndTimestamp", "EdgePathingOp", "EdgePathingSrc", "EdgePathingStatus", "EdgeRequestHost", "EdgeResponseBodyBytes", "EdgeResponseBytes", "EdgeResponseCompressionRatio", "EdgeResponseContentType", "EdgeResponseStatus", "EdgeServerIP", "EdgeStartTimestamp", "EdgeTimeToFirstByteMs", "JA3Hash", "JA4", "JA4Signals", "LeakedCredentialCheckResult", "OriginDNSResponseTimeMs", "OriginIP", "OriginRequestHeaderSendDurationMs", "OriginResponseDurationMs", "OriginResponseHTTPExpires", "OriginResponseHTTPLastModified", "OriginResponseHeaderReceiveDurationMs", "OriginResponseStatus", "OriginSSLProtocol", "OriginTCPHandshakeDurationMs", "OriginTLSHandshakeDurationMs", "ParentRayID", "RayID", "RequestHeaders", "ResponseHeaders", "SecurityAction", "SecurityActions", "SecurityRuleDescription", "SecurityRuleID", "SecurityRuleIDs", "SecuritySources", "SmartRouteColoID", "UpperTierColoID", "WAFAttackScore", "WAFRCEAttackScore", "WAFSQLiAttackScore", "WAFXSSAttackScore", "WorkerCPUTime", "WorkerStatus", "WorkerSubrequest", "WorkerSubrequestCount", "WorkerWallTimeUs", "ZoneName"] |
| nel_reports                 | ["ClientIPASN", "ClientIPASNDescription", "ClientIPCountry", "LastKnownGoodColoCode", "Phase", "Timestamp", "Type"]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| spectrum_events             | ["Application", "ClientAsn", "ClientBytes", "ClientCountry", "ClientIP", "ClientMatchedIpFirewall", "ClientPort", "ClientProto", "ClientTcpRtt", "ClientTlsCipher", "ClientTlsClientHelloServerName", "ClientTlsProtocol", "ClientTlsStatus", "ColoCode", "ConnectTimestamp", "DisconnectTimestamp", "Event", "IpFirewall", "OriginBytes", "OriginIP", "OriginPort", "OriginProto", "OriginTcpRtt", "OriginTlsCipher", "OriginTlsFingerprint", "OriginTlsMode", "OriginTlsProtocol", "OriginTlsStatus", "ProxyProtocol", "Status", "Timestamp"]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| page_shield_events          | ["Action", "Host", "PageURL", "PolicyID", "Timestamp", "URL", "URLContainsCDNCGIPath", "URLHost"]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| audit_logs                  | ["ActionResult", "ActionType", "ActorEmail", "ActorID", "ActorIP", "ActorType", "ID", "Interface", "Metadata", "NewValue", "OldValue", "OwnerID", "ResourceID", "ResourceType", "When"]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| gateway_dns                 | ["ApplicationID", "CNAMECategoryIDs", "CNAMECategoryNames", "ColoCode", "ColoID", "CustomResolveDurationMs", "CustomResolverAddress", "CustomResolverPolicyID", "CustomResolverPolicyName", "CustomResolverResponse", "Datetime", "DeviceID", "DeviceName", "DstIP", "DstPort", "Email", "InitialCategoryIDs", "InitialCategoryNames", "IsResponseCached", "Location", "LocationID", "MatchedCategoryIDs", "MatchedCategoryNames", "MatchedIndicatorFeedIDs", "MatchedIndicatorFeedNames", "Policy", "PolicyID", "Protocol", "QueryCategoryIDs", "QueryCategoryNames", "QueryIndicatorFeedIDs", "QueryIndicatorFeedNames", "QueryName", "QueryNameReversed", "QuerySize", "QueryType", "QueryTypeName", "RCode", "RData", "ResolvedIPCategoryIDs", "ResolvedIPCategoryNames", "ResolvedIPs", "ResolverDecision", "SrcIP", "SrcPort", "TimeZone", "TimeZoneInferredMethod", "UserID"]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| gateway_http                | AccountID, Action, BlockedFileHash, BlockedFileName, BlockedFileReason, BlockedFileSize, BlockedFileType, DestinationIP, DestinationPort, DeviceID, DownloadedFileNames, Email, HTTPHost, HTTPMethod, HTTPVersion,IsIsolated, PolicyID, Referer, RequestID, SourceIP, SourcePort, URL, UploadedFileNames, UserAgent, UserID                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| gateway_network             | ["AccountID", "Action", "BlockedFileHash", "BlockedFileName", "BlockedFileReason", "BlockedFileSize", "BlockedFileType", "Datetime", "DestinationIP", "DestinationPort", "DeviceID", "DeviceName", "DownloadMatchedDlpProfileEntries", "DownloadMatchedDlpProfiles", "DownloadedFileNames", "Email", "FileInfo", "HTTPHost", "HTTPMethod", "HTTPStatusCode", "HTTPVersion", "IsIsolated", "PolicyID", "PolicyName", "Referer", "RequestID", "SessionID", "SourceIP", "SourceInternalIP", "SourcePort", "URL", "UntrustedCertificateAction", "UploadMatchedDlpProfileEntries", "UploadMatchedDlpProfiles", "UploadedFileNames", "UserAgent", "UserID"]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| gateway_network             | ["AccountID", "Action", "Datetime", "DestinationIP", "DestinationPort", "DetectedProtocol", "DeviceID", "DeviceName", "Email", "OverrideIP", "OverridePort", "PolicyID", "PolicyName", "SNI", "SessionID", "SourceIP", "SourceInternalIP", "SourcePort", "Transport", "UserID"]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| network_analytics_logs      | ["AttackCampaignID", "AttackID", "AttackVector", "ColoCity", "ColoCode", "ColoCountry", "ColoGeoHash", "ColoName", "Datetime", "DestinationASN", "DestinationASNName", "DestinationCountry", "DestinationGeoHash", "DestinationPort", "Direction", "GREChecksum", "GREEtherType", "GREHeaderLength", "GREKey", "GRESequenceNumber", "GREVersion", "ICMPChecksum", "ICMPCode", "ICMPType", "IPDestinationAddress", "IPDestinationSubnet", "IPFragmentOffset", "IPHeaderLength", "IPMoreFragments", "IPProtocol", "IPProtocolName", "IPSourceAddress", "IPSourceSubnet", "IPTTL", "IPTTLBuckets", "IPTotalLength", "IPTotalLengthBuckets", "IPv4Checksum", "IPv4DSCP", "IPv4DontFragment", "IPv4ECN", "IPv4Identification", "IPv4Options", "IPv6DSCP", "IPv6ECN", "IPv6ExtensionHeaders", "IPv6FlowLabel", "IPv6Identification", "MitigationReason", "MitigationScope", "MitigationSystem", "Outcome", "ProtocolState", "RuleID", "RuleName", "RulesetID", "RulesetOverrideID", "SampleInterval", "SourceASN", "SourceASNName", "SourceCountry", "SourceGeoHash", "SourcePort", "TCPAcknowledgementNumber", "TCPChecksum", "TCPDataOffset", "TCPFlags", "TCPFlagsString", "TCPMSS", "TCPOptions", "TCPSACKBlocks", "TCPSACKPermitted", "TCPSequenceNumber", "TCPTimestampECR", "TCPTimestampValue", "TCPUrgentPointer", "TCPWindowScale", "TCPWindowSize", "UDPChecksum", "UDPPayloadLength", "Verdict"]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| access_requests             | ["Action", "Allowed", "AppDomain", "AppUUID", "Connection", "Country", "CreatedAt", "Email", "IPAddress", "PurposeJustificationPrompt", "PurposeJustificationResponse", "RayID", "TemporaryAccessApprovers", "TemporaryAccessDuration", "UserUID"]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| casb_findings               | ["AssetDisplayName", "AssetExternalID", "AssetLink", "AssetMetadata", "DetectedTimestamp", "FindingTypeDisplayName", "FindingTypeID", "FindingTypeSeverity", "InstanceID", "IntegrationDisplayName", "IntegrationID", "IntegrationPolicyVendor"]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| device_posture_results      | ["ClientVersion", "DeviceID", "DeviceManufacturer", "DeviceModel", "DeviceName", "DeviceSerialNumber", "DeviceType", "Email", "OSVersion", "PolicyID", "PostureCheckName", "PostureCheckType", "PostureEvaluatedResult", "PostureExpectedJSON", "PostureReceivedJSON", "Timestamp", "UserUID"]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| dns_firewall_logs           | ["ClientResponseCode", "ClusterID", "ColoCode", "EDNSSubnet", "EDNSSubnetLength", "QueryDO", "QueryName", "QueryRD", "QuerySize", "QueryTCP", "QueryType", "ResponseCached", "ResponseCachedStale", "ResponseReason", "SourceIP", "Timestamp", "UpstreamIP", "UpstreamResponseCode", "UpstreamResponseTimeMs"]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| magic_ids_detections        | ["Action", "ColoCity", "ColoCode", "DestinationIP", "DestinationPort", "Protocol", "SignatureID", "SignatureMessage", "SignatureRevision", "SourceIP", "SourcePort", "Timestamp"]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| sinkhole_http_logs          | ["AccountID", "Body", "BodyLength", "DestAddr", "Headers", "Host", "Method", "Password", "R2Path", "Referrer", "SinkholeID", "SrcAddr", "Timestamp", "URI", "URL", "UserAgent", "Username"]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| workers_trace_events        | ["DispatchNamespace", "Entrypoint", "Event", "EventTimestampMs", "EventType", "Exceptions", "Logs", "Outcome", "ScriptName", "ScriptTags", "ScriptVersion"]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| zero_trust_network_sessions | ["AccountID", "BytesReceived", "BytesSent", "ClientTCPHandshakeDurationMs", "ClientTLSCipher", "ClientTLSHandshakeDurationMs", "ClientTLSVersion", "ConnectionCloseReason", "ConnectionReuse", "DestinationTunnelID", "DetectedProtocol", "DeviceID", "DeviceName", "EgressColoName", "EgressIP", "EgressPort", "EgressRuleID", "EgressRuleName", "Email", "IngressColoName", "Offramp", "OriginIP", "OriginPort", "OriginTLSCertificateIssuer", "OriginTLSCertificateValidationResult", "OriginTLSCipher", "OriginTLSHandshakeDurationMs", "OriginTLSVersion", "Protocol", "RuleEvaluationDurationMs", "SessionEndTime", "SessionID", "SessionStartTime", "SourceIP", "SourceInternalIP", "SourcePort", "UserID", "VirtualNetworkID"]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |

Common errors table:

| Error                                                                           | Description                                                                                                                                                                                                                                                                                      |   |
|---------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---|
| creating a new job is not allowed: Bot Management fields are not allowed (1004) | Your Cloudflare account plan doesn't allow the specified fields in cloudflare_logpush_fields. Contact Cloudflare support to enable these fields.                                                                                                                                                 |   |
| creating a new job is not allowed: exceeded max jobs allowed (1004)             | Your Cloudflare account plan doesn't allow the specified dataset in cloudflare_logpush_dataset or you have reached your account maximum concurrent jobs. Contact Cloudflare support to ensure your account can create this logpush dataset and that you didn't exceed your maximum jobs allowed. |   |
