---
title: "Getting Started"
date: "2024-08-28"
---
# Getting Started

## Overview

This section contains all tutorials to help you send your telemetry data and integrate various products with the Coralogix platform.

!!! note
    Coralogix recommends [OpenTelemetry](../opentelemetry/index.md) as the telemetry pipeline and we have dedicated a separate section for [all things OpenTelemetry](../opentelemetry/index.md)

## Send Data to Coralogix

Coralogix supports logs, metrics, and traces from many different sources using any of the following [integrations](https://coralogix.com/integrations/). All integrations require:

- Your Coralogix [Send-Your-Data API key](../user-guides/account-management/api-keys/send-your-data-api-key/index.md)

- An [endpoint](./coralogix-endpoints.md) associated with your Coralogix account [domain](../user-guides/account-management/account-settings/coralogix-domain/index.md)

- [Application and subsystem names](../user-guides/account-management/account-settings/application-and-subsystem-names/index.md) to organize the data in your Coralogix account

### Integration Packages

The **easiest method** for sending us your data is using our two-step, out-of-the box [integration packages](../user-guides/getting-started/packages-and-extensions/integration-packages/index.md).

### Integrations (Interactive)

For those integrations which are yet to be packaged, select a shipper for which to send us your data from our full list of [integrations](https://coralogix.com/integrations/).

- **Cloud-Based Integrations**. We offer a wide range of cloud-based shippers, including AWS, Azure, and GCP integrations.

- **Telemetry Shippers**. Choose from our many shippers, including integrations using [OpenTelemetry](../opentelemetry/getting-started/index.md), [Prometheus](prometheus/prometheus-server/index.md), [Fluentd](files/fluentd/index.md) and [Fluent Bit](files/fluent-bit/index.md).

- **Push &** **Pull** **Integrations**. Choose from our list of push and pull integrations, including [Cloudflare](cdns/cloudflare/index.md), [Nagios](metrics/nagios/index.md), and [Okta](../user-guides/security/security-data-sources/okta-contextual-logs/index.md).

- **Use-Cases.** Select a use-case integration on the basis of the particular logs, metrics, or traces you’d like to send us.

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).