---
title: "Custom Metrics"
date: "2022-07-06"
coverImage: "Relative-graphs-1000X1000.png"
description: "Coralogix provides a scalable Prometheus-compatible managed service for time-series data. This tutorial presents a series of use cases employing our custom metric endpoint, including serverless computing and quick cURL-like calls to send counters and gauges to Coralogix."
---

Coralogix provides a scalable Prometheus-compatible managed service for time-series data.

## Overview

Coralogix supports ingesting metrics in multiple ways. Our most common integrations are [Prometheus](../prometheus/index.md) & [OpenTelemetry](https://coralogix.com/blog/tag/opentelemetry/), and we also have metrics integrations such as [Cloudwatch metrics](../../aws/amazon-data-firehose/aws-cloudwatch-metric-streams-with-amazon-data-firehose/index.md) and [Amazon Data Firehose](../../aws/amazon-data-firehose/send-logs-using-amazon-data-firehose/index.md).

This tutorial presents a series of use cases employing our custom metric endpoint, referred to [here](../../coralogix-endpoints.md) as the **Otel endpoint**, including serverless computing and quick cURL-like calls to send counters and gauges to Coralogix.

[View this GitHub repo](https://github.com/coralogix/custom-metrics-examples) for SDK examples of the custom metrics endpoint for grpcurl, Java, and Go.

## Data Model

Coralogix metrics follow the Prometheus data model, and metrics can be one of the following: Counter, Gauge, and Histogram (Read more about the [Prometheus data model](https://prometheus.io/docs/concepts/data_model/)).

The Custom Metric API implementation is based on the stable OpenTelemetry Metric [Spec](https://github.com/open-telemetry/opentelemetry-proto/blob/main/opentelemetry/proto/metrics/v1/metrics.proto).

Here's a sample of both a counter and a gauge:

```json
{
  "resource_metrics": {
    "scope_metrics": {
      "metrics": [{
        "name": "grpc_sample_gauge1",
        "gauge": {
          "data_points": [{
            "as_double": 0.8,
            "attributes": [{
              "key": "service.name",
              "value": {
                "string_value": "test-service"
              }
              }],
              "start_time_unix_nano": 1657079957000000000,
              "time_unix_nano": 1657079957000000000
          }]
        }
      },{
        "name": "grpc_sample_counter1",
        "gauge": {
          "data_points": [{
            "as_int": 100,
            "attributes": [{
              "key": "service.name",
              "value": {
                "string_value": "test-service"
              }
              }],
              "start_time_unix_nano": 1657079957000000000,
              "time_unix_nano": 1657079957000000000
          }]
        }
      }]
    }
  }
}
```

\* Currently both timestamps as well as `service.name` are mandatory.

## Sending Data with grpcurl

gRPC is a modern way of calling APIs on top of HTTP/2. Similar to cURL, [grpcurl](https://github.com/fullstorydev/grpcurl) is a command-line tool used to communicate with gRPC services.

Coralogix currently supports gRPC for its custom metrics endpoint.

Assuming the example in the data model is saved as "sample.json", the following command will send it to Coralogix:

``` bash
grpcurl -v -d @ -H 'Authorization: Bearer e0cxxxx-xxxx-xxxx-xxxx-xxxa08b' <custom-metrics-endpoint> opentelemetry.proto.collector.metrics.v1.MetricsService/Export <sample.json
```

- For `<custom-metrics-endpoint>`, input the Coralogix **[OpenTelemetry endpoint](../../coralogix-endpoints.md)** associated with your Coralogix domain.

- For the Authorization key, input your Coralogix [](../../../user-guides/account-management/api-keys/send-your-data-api-key/index.md)**[Send-Your-Data API key](../../../user-guides/account-management/api-keys/send-your-data-api-key/index.md)**.

## Using Java

While there are many OpenTelemetry SDKs in this tutorial we will be using Java.

In order to get started quickly with OpenTelemetry SDK and Coralogix, follow this sample project.

Add to your maven pom.xml the following libraries:

``` xml
<dependency>
  <groupId>io.opentelemetry</groupId>
  <artifactId>opentelemetry-sdk-metrics</artifactId>
</dependency>
<dependency>
  <groupId>io.opentelemetry</groupId>
  <artifactId>opentelemetry-exporter-otlp</artifactId>
</dependency>
```

Code snippet to generate a counter and a gauge:

``` java
SdkMeterProvider meterProvider = 
      SdkMeterProvider.builder()
        .registerMetricReader(
          PeriodicMetricReader.builder(
            OtlpGrpcMetricExporter.builder()
              .setEndpoint("<custom-metrics-endpoint>")
              .addHeader("Authorization", "Bearer e0cxxxx-xxxx-xxxx-xxxx-xxxa08b")
          .build())
        .build())
      .build();
                
    Meter meter = meterProvider.meterBuilder("test").build();
    
    LongCounter counter = meter
      .counterBuilder("otlp_test_counter1")
      .setDescription("Processed jobs")
      .build();

    counter.add(
      100l, 
      Attributes.of(AttributeKey.stringKey("service.name"), "my-test-service")
    );
    
    meter
      .gaugeBuilder("otlp_test_gauge1")
      .buildWithCallback(measurement -> {
        measurement.record(0.8, Attributes.of(AttributeKey.stringKey("service.name"), "my-test-service"));
      });

    meterProvider.forceFlush();
```

\* Currently the `service.name` attribute is mandatory on each metric.

## Limits & Quotas

Coralogix places the following limits on endpoints:

- A **hard limit of 10MB** of data to our **OpenTelemetry** **endpoint**, with a **recommendation of 2MB**

- A **hard limit of 2411724 bytes** of data to our **Prometheus RemoteWrite** **endpoint**, with a **recommendation** for any amount less than this limit

Limits apply to single requests, regardless of timespan.

## Additional Resources
| | |
| --- | --- |
| Documentation | [Coralogix Endpoints](../../coralogix-endpoints.md) |
| GitHub | [GitHub Repo](https://github.com/coralogix/custom-metrics-examples) |

## **Support**

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at **[<EMAIL>](mailto:<EMAIL>)**.
