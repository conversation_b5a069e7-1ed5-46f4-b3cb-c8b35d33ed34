---
title: "Syslog using OpenTelemetry"
date: "2023-03-02"
coverImage: "Logs-Screen-1000X1000.webp"
description: "This tutorial demonstrates how to use custom syslog to send your logs to Coralogix using OpenTelemetry."
---

This tutorial demonstrates how to use custom syslog to send your logs to Coralogix using [OpenTelemetry](../../../opentelemetry/getting-started/index.md).

## Overview

Syslog is a standard for message logging. It allows separation of the software that generates messages, the system that stores them, and the software that reports and analyzes them. Each message is labeled with a facility code, indicating the type of system generating the message, and is assigned a severity level.

When there is no support for custom syslog, an intermediate server is required in order to send the data to the Coralogix account.

## Prerequisites

- Server to install [OpenTelemetry](../../../opentelemetry/getting-started/index.md)

- Static public IP allocated to the server for initial configuration

## Deployment

**STEP 1**. **[Install](https://opentelemetry.io/docs/collector/getting-started/)** OpenTelemetry on your server.

**STEP 2**. Create a configure file.

``` yaml
receivers:
  syslog:
    tcp:
      listen_address: "0.0.0.0:514"
    protocol: rfc5424
    operators:
      - type: syslog_parser
        protocol: <**message_format>**
        parse_from: body
        parse_to: body
			- type: remove
        field: attributes
exporters:
  coralogix:
    domain: "<coralogix_domain>"
    private_key: "private_key"
    application_name: "applicationName"
    subsystem_name: "subsystemName"
    timeout: 30s
service:
  pipelines:
    logs:
      receivers: [ syslog ]
      exporters: [ coralogix ]

```

Replace the following values.

| Value | Description |
| --- | --- |
| applicationName | [Application name](../../../user-guides/account-management/account-settings/application-and-subsystem-names/index.md) to be displayed in your Coralogix dashboard |
| subsystemName | [Subsystem name](../../../user-guides/account-management/account-settings/application-and-subsystem-names/index.md) to be displayed in your Coralogix dashboard |
| coralogix_domain | Your [Coralogix domain](../../../user-guides/account-management/account-settings/coralogix-domain/index.md) |
| private_key | Your [Coralogix Send-Your-Data API key](../../../user-guides/account-management/api-keys/send-your-data-api-key/index.md) |
| message_format | The syslog message format ( rfc3164/rfc5424 ) |

**Notes**:

- `port 514` is the default port for Syslog.

- To change to this port, modify the OpenTelemetry configuration should be changed accordingly.

**STEP 3**. Save the configure file.

## Additional Resources
| | |
| --- | --- |
| Documentation | [Syslog](../syslog/index.md) |

## **Support**

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
