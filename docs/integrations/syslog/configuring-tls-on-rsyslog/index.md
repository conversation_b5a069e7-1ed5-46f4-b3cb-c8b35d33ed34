---
title: "Configuring TLS on rsyslog"
date: "2024-06-06"
description: "This document explains how to configure TLS on rsyslog, which is necessary for sending logs to Coralogix syslog endpoints."
show_blocks:
  - domain_selector
---

## Introduction

This document explains how to configure rsyslog to support TLS connections, which is necessary for sending logs to Coralogix syslog endpoints.

## Prerequisites

- Ubuntu 22.04 (or newer)

## Configuration

**STEP 1**. Install the `gtls driver` and `ca-certificates`:

``` bash
sudo apt update -y
sudo apt install rsyslog-gnutls ca-certificates
```

**STEP 2**. Modify the rsyslog configuration file:

``` 
$DefaultNetstreamDriver gtls $DefaultNetstreamDriverCAFile /etc/ssl/certs/ca-certificates.crt $ActionSendStreamDriverMode 1 $ActionSendStreamDriverAuthMode x509/name $ActionSendStreamDriverPermittedPeer *.coralogix.com $template CoralogixSyslogFormat,"{\"fields\": {\"private_key\":\"xxxx\",\"application_name\":\"vagrant\",\"subsystem_name\":\"syslog\"},\"message\": {\"message\":\"%msg:::json%\"}}\n" *.* @@{{ endpoints.syslog }};CoralogixSyslogFormat
```

**STEP 3**. Restart rsyslog:

``` bash
sudo systemctl restart rsyslog
```

## Troubleshooting

**STEP 1**. Check the status of the rsyslog service:

``` bash
sudo systemctl status rsyslog
```

**STEP 2**. Check for any errors in the journalctl logs:

``` bash
sudo journalctl -f -u rsyslog
```

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by emailing [<EMAIL>](mailto:<EMAIL>).
