---
title: "Slack Audit Logs"
date: "2023-01-17"
description: "Easily collect and analyze your Slack audit logs in Coralogix using our seamless Slack Audit Logs integration package."
---
# Slack Audit Logs

Easily collect and analyze your Slack audit logs in Coralogix using our seamless **Slack Audit Logs** integration package.

Slack audit logs provide comprehensive records of actions across all your workspaces on Enterprise Grid. This enables your organization to maintain security, prevent misuse, and meet compliance standards. By centralizing these logs in Coralogix, you can optimize your Slack environment while gaining actionable insights and real-time monitoring capabilities.

## Prerequisites

Before starting the integration, ensure you have a [Slack Enterprise](https://slack.com/enterprise) account and are an [Owner](https://slack.com/help/articles/*********-Permissions-by-role-in-Slack) of an Enterprise Grid organization.

## Required permissions

To set up this integration in Coralogix, your account must have the following permissions:

| **Resource** | **Action** | **Description** | **Details** |
| --- | --- | --- | --- |
| `integrations` | `ReadConfig` | View Deployed Integrations | Access information about deployed integrations. |
| `integrations` | `Manage` | Manage Integrations | Add, remove, or update integration packages. |

Learn more about Coralogix roles and permissions [here](../../../user-guides/account-management/user-management/create-roles-and-permissions/index.md).

## Setup

**STEP 1: Navigate to the Slack Audit Logs integration**

From the Coralogix toolbar, go to **Data Flow** > **Integrations** and select **Slack Audit Logs**.

**STEP 2: Add a new integration**

Click **Add New** and provide the following information:

- **Integration Name**
- **Application and Subsystem**: These define metadata for the logs sent to Coralogix. Learn more [here](../../../user-guides/account-management/account-settings/application-and-subsystem-names/index.md).

Click **Create**.

**STEP 3: Authorize Coralogix with Slack**

Click **Authorize** to install the Coralogix app. You will be redirected to Slack's authorization page. Follow the prompts and click **Allow** to grant permissions.

| **Requested OAuth Scope**   | **Reason** |
|-----------------------------|------------|
| [openid](https://api.slack.com/scopes/openid) |  Allows the app to connect Slack user account with the Coralogix account and enables to determine additional user details. |
| [auditlogs:read](https://api.slack.com/scopes/auditlogs:read) | Grants the app permission to read audit events from all workspaces, channels, and users, and ingest them as logs to coralogix. |

## Managing your integrations

Once set up, your integration will appear on the Slack Audit Logs management screen. Use the right-hand icons to:

- Edit existing integrations.
- Delete integrations no longer in use.

## Monitoring your audit logs

You may monitor your Slack audit logs in [Explore](../../../user-guides/monitoring-and-insights/logs-screen/logs-screen/index.md) or [Custom Dashboards](../../../user-guides/custom-dashboards/getting-started/index.md).  

## Support

Need assistance? Our support team is available 24/7 to help you set up and troubleshoot any issues.

Reach out to us **via in-app chat** or email us at [<EMAIL>](mailto:<EMAIL>).
