---
title: "Open Commerce API"
date: "2022-08-01"
description: "Coralogix provides an easy way to collect your Open Commerce OrderSearch API logs. The preferred and easiest integration method will be to use our app in the AWS Serverless Application Repository."
---

Coralogix provides an easy way to collect your Open Commerce OrderSearch API logs.  
The preferred and easiest integration method will be to use our app in the [AWS Serverless Application Repository](https://eu-central-1.console.aws.amazon.com/serverlessrepo/home?region=eu-central-1#/available-applications).

## Requirements

- Your AWS user should have permissions to create lambdas and IAM roles.

## Installation

- Navigate to the [Application Page](https://eu-central-1.console.aws.amazon.com/lambda/home?region=eu-central-1#/create/app?applicationId=arn:aws:serverlessrepo:eu-central-1:597078901540:applications/Coralogix-ocapi).

- Fill in the required parameters.

- Click Deploy.

Once the deployment is done the lambda will execute every X minutes based on the ‘lambda schedule’ parameter.

## Parameters and Descriptions

| Variable | Description |
| --- | --- |
| Application name | The stack name of this application created |
| NotificationEmail | Failure notification email address |
| ApplicationName | Application Name in Coralogix |
| CoralogixRegion | The Coralogix region [EU1, EU2, US1, US2, AP1 (India), AP2 (Singapore)] associated with your [Coralogix domain](../../../user-guides/account-management/account-settings/coralogix-domain/index.md) |
| FunctionArchitecture | Lambda function architecture [x86_64, arm64] |
| FunctionMemorySize | Lambda function memory limit |
| FunctionSchedule | Lambda function schedule in minutes, the function will be invoked each X minutes. After deploy, first invocation will be after X minutes. |
| FunctionTimeout | Lambda function timeout limit |
| LogsToStdout | Send logs to stdout/cloudwatch. Possible values are `True`,`False` |
| OcapiEndpoint | The full endpoint to the orderSearch API |
| OcapiPassword | The OCAPI password used for authentification |
| OcapiUsername | OCAPI username. used to get authenticated. |
| PrivateKey | Coralogix [Send-Your-Data API key](../../../user-guides/account-management/api-keys/send-your-data-api-key/index.md) |
| SelectStatement | The select statement to be used in the query. Default to `(*)` |
| SubsystemName | Subsystem name in Coralogix |
