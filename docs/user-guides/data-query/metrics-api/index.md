---
title: "Metrics API"
date: "2022-10-27"
coverImage: "Prometheus.png"
description: "Coralogix provides a Metrics API that lets you query your hosted metrics easily."
show_blocks:
  - domain_selector
---

## Overview

Coralogix provides a **Metrics API**,. granting you access to all metrics you’ve sent to Coralogix. This API supports two primary use cases: querying metrics data programmatically and acting as a PromQL-compatible query data source (e.g., as a [Grafana Prometheus data source](https://grafana.com/docs/grafana/latest/datasources/prometheus/)).

## Authentication

Each HTTP request must include an `Authorization` header with a value `Bearer <API-Key>`. Configure a [customized API key](../../../user-guides/account-management/api-keys/api-keys/index.md) with the `DataQuerying` role preset or `Query Metrics` permission: `metrics.data-api#high:ReadData`. For more details, refer [here](../../../user-guides/account-management/api-keys/api-keys/index.md).

Alternatively, you may use the `Legacy Logs Query API Key`.

## Querying metrics data programmatically

### Prometheus-compatible API request

Include the following in your API request:

| Header | Authorization: `Bearer <API-Key>` |
| --- | --- |
| URL | Select the [**Metrics endpoint**](../../../integrations/coralogix-endpoints.md) associated with your Coralogix [**domain**](../../../user-guides/account-management/account-settings/coralogix-domain/index.md) |

### Examples

Ensure you update your Coralogix [domain](../../../user-guides/account-management/account-settings/coralogix-domain/index.md) and API key in the following examples.

**Instant Metric Query**

Evaluates an instant metric at a defined single point in time.

``` bash
curl --location --request GET '{{ endpoints.metrics.metrics }}/api/v1/query?query=vector(1)' --header 'Authorization: Bearer <API-Key>'

```

## PromQL-Compatible Query Data Source

### Grafana Prometheus Data Source

[Configure a Prometheus data source](https://grafana.com/docs/grafana/latest/datasources/prometheus/configure-prometheus-data-source/) in Grafana:

**STEP 1**. Navigate to **Home** > **Connection** > **Add new connection**. Enter **Prometheus data source**.

**STEP 2**. Click **Add new data source**.

**STEP 3**. Input the following fields:

**Connection**

- Prometheus server URL: `{{ endpoints.metrics.metrics }}` (Input your [Coralogix domain](../../../user-guides/account-management/account-settings/coralogix-domain/index.md) into the [Metrics endpoint](../../../integrations/coralogix-endpoints.md#metrics))

**Authentication**

- **HTTP headers**:
    - Header: `Authorization`
    
    - Value: `Bearer <API-Key>` (as mentioned in the **Authentication** section above)

**Advanced Settings**

- **Performance**
    - Prometheus type: `Prometheus`
    
    - Prometheus version: `2.24.x`

**STEP 4**. Click **Save & test**. You should receive a message confirming a successful query to the Prometheus API.

Find out more [here](https://grafana.com/docs/grafana/latest/datasources/prometheus/configure-prometheus-data-source/).

## API support

### Supported APIs

The following are examples of **supported** APIs for HTTP methods GET and POST.

| API | Description |
| --- | --- |
| `/api/v1/query` | Instant query |
| `/api/v1/query_range` | Range query |
| `GET /api/v1/label/<label_name>/values` | Querying label values |
| `GET /api/v1/labels   POST /api/v1/labels` | Getting label names |
| `GET /api/v1/series   POST /api/v1/series` | Finding series by label matchers |

Details on how to use supported APIs can be found [here](https://prometheus.io/docs/prometheus/latest/querying/api).

### Unsupported APIs

The following are examples of **unsupported** APIs for HTTP methods GET and POST.

| API | Description |
| --- | --- |
| `GET /api/v1/alerts` | Returns a list of all active alerts |
| `/api/v1/query_exemplars` | Query exemplars for a specific series over time |
| `GET /api/v1/rules` | Returns a list of alerting and recording rules |
| `GET /api/v1/targets/metadata` | Returns metadata about metrics from targets |

## Additional resources
| | |
| --- | --- |
| External Documentation | [Prometheus API](https://prometheus.io/docs/prometheus/latest/querying/api/) |

## Support

**Need help?**

Our world-class customer success team is available 24/7 to assist you with your setup and answer any questions.

Contact us via our in-app chat or by emailing [<EMAIL>](mailto:<EMAIL>).
