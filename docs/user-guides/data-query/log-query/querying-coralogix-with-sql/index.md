---
title: "Querying Coralogix with SQL"
date: "2021-01-25"
coverImage: "Quering-Coralogix-with-SQL-1000X1000.png"
description: "Java Database Connectivity (JDBC) is a common standard for database drivers, and many popular querying tools support it. In this tutorial, you'll find instructions on using the Coralogix JDBC driver with the popular tools DataGrip, DBeaver, and Tableau."
---

The Coralogix JDBC driver allows you to investigate your log data using SQL queries with your favorite database tool. With the Coralogix JDBC driver, you can quickly perform SQL queries against the data already stored in your Coralogix account.

Java Database Connectivity (JDBC) is a common standard for database drivers, and many popular querying tools support it. In this tutorial, you'll find instructions on using the Coralogix JDBC driver with the popular tools DataGrip, DBeaver, and Tableau.

!!! Note
    This API can be used to query high tier data only.    

## Getting started

Follow these steps to set up the connection to Coralogix:

1. Download the [latest driver](https://opensearch.org/docs/latest/search-plugins/sql/sql/jdbc/).

2. To use this API you need to [create](../../../account-management/api-keys/api-keys/index.md) a personal or team API key. It’s recommended to use permission presets, as they are automatically updated with all relevant permissions. Alternatively, you can manually add individual permissions.

    | Preset | Action | Description |
    | --- | --- | --- |
    | DataQuerying | `LEGACY-ARCHIVE-QUERIES:EXECUTE` <br> `LOGS.DATA-API#HIGH:READDATA` <br> `METRICS.DATA-API#HIGH:READDATA` <br> `SPANS.DATA-API#HIGH:READDATA` | Query Data from the Archive <br> Query Frequent Search Logs <br> Query Metrics <br> Query Frequent Search Spans | 

3. Follow the steps below for client-specific instructions. Other clients should have a similar procedure for installing the driver.

4. Test the connection using a simple query: `SELECT * FROM logs LIMIT 5`

## DataGrip

1. Click on `+` icon in the Database menu and choose `Driver`

2. Into `Name` field write `Coralogix`

3. Click on `+` under `Driver Files` and pick the driver file you downloaded earlier (Getting Started step #1 above)

4. Open `Class` picker and pick `org.opensearch.jdbc.Driver`

5. Click on `Apply` then `OK`

6. Click on `+` icon in the database menu -> choose `Data Source` -> choose `Coralogix`

7. Replace `<cx_api_key>` with the generated API key value in the JDBC URL below as per your Team's cluster location.

8. In the `General` tab, change the URL as follows:`jdbc:opensearch://<sql_endpoint>/<cx_api_key>`. Select the [SQL endpoint](../../../../integrations/coralogix-endpoints.md#sql) associated with your Coralogix [domain](../../../account-management/account-settings/coralogix-domain/index.md).

9. Click on `Apply` and `OK`.

## DBeaver

1. In the top menu select `Database` -> `Driver manager` and click the `New` button

2. In `Driver Name` field, enter `Coralogix`.

3. In the Libraries tab, click `Add File` button and pick the driver file you downloaded earlier (Getting Started step #1 above)

4. Click the `Find Class` button. Select the `org.opensearch.jdbc.Driver` in the `Driver Class` field.

5. Click `OK`.

6. Click `Close`.

7. Click `Database/New Database Connection`. Make sure that **All** is selected.

8. Type `coralogix` into the search box and choose `Coralogix` driver. Click `Next`.

9. Replace `<cx_api_key>` with the generated API key value in the JDBC URL below as per your Coralogix [domain](../../../account-management/account-settings/coralogix-domain/index.md).

10. Set `JDBC URL` to `jdbc:opensearch://<sql_endpoint>/<cx_api_key>`. Select the [SQL endpoint](../../../../integrations/coralogix-endpoints.md#sql) associated with your Coralogix [domain](../../../account-management/account-settings/coralogix-domain/index.md).

11. Click Test Connection to ensure it all works.

12. Click `OK`.

## Tableau

1. Download the [cx_sql_jdbc.taco](https://github.com/coralogix/tableau_jdbc_connector/blob/main/cx_sql_jdbc.taco) connector file, and copy it to: 
    - Windows: `C:\\Users\\<USER>\\Documents\\My Tableau Repository\\Connectors` 
    - MacOS: `~/Documents/My Tableau Repository/Connectors`

2. Place the OpenSearch JDBC driver (jar file) downloaded earlier (Getting Started step #1 above) into: 
    - Windows: `C:\\Program Files\\Tableau\\Drivers` 
    - MacOS: `~/Library/Tableau/Drivers`

3. Run the Tableau Desktop with the command line flag `DDisableVerifyConnectorPluginSignature=true`: 
    - Windows: `C:\\Program Files\\Tableau\\Tableau 2022.1\\bin\\tableau.exe" -DDisableVerifyConnectorPluginSignature=true` 
    - MacOS: `open -n /Applications/Tableau\\ Desktop\\ 2022.1.app --args -DDisableVerifyConnectorPluginSignature=true`  
    
    Adjust the command line according to the Tableau version you have. You can create a shortcut or a script to simplify the above step.

4. Replace `<cx_api_key>` with the generated API key value in the JDBC URL below as per your Team's cluster location.

5. Open Tableau, and select to a Server > Coralogix by Coralogix.

6. Set the `JDBC URL` to: `jdbc:opensearch://<sql_endpoint>/<cx_api_key>`. Select the [SQL endpoint](../../../../integrations/coralogix-endpoints.md#sql) associated with your Coralogix [domain](../../../account-management/account-settings/coralogix-domain/index.md).

## Additional resources
| | |
| --- | --- |
| API | [Direct Archive Query HTTP API](../../../../developer-portal/apis/data-query/direct-archive-query-http-api/index.md) |
| External | [OpenDistro SQL](https://opendistro.github.io/for-elasticsearch-docs/docs/sql/sql-full-text/) |

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us via our in-app chat or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
