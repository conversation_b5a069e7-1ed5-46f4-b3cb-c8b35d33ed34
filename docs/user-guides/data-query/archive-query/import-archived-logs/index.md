---
title: "Import Archived Logs"
date: "2019-01-07"
coverImage: "<EMAIL>"
---

## Description

Coralogix allows you to import the data [you archived](https://coralogix.com/tutorials/archive-s3-bucket-forever/) in an easy and structured way. Simply use our proprietary Lambda function and have your archived logs back in your system in minutes.

To choose what to archive:

1. Go to your Coralogix archive bucket, data should be stored there per the day and hour of the log entries.

3. Choose the relevant hour to import and place it in a separate bucket.

5. Note - Imported logs count as part of your daily quota.

## Usage

Make sure you add the private_key environment variable when using this lambda.

1. Create an "author from scratch" **Node.js 12.0** runtime lambda with an S3 read permissions 

![configure lambda function](images/lambda_function_screen.png)

2. At "Code entry type" choose "Upload a ZIP file" and upload "s3ToCoralogix.zip"

[https://s3-eu-west-1.amazonaws.com/coralogix-public/tools/s3ArchivedReader.zip](https://s3-eu-west-1.amazonaws.com/coralogix-public/tools/s3ArchivedReader.zip)

3. Add the _private_key_ environment variable.

4. Choose the S3 bucket you want to get triggered by and change the event type from "PUT" to "Object Created(All)"

![S3 bucket for coralogix](images/choosing-S3-bucket-1024x388.png)

5. Click **'Save'**

You're all set!

## **Support**

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at **[<EMAIL>](mailto:<EMAIL>)**.
