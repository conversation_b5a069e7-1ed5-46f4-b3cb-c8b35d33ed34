---
title: "Data Usage Metrics"
date: "2023-05-18"
description: "Enable Data Usage Metrics for an added layer of granularity in your data usage overview. Use it to create custom dashboards, insights, alerts, and useful summaries of your data."

---

Enable **Data Usage Metrics** for an added layer of granularity in your [data usage](../data-usage/index.md) overview. Use this feature to create custom dashboards, insights, alerts, and useful summaries of your data.

![](images/Data-Usage-1-1024x887.png)

## Overview

The Data Usage Metrics feature creates three new metrics: GB Sent, Counted Units, and Daily Quota. These metrics are counted towards your team’s daily metrics quota. With these new metrics, you will be able to create custom dashboards, insights, alerts, and an overview of your data.

## Enabling Data Usage Metrics

Enable the data usage metrics feature after having [configured your s3 metrics bucket](../../../data-flow/s3-archive/connect-s3-archive/index.md).

Once enabled, Coralogix begins an auto-populate process to provide data for the past 24 hours. This process may take up to two hours.

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            In the navigation bar, click **Settings** > **Data Usage**.
            
            ![](images/Data-Usage-Metrics-Support-1024x859.png)
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            On the Data Usage page, toggle **Enable data usage metrics**.

            - If you do not have a required S3 metrics bucket configured, the toggle will be disabled.
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            View your new data usage metrics using our [custom dashboards](../../../custom-dashboards/getting-started/index.md). Metric names will appear as:

            - `cx_data_usage_units` 
            - `cx_data_usage_bytes_total`
            - `cx_data_plan_units_per_day`
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Use the data usage metrics to define useful alerts to monitor your data consumption and quota.

## Labels

The information gathered on the new metrics includes the following labels:

| Label                | Description                                                                                 |
|----------------------|---------------------------------------------------------------------------------------------|
| pillar               | Type of traffic (logs, spans, metrics) in each data point                                   |
| subsystem_name       | Subsystem that generated the traffic (used by the logs and spans pillars)                   |
| application_name     | Application that generated the traffic (used by the logs and spans pillars)                 |
| priority             | TCO priority (high, medium, low, blocked)                                                   |
| severity             | Log severity (critical, error, warn, info, debug, trace) used by the logs pillar            |
| blocking_reason_type | The reason why traffic was blocked. The value can be either `tco_policy` or `parsing_rule`. |
| blocking_reason_name | The name of the TCO policy or parsing rule which is blocking traffic.                       |

## Additional Resources
|     |                                                                                                                                                      |
|-----|------------------------------------------------------------------------------------------------------------------------------------------------------|
| API | [Data Usage](../data-usage/index.md)<br/>[Data Usage Service API](../../../../developer-portal/apis/data-management/data-usage-service-api/index.md) |

## **Support**

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
