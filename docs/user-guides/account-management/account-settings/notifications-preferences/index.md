---
title: "Notifications Preferences"
date: "2018-03-04"
coverImage: "Notification-settings.png"
---

Coralogix offers the users event-driven notifications such as user-defined alerts, a daily email for the team users summarizing top errors as well as if new Error/Critical logs were introduced, and detection of new anomaly notifications. This post will help you manage your notification preferences in order to get the relevant emails for you.

## Enable/Disable Notifications

### Alerts

when [creating an alert](https://coralogix.com/tutorials/coralogix-user-defined-alerts/), under **Recipients** you will define who should receive a notification once the alert has triggered. You can add/subtract addressee by its email address or choose a webhook integration from the drop-down (if any webhook integration was defined beforehand).

![coralogix user defined alerts recipients section](images/Screen-Shot-2021-11-27-at-1.20.57-1024x650.png)

### Reports

Under **Settings** --> **Notifications**, you can configure 4 reports to be sent to you automatically, either by email or Slack.

The 4 reports are:

- Notify on new Error and Critical logs - Auto generated report that summarizes the top error los and new error logs that were introduced to the system - read more [here](../../../monitoring-and-insights/anomaly-detection/new-error-and-critical-logs-anomaly/index.md)

- Notify on Flow Anomalies - Notifies on anomaly detected in the flow ratio of specific types of logs - read more [here](../../../monitoring-and-insights/anomaly-detection/flow-anomaly/index.md)

- Notify on Spike Anomalies - Notifies on a spike in error logs - read more [here](../../../alerting/incidents/index.md)

- Subscribe to Data Usage - alerts regarding daily quota of the team (data consumption warning, blocking alert)

If you already created any Slack [webhook integration](https://coralogix.com/tutorials/alert-webhooks/), you will be able to attach them to any of the Coralogix reports. Note that **Slack Notification Settings** is available only for account administrators.

You can add a filter in order to receive a notification customized to you based on the application/subsystem that is of interest to you.

If you wish not to receive any of these notifications you can uncheck the relevant checkbox.

![Notification settings Coralogix](images/Screen-Shot-2021-11-27-at-1.26.23-1024x559.png)
