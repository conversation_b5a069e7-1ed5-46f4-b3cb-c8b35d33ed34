---
title: "Assign User Roles &amp; Scopes via Groups"
date: "2024-01-04"
description: "Coralogix users enjoy role-based access based on their membership in one or more Groups, each of which is assigned one or more roles."
---

Streamline user management, including role-based and data scope access, via **Groups**.

## Overview

Coralogix users from one organization are joined together in teams based on their project, department, or other relevant criteria. An organization may have multiple teams, each consisting of multiple users. Each team is a platform environment with a unique URL and settings.

Team members are attached to one or more [groups](./index.md), through which they are assigned roles and permissions, as well as data scopes. For example, you may have a Development Team with users from Engineering, Operations, and Support groups, each with different roles and data scopes.

Groups allow administrators to [grant permissions and manage data access](../create-roles-and-permissions/index.md) granularly by assigning team members to groups that align with their organization’s structure and requirements.

### Roles

[Roles](../create-roles-and-permissions/index.md) categorize users and define their account permissions, determining which actions they may perform and on which resources. By default, Coralogix offers seven predefined system roles. You can create custom roles to customize between your users and their permissions by adding permissions to existing system roles.

### Scopes

[Data scopes](../scopes/index.md) enable users to see only data that is relevant to them or that they are allowed to see. A data scope represents a subset of the overall data within your environment, defined by an entity scope query.

## Required permissions

Users with the following permissions may view and/or manage groups.

| Resource      | Action       | Description              | Explanation                                                 |
|---------------|--------------|--------------------------|-------------------------------------------------------------|
| `team-groups` | `ReadConfig` | View Team-Level Groups   | View groups, membership, and permissions.                   |
| `team-groups` | `Manage`     | Manage Team-Level Groups | Create a group and control user membership and permissions. |

## Create a group

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Access your **settings** in the upper right-hand corner of the Coralogix toolbar.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            In the left-hand sidebar, click **Groups**. A list of existing groups will appear.
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Click + **Group**.
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Enter the new group details.

            ### Name & description
            
            Enter a name and description for the group.
            
            ### Select role
            
            Select an existing role for users in the group or [create a new one](../create-roles-and-permissions/index.md).
            
            ### Select scope
            
            Select an existing data scope for the group's users or [create a new one](../scopes/index.md). Groups not assigned a data scope have access to all data.
            
            ### Add members
            
            Add additional members of your organization to the group.
    ::: custom-list-item marker=5
        ::: list-item-header

        ::: list-item-content
            Click **CREATE**.

## Modify an existing group

To edit a group, click the pen icon in the **Actions** column of the Groups catalog. To delete a group, click the garbage icon.

To change a role or data scope for an existing group, click the pen icon, then select alternatives from the **Select Role** and **Select Scope** drop-down menus. Click **SAVE**.

## Additional resources
|               |                                                                                                  |
|---------------|--------------------------------------------------------------------------------------------------|
| Documentation | [Roles & Permissions](../create-roles-and-permissions/index.md)<br/>[Scopes](../scopes/index.md) |

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to contact us **via our in-app chat** or by emailing [<EMAIL>](mailto:<EMAIL>).
