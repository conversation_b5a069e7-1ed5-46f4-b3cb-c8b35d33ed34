---
title: "SSO with SAML"
date: "2020-10-24"
coverImage: "SAML.png"
---

Coralogix provides full SAML 2.0 support so you can integrate with your chosen IdP and manage your Coralogix users' SSO login in a centralized way. Here you can find the walkthrough process for integrating with the common IdPs in the market, don't hesitate to contact us via the chat bubble within our web app if you have any questions or comments.

!!! note

    - If you intend to follow this integration with a SCIM one, add the users through SCIM and make sure that any existing users are deleted before
      undertaking the SCIM integration. If necessary, you may leave one admin user.

    - Upon completion of the SCIM integration, recreate all users through SCIM.

**Common IdPs for SAML Integration:**

- [Integrate with Google as a SAML IdP](#integrate-with-google-as-a-saml-idp)
- [Integrate with OneLogin as a SAML IdP](#integrate-with-onelogin-as-a-saml-idp)
  - [EU1 region](#eu1-region)
  - [Non-EU1 Region Users](#non-eu1-region-users)
- [Integrate with <PERSON><PERSON> as a SAML IdP](#integrate-with-okta-as-a-saml-idp)
- [Integrate with Azure as a SAML IdP](#integrate-with-azure-as-a-saml-idp)
- [Integrate with JumpCloud as a SAML IdP](#integrate-with-jumpcloud-as-a-saml-idp)
  - [Login with SAML](#login-with-saml)
- [**Integrate Keycloak SSO with Coralogix using SAML**](#integrate-keycloak-sso-with-coralogix-using-saml)
  - [**Prerequisites**](#prerequisites)
  - [**Keycloak configuration**](#keycloak-configuration)
  - [**Coralogix configuration**](#coralogix-configuration)

## Integrate with Google as a SAML IdP

View dedicated Google instructions [here](https://support.google.com/a/answer/9089758?hl=en&ref_topic=6304947).

## Integrate with OneLogin as a SAML IdP

### EU1 region

These instructions apply only to EU1 [region](../../account-settings/coralogix-domain/index.md)\-based users. Integration instructions for all other regions can be found in the following section.  

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Navigate to your administration panel and navigate to applications. Click on **Add App** button and choose **Coralogix**.
            
            ![coralogix one login saml](images/one1.png)
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Choose a name to display (default is Coralogix), add a description if you'd like, and **Save.**

            ![coralogix one login saml display](images/one2-1024x513.png)
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Coralogix also supports IdP-initiated flow enabling direct connection to Coralogix from your Onelogin **App portal**. To enable, once our connector is saved, go to **Configuration** and insert your Coralogix account company ID (in your Coralogix account, go to **Settings > Send your data**) into the RelayState dialog box. Click **Save**.

            ![coralogix one login saml relay state config](images/one3-1024x517.png)
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Download the SAML Metadata XML file.

            ![coralogix one login saml download xml metadata](images/one4-1024x517.png)
    ::: custom-list-item marker=5
        ::: list-item-header

        ::: list-item-content
            Upload the metadata file to our web app via **Settings -> Configure SAML**.

            ![Upload xml metadata in Coralogix](images/Screen-Shot-2021-11-27-at-0.52.13-1024x543.png)

### Non-EU1 Region Users

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Navigate to **Applications** and select **Add App**.
            
            ![](images/Screenshot_2023-03-23_at_09_42_20.png)
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Search for and select **SAML Test Connector (IdP)**.

            ![](images/Screenshot_2023-01-26_at_12_38_45.png)
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Go to **Configuration** and add the details according to the following example. This particular example is for an EU2-based team.

            ![](images/Screenshot_2023-01-26_at_12_31_46-2.jpg)
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Click **Save**. The newly prepared XML configuration may be uploaded to the relevant Coralogix team.

## Integrate with Okta as a SAML IdP

!!! note
    We **strongly recommend** setting up your Okta SSO using SCIM rather than SAML.

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Create a new SAML 2.0 app integration. In Okta, navigate to **Applications** > **Create App Integration** > **SAML 2.0**. Click **Next**.
            
            ![](images/Untitled-2024-06-19T081901.831.png)
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            In General Settings, enter the **App name** used by Okta to display the application name to users (e.g., "Coralogix Production").

            ![](images/Untitled-2024-06-19T081912.035.png)
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            In Coralogix, navigate to **Settings** > **Configure SAML**.

            ![](images/Untitled-2024-06-19T081917.657.jpg)
            
            ![](images/Untitled-2024-06-19T081923.081.png)
            
            - Single sign-on URL > Assertion Consumer Service URL
            - Audience URI (SP Entity ID) > Service Provider Entity ID/Audience
            - Default RelayState > `<Company ID or Name of your Coralogix team>`
            - Name ID Format should be set to EmailAddress
            
            Once complete, click **Next** and complete the last stage. Click **Finish**.
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Scroll down to SAML Signing Certificate, click on the **Actions** drop-down menu and select "View IdP metadata". Copy the metadata into a text file and save it as .xml.

            ![](images/Untitled-2024-06-19T081927.944.png)
    ::: custom-list-item marker=5
        ::: list-item-header

        ::: list-item-content
            Returning to the Coralogix SAML configuration, you must upload the file you just created by clicking **Choose file** and selecting it from where it was saved.

            ![](images/Untitled-2024-06-19T081932.451.png)
            
            **Note**: You can change the **Default Groups on first Sign-in** to the group that you want to be assigned to users by default.

## Integrate with Azure as a SAML IdP

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Enter _https://portal.azure.com/_.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            After logging into Azure, go to **Azure Active Directory** tab.
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Select **Enterprise applications** service.
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Choose 'New application'.
    ::: custom-list-item marker=5
        ::: list-item-header

        ::: list-item-content
            Choose 'Non-gallery application'.
    ::: custom-list-item marker=6
        ::: list-item-header

        ::: list-item-content
            Name it (for example, CoralogixSSO) and click **Add**.
    ::: custom-list-item marker=7
        ::: list-item-header

        ::: list-item-content
            Go to **Configure single sign-on**.
    ::: custom-list-item marker=8
        ::: list-item-header

        ::: list-item-content
            Select **SAML-based Sign-on** as the SSO mode.  
            
            ![saml_based_sign_on azure config](images/1-2.png)
    ::: custom-list-item marker=9
        ::: list-item-header

        ::: list-item-content
            Next, you will need to add Coralogix service provider details to the configuration in Azure as follows:  
            From Coralogix web app, go to **Settings -> Configure SAML**. Configure the following:  
                \* Service Provider Entity ID/Audience   
                \* Assertion Consumer Service URL  
            In the Azure portal (example paths):  

            ![set_sso_values_azure](images/2-2.png)
    ::: custom-list-item marker=10
        ::: list-item-header

        ::: list-item-content
            If you would like to log in to a specific Coralogix team or account from your Azure SSO app directly, add the Coralogix team name to the `Relay State` option, as part of your basic SSO configuration.

            ![](images/Screenshot-2024-01-02-at-7.10.26 PM-********-134039-1.jpg)
    ::: custom-list-item marker=11
        ::: list-item-header

        ::: list-item-content
            Choose 'user.mail' as the value for **User Identifier**.  

            ![user_identifier_sso_config](images/3-3.png)
    ::: custom-list-item marker=12
        ::: list-item-header

        ::: list-item-content
            At the bottom of the page in step 5, click **Configure CoralogixSSO**.
    ::: custom-list-item marker=13
        ::: list-item-header

        ::: list-item-content
            Scroll down to step 3 of the '**Configure CoralogixSSO** for SSO section, and download the file named _SAML XML Metadata_.
    ::: custom-list-item marker=14
        ::: list-item-header

        ::: list-item-content
            Scroll up to the top of the **SSO Configuration** section and click **Save**.
    ::: custom-list-item marker=15
        ::: list-item-header

        ::: list-item-content
             Upload the metadata file to Coralogix web app via **Settings > Configure SAML**.

            ![Upload xml metadata in Coralogix](images/Screen-Shot-2021-11-27-at-0.52.13-1024x543.png)
    ::: custom-list-item marker=16
        ::: list-item-header

        ::: list-item-content
             Click the **Multiple teams** button to create a unique ID for the team.

## Integrate with JumpCloud as a SAML IdP

Find instructions [here](https://jumpcloud.com/support/integrate-with-coralogix).

### Login with SAML

After SAML has been activated for your account you may access it using SSO. 

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Enter Coralogix.com --> Login and insert your team name.
            
            ![](images/coralogix_sso_team_screen.png)
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            In the next window, choose SSO login (for example, SIGN IN WITH GOOGLE) to log as a user.

            ![](images/coralogix_sso_login_screen.png)
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            If the username you used to log in was used with Coralogix in the past (for example, the administrator wanted to block the access and removed the username), then it will be required by the administrator to approve it. In that case a request was sent to your administrator, wait for his invitation email.

            ![](images/coralogix_sso_approval_screen.png)
            
            Join request received by the administrator:
            
            ![coralogix sso request sent to admin](images/3-1.png)
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Administrator panel after receiving join request from a user, click **Approve** to send an invitation to the user.

            ![coralogix admin panel receiving invite request](images/Screen-Shot-2021-11-27-at-1.10.32-1-1024x560.png)
            
            Invitation sent to you:
            
            ![coralogix sso join request to user](images/4.png)
            
            Now you can enter Coralogix with SAML SSO.
            
            ![](images/coralogix_sso_login_screen.png)
    ::: custom-list-item marker=5
        ::: list-item-header

        ::: list-item-content
            Finalize any additional configuration or verification needed on Coralogix’s side.

            Keycloak is an open-source identity and access management solution offering single sign-on (SSO) capabilities. It allows users to authenticate across multiple applications using a single set of credentials. Integrating Keycloak with Coralogix via SAML enhances security, streamlines user authentication, and centralizes identity management. This integration enables users to securely access Coralogix with their existing Keycloak credentials, simplifying access control and enhancing the overall user experience.

## **Integrate Keycloak SSO with Coralogix using SAML**

Follow the steps below to successfully set up the SAML integration between Keycloak and Coralogix.

### **Prerequisites**

Configure the following entities and parameters.

- **Keycloak:**
    - Operational Keycloak server
    
    - Realm ready for the integration
    
    - Users in the realm

- **Coralogix:**
    - Coralogix team
    
    - Coralogix team URL
    
    - Coralogix team ID number (**Settings > Send Your Data**)
    
    - Service Provider Metadata URL (**Settings > Configure SAML**)
    
    - Assertion Consumer Service URL (**Settings > Configure SAML**).
    
    - Client Certificate PEM, see details below

### **Keycloak configuration**

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            In the Keycloak admin console, create a new realm or use an existing one for the integration.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Export SAML 2.0 identity provider metadata.
            
            - In the Keycloak admin console, navigate to **Configure > Realm Settings**.
            - Click **SAML 2.0 Identity Provider Metadata**.
            - Save the metadata as an XML file to be used for Coralogix configuration later.
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Create a new client.
            
            - Go to **Clients > Clients List** and click **Create**.
                - **Client type:** SAML
                - **Client ID:** `<your_CX_team_service_provider_metadata_URL>`
                - **Name:** Coralogix (or any other meaningful name)
                - **Description** (optional)
            - Configure client settings.
                - **Root URL:** `<your_CX_team_URL>`
                - **Home URL:** `<your_CX_team_URL>`
                - **Valid redirect URIs:** `<your_assertion_URL>`
                - **Valid post logout redirect URIs:** `<your_CX_team_URL>`
                - **IdP-initiated SSO URL name:** leave empty
                - **IDP Initiated SSO Relay State:** `<your_team_ID_number><Your Team ID number>`
                - **Master SAML Processing URL:** `<Your CX Team Service Provider Metadata URL>`
            - Adjust SAML capabilities.
                - After saving your changes, go to the **Settings** tab.
                - Under SAML capabilities, change the **Name ID format** to **email**.
            - Configure signature and encryption.
                - In the Signature and Encryption section, enable **Sign assertions**.
            - Replace the default certificate.
                - Go to the **Keys** tab.
                - Verify that the **Client signature required** option is enabled.
                - Replace the existing certificate with the PEM certificate (public key) provided by Coralogix.
                - **Important**: This certificate contains the public key for Coralogix, used in SAML integrations to verify the identity of Coralogix and ensure secure, authenticated communication.

            ``` 
            -----BEGIN CERTIFICATE-----
            MIIDeTCCAmGgAwIBAgIUeDB+CHuqR0rEP0InQE3TN0fJjDMwDQYJKoZIhvcNAQEL
            BQAwTDELMAkGA1UEBhMCICAxCjAIBgNVBAgMASAxEjAQBgNVBAoMCUNvcmFsb2dp
            eDEdMBsGA1UEAwwUU0FNTCBTU08gQ2VydGlmaWNhdGUwHhcNMjQwNTMxMTUwMTU2
            WhcNMjUwNTMxMTUwMTU2WjBMMQswCQYDVQQGEwIgIDEKMAgGA1UECAwBIDESMBAG
            A1UECgwJQ29yYWxvZ2l4MR0wGwYDVQQDDBRTQU1MIFNTTyBDZXJ0aWZpY2F0ZTCC
            ASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKeTRA3lRNXyaZtzIaDvaA1V
            x4B4qOPRF+795pY4UICzPvSScJFDqLcaxw2jpgrJ+sGCalo2V/2tvTX2rxke/qw0
            W+91dhgCbgZZRORNdAUBtK6wVX4fpB3tSVf/X14diazkYX6Tt9JV36WOB/Dg91Ys
            AqsbdSHJe8okJ9eodCFoL4tZrVC8+C2n2mdz9KsYBo8BhMl9arR5oxxX2rL22oBZ
            7/5yqXKt1Z0664HPw5aAoP6jhpiAR69r84RI9ijtViErtc3u1JptYIDNMb6FeSxx
            2CsoHrotasUWdq5j3Ly9ymIxWyfTmAJnk40tGLb58C2JkFZNy1rJ/qdmexpRAckC
            AwEAAaNTMFEwHQYDVR0OBBYEFMPWQqrERDm+dFBbZiwcKOAg+hafMB8GA1UdIwQY
            MBaAFMPWQqrERDm+dFBbZiwcKOAg+hafMA8GA1UdEwEB/wQFMAMBAf8wDQYJKoZI
            hvcNAQELBQADggEBAJunYmEf4iELSC2ZCTuqij6Id5MLgSwpEwwScdaJwvI+qaVc
            lJlaI8NNOblX76iOoRQg+JehF11p99BpRGGPGZdS56C0gUoXkpHLse5OeC9zfnwl
            svdVZ+BKXUYTdKSNLkckr/+kl5gIngfh8WsfLfa99+oQXl5xFmhSo8G6gFT4yKGP
            JHhMa65/fyWiPRIz4d9xOo2oUml92NGyedoi7pmijx8ZLJeiNr43PVhoWKfem8ZJ
            5bAdDXvYl1Ga4Uw88lEUqnqwTIdfHQCIesr0+9X86LgimfEXnqg7q5EdZUXONQip
            Gzd5gGpfOFukQeUfxqtDjBZNXqgdpDMK+3RSy8w=
            -----END CERTIFICATE-----
            
            ```

            - Configure roles.
              - Go to **Roles** and create a new default role.
              - Add the necessary users/groups to this role.
            - Save the client configuration.

### **Coralogix configuration**

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            In Coralogix platform, navigate to **Settings > Account > Configure SAML** and activate SAML.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Upload the Keycloak Identity Provider Metadata XML file that was exported earlier.
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Finalize any additional configuration or verification needed on Coralogix’s side.
