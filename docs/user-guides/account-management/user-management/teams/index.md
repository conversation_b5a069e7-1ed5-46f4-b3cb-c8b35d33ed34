---
title: "Teams"
date: "2021-09-14"
coverImage: "role-access-1.png"
description: "Manage your users and their associated roles and permissions across Coralogix teams."
---

## Overview

A **Coralogix Team** is a platform environment with its unique URL, settings, and [Send-Your-Data API key](../../api-keys/send-your-data-api-key/index.md). Teams are used to group users together based on their project, department, or any other relevant criteria. Teams provide a convenient way to collectively manage permissions and settings for a group of users. You can assign teams to specific logs, alerts, or dashboards, ensuring that the right people can access the relevant information. Users may view data only of those teams of which they are members.

Teams can consist of users from different [Groups](../assign-user-roles-and-scopes-via-groups/index.md), allowing you to create flexible and dynamic access controls. For example, you can have a Development Team with users from the Engineering, Operations, and Support groups. This allows you to [grant permissions and manage access](../create-roles-and-permissions/index.md) granularly, aligning with your organization's structure and requirements.

## Create a Team

If you haven't already done so, [sign up](https://dashboard.eu2.coralogix.com/#/signup) for a free Coralogix account. You will be prompted to create a new team. Input a team name and click **CREATE TEAM**.

If you already have an account, click **CREATE NEW TEAM** in your login screen.

![new team](images/new_team-2-1.png)

Alternatively, click **\+ CREATE NEW TEAM** in the upper right-hand corner of your Coralogix dashboard.

![](images/Screen-Shot-2023-06-25-at-15.16.43.png)

## Manage Existing Team Members

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Access your settings in the upper-right hand corner of your Coralogix dashboard.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            In the left-hand sidebar, click **Team Members**. A list of existing team members will appear.
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            **Search** existing team members. You may filter your search according to member roles.
            
            ![](images/Screen-Shot-2023-06-25-at-14.52.14-1024x171.png)
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            **Administrators** (admins) may add team members, remove them, or change their [permissions](../create-roles-and-permissions/index.md) by clicking on the drop-down menu right of the user's name.  
            
            **Notes**:
            
            - Users can be assigned to more than one team.
            - Users may view data only of those teams of which they are members.
            - Upon logging in, users may select the team within which they would like to work.
            - Each team has its own unique [Send-Your-Data API key](../../api-keys/send-your-data-api-key/index.md).

## SSO Login

For instructions on how to set up a single sign-on (SSO) with your IDP, follow our tutorial [here](https://coralogix.com/tutorials/sso-with-saml/).

**Notes**:

- If your admin configures a SAML SSO, no password is necessary for you to sign in to Coralogix.

- Only the admin is authorized to change the password if an SSO is enabled.

## Session Length Management

Team administrators can define the **duration of idle sessions** for all users in the team. Enabling this option will end all current login sessions and require users to log in again. Find out more [here](../../account-settings/session-length/index.md).

## Role-Based Access Control

**Role-based access control** **(RBAC)** allows account administrators to grant some or all team members specific [application and subsystem](../../account-settings/application-and-subsystem-names/index.md) data scope permissions for logs and traces, as well as action permissions.

- **RBAC for logs**. Find out more [here](../create-roles-and-permissions/index.md).

- **RBAC for traces**. Find out more [here](../create-roles-and-permissions/index.md).

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
