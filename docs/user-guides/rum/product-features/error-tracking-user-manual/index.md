---
title: "Error Tracking: User Manual"
date: "2023-11-07"
description: "As part of our Real User Monitoring (RUM) toolkit, Coralogix offers multi-faceted Error Tracking. Designed to help web application owners and developers gain insights into errors occurring within their users' browsers, this tool allows you to effectively capture and analyze frontend errors to optimize application performance and enhance the overall user experience. This user manual demonstrates how to engage with our RUM UI to get the most out of our Error Tracking features."
---

![](images/Header-1024x754.png)

As part of our [Real User Monitoring](../../getting-started/real-user-monitoring/index.md) (RUM) toolkit, Coralogix offers multi-faceted Error Tracking. Designed to help web application owners and developers gain insights into errors occurring within their users' browsers, this tool allows you to effectively capture and analyze frontend errors to optimize application performance and enhance the overall user experience. Get an overview of all errors generated from your browser or view related errors in a consolidated manner. You can view errors as individual errors (**Error Screen**) or aggregated into groups (**Error Template View**).

This user manual demonstrates how to engage with our RUM UI to get the most out of our **Error Tracking** features.

## Get Started

Once you have [configured and installed our browser SDK](../../sdk-installation/javascript/npm-browser/index.md), in your Coralogix toolbar, navigate to **RUM** \> **Browser Error Tracking**.

## Error Screen

Get an overview of all of the errors generated by your browser. View the number of errors and users affected by each, and filter according to your needs. In addition, create alerts directly from this screen by clicking CREATE ALERT in the upper right-hand corner.

### Display

The Error Screen includes:

1. **Filters.** Refine your view by filtering errors according to particular attributes.

3. **Error Overall Graph and KPIs**. This graph presents the error trend in the application and the overall number of errors and users affected.

5. **Error Grid.** Use the error table grid to view error information, row by row. Modify columns and the information presented based on your specific interests.

![](images/1-1-2.png)

## Group Related Errors (Error Template View)

In addition to a granular view of all errors, take advantage of our **Error Template View**, where similar errors with shared attributes are grouped into a single template for quick and easy analysis. Find out more [here](../error-template-view/index.md).

![](images/Untitled-5-1.png)

## Investigate Specific Errors

Drill down and investigate a specific error by clicking on an error of interest. The Specific Error Screen includes:

1. **Error Header.** A header bar with information about the error selected.

3. **Error Graph.** This time series graph presents the frequency of the specific error that has been chosen.

5. **Information Grid**.
    - Click on the **OVERVIEW** tab to acquire additional information surrounding the error, including event ID, type, group by, error status, browser, device type, language, country, and labels.
    
    - Depending on the error type, you will presented with a **STACK TRACE** tab, a **DEPENDENCIES VIEW** tab, **BREADCRUMBS VIEW** tab or a **CORRELATE LOGS** tab.
        - **STACK TRACE** allows you to drill down into a specific stack trace tab. It is presented when the error is an **unhandled exception** - an error in a computer program or application when the code has no appropriate handling exceptions.
        
        - **DEPENDENCIES VIEW** allows you to visualize all the services in the error flow and pinpoint specific servers that may have caused the error. It is presented when a **network error** has occurred.

        - **BREADCRUMBS VIEW** lists all events captured during user’s journey through an application leading up to a specific point, such as an error.
        
        - **CORRELATE LOGS** enables users to view detailed logs associated with errors, providing additional contextual information to aid debugging.

7. **Similar Errors Grid**. View and easily move between identical errors received in a selected time period.

![](images/2-1024x690.png)

### Stack Trace

Drill-down and investigate the stack trace of an error by clicking on the **STACK TRACE** tab.

![](images/3-1024x690.png)

### Dependencies View

Visualize all the services in the error flow and pinpoint specific servers that may have caused the error by clicking on the **DEPENDENCIES VIEW** tab.

![](images/4-1024x690.png)

### Breadcrumbs View

Breadcrumbs view lists all events captured during user’s journey through an application leading up to a specific point, such as an error. Use this view to understand the context and sequence of actions that resulted in an issue, making it easier to perform root-cause analysis of an issue and troubleshoot the problem.
In the Breadcrumbs tab and examine the following information:

- Timestamp. A record of the time of event occurrence. Click it to display events in the ascending or descending order.

- Event type. Click it to group events by type.

    - User actions (click, input, etc.)

    - XHR (request URL, request method, etc.)

    - Errors (error message, error type, etc.)

    - Navigation (page URL, navigation type, etc.)

    - Console Logs (log message, log type, etc.)

- Severity. Color-coded severity indication of the event. Click it to group events according to their severity.

- URL. Exact location of the event occurrence.

- Details. Additional information about the event. For example, type of the button clicked, XHR type and response time, etc.

You can also use the **Search** field to search for a particular entry, filter events by type via the **Filter** drop-down or click any event to display detailed information about it.

![](images/breadcrumbs-view.png)

### Correlate Logs

View detailed logs associated with errors, providing additional contextual information to aid debugging by clicking on the **CORRELATE LOGS** tab.

![](images/RUM-Related-Logs-1024x690.png)

## Add an Alert to Track an Error

![](images/RUM-Alerting-1024x829.png)

Alerting refers to the practice of setting up automated notifications or alarms that trigger when certain predefined conditions or thresholds are met. These conditions can be related to the performance, health, or behavior of the systems, applications, or infrastructure being monitored. The main purpose of alerting is to promptly notify DevOps teams and developers – when something unusual or problematic occurs, so you can take appropriate actions to resolve the issue before it escalates.

For more detailed information on Coralogix Alerts, see [Getting Started with Coralogix Alerts](../../../alerting/introduction-to-alerts/index.md).

Coralogix enables you to create [alerts](../../../alerting/introduction-to-alerts/index.md) directly from the Error Tracking screen, with several different available options for alert creation.

- **Create a custom alert directly from the error tracking screen.** Click the **CREATE ALERT** button on the right-hand side of the screen. The blank alert creation panel appears on the right-hand side of the screen for you to create your alert.

![](images/1-1024x690.png)

- **Create an alert from a specific error.** Click on the three dots on the left-hand side of the error number and select **CREATE ALERT**. The alert creation panel appears with the specified error details already in the query.

![](images/3-1-1024x690.png)

- **Create an alert from a specific template.** Click on the three dots on the left-hand side of the template number and select **CREATE ALERT**. The alert creation panel appears with the specified template details already in the query.

![](images/4-2-1024x690.png)

- **Create an alert from a template or error drill-down page.** Click on the **CREATE ALERT** button on the right-hand side of the screen. The alert creation panel appears with the specified error or template details already in the query.

![](images/2-1-1024x690.png)

## Additional Resources
| | |
| --- | --- |
| Documentation | [Real User Monitoring](../../getting-started/real-user-monitoring/index.md)<br/>[Error Tracking](../error-tracking/index.md)<br/>[Error Template View](../error-template-view/index.md)<br/>[Browser SDK Installation Guide](../../sdk-installation/javascript/npm-browser/index.md) |

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
