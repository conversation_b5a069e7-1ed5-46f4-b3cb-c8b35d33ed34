---
title: "Session Replay"
date: "2024-04-18"
---

Optimize user experience monitoring by recording and replaying user sessions with **Session Replay**.

![](images/Header-2.png)

## Overview

Creating exceptional user experiences is paramount in the fast-paced software development world. To achieve this, developers must understand their users' interactions, identify pain points, and ensure seamless functionality.

Session Replay enhances your user experience monitoring by enabling you to record and visually replay the web browsing activities of your users. When combined with [Real User Monitoring](../../getting-started/real-user-monitoring/index.md) (RUM) performance data, Session Replay is invaluable for pinpointing, tracking, and resolving errors. It also provides insights into your web application's usage patterns and design flaws.

## How it works

The Session Replay recorder is part of the RUM Browser SDK, whether [NPM](../../sdk-installation/javascript/npm-browser/index.md) or [CDN](../../sdk-installation/javascript/cdn-browser/index.md).

The recording tool captures a snapshot of the browser’s DOM by actively monitoring and logging events occurring on a web page, including but not limited to DOM modifications, mouse movements, clicks, input events, and their respective timestamps.

Coralogix then reconstructs the web page and re-implements the recorded events at the appropriate moments in the replay view.

## Configuration

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Ensure that the following URLs are allowed in your Content Security Policy (CSP):
            
            | CSP                          | URL                                                                                                                                                                                                                        |
            |------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
            | Script Source (script-src)   | `https://cdnjs.cloudflare.com/ajax/libs/pako/`                                                                                                                                                                             |
            | Connect Source (connect-src) | `https://ingress.<eu1 | eu2 | us1 | us2 | ap1 | ap2>.rum-ingress-coralogix.com`<br>Select the **[domain](../../../account-management/account-settings/coralogix-domain/index.md)** associated with your Coralogix account. |
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Configure Session Replay as follows:

            ```
            CoralogixRum.init({
              // ...
              sessionRecordingConfig: {
                enable: true, // Must declare.
                /**
                 * If autoStartSessionRecording is false, you can manually start & stop your session recording.
                 * Refer to Recording Manually Section.
                 **/
                autoStartSessionRecording: true, // Automatically records your session when SDK is up.
                recordConsoleEvents: true, // Will record all console events from dev tools. Levels: log, debug, warn, error, info, table etc..
                sessionRecordingSampleRate: 100, // Percentage of overall sessions recording being tracked, defaults to 100% and applied after the overall sessionSampleRate.
              },
            });
            ```

## Configuration options

### Settings

Below are the settings you can configure at the root level of your Browser SDK using `init({})` for Session Replay configuration:

| Key                        | Default            | Description                                                                                                                                                                                                        | Required |
|----------------------------|--------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------|
| blockClass                 | 'rr-block'         | A string or RegExp to redact all elements with this class. These elements will not be recorded but will replay as placeholders with the same dimensions.                                                           |          |
| blockSelector              | null               | A string to specify which selector should be blocked. Select an element (e.g., h1) in the DOM.                                                                                                                     |          |
| ignoreClass                | 'rr-ignore'        | A string or RegExp to ignore all events on elements with this class. Input events on these elements will not be recorded.                                                                                          |          |
| ignoreSelector             | null               | A string to specify which selector should be ignored. Ignore an element (e.g., h1) in the DOM.                                                                                                                     |          |
| ignoreCSSAttributes        | null               | An array of CSS attributes that should be ignored.                                                                                                                                                                 |          |
| maskTextClass              | 'rr-mask'          | A string or RegExp to mask all elements with this class. The text of these elements will be masked with asterisks.                                                                                                 |          |
| maskTextSelector           | null               | A string to specify which selector should be masked. Choose an element in the DOM to mask.                                                                                                                         |          |
| maskAllInputs              | false              | Masks all input content as \* .                                                                                                                                                                                    |          |
| maskInputOptions           | { password: true } | Masks certain types of input as \*. By default, password inputs are masked.                                                                                                                                        |          |
| maskInputFn                | \-                 | Function to customize the masking logic for input content recording. Pass a function to this property. Each time it masks, it calls your function and uses its logic.                                              |          |
| maskTextFn                 | \-                 | Function to customize the masking logic for text content recording. Same as above with text.                                                                                                                       |          |
| recordCanvas               | false              | Whether to record the canvas element. Options: false, true.                                                                                                                                                        |          |
| recordAfter                | 'load'             | If the document is not ready, the recording starts after the specified event fires. Options: DOMContentLoaded, load.                                                                                               |          |
| sampling                   | —                  | Ignore certain interactions/events - such as `mousemove` - to reduce storage size. Sampling code snippets can be found below.<br>If not specified, all sampling options will default to true and will be recorded. |          |
| inlineImages               | false              | Whether to record the content of images.                                                                                                                                                                           |          |
| collectFonts               | false              | Whether to collect fonts as they appear on the website.                                                                                                                                                            |          |
| enable                     | \-                 | Boolean to enable or disable session recording.                                                                                                                                                                    | required |
| autoStartSessionRecording  | \-                 | Boolean to automatically start session recording.                                                                                                                                                                  | required |
| recordConsoleEvents        | false              | Boolean to record console events.                                                                                                                                                                                  |          |
| maxMutations               | 5000               | Upper limit on the number of mutations to process before stopping recording to avoid performance issues.                                                                                                           |          |
| excludeDOMOptions          | {}                 | SlimDOMOptions to remove unnecessary parts of the DOM. If you don't want to record script tags, specify here.                                                                                                      |          |
| sessionRecordingSampleRate | 100                | Percentage of sessions being recorded, applied after the overall sample rate.                                                                                                                                      |          |

### Sampling

Rely on the following examples for your sampling configuration.

This example presents general configuration options.

``` js
CoralogixRum.init({
  //...
  sessionRecordingConfig:{
	  sampling: {
	    // record all mouse movements 
	    // if set to false, no mouse movements will be recorded
	    mousemove: true,
	    
	    // record all mouse interactions
	    // if set to false, no mouse interactions will be recorded
	    mouseInteraction: true,
	    
		   // set the interval of a scrolling event
	    scroll: 150, // does not emit twice in 150ms 
	    //if not declared, all scrolling events will be emitted
	    
	    // set the interval of a media interaction event
	    media: 800, //if not declared, all media events will be emitted
	    
	    // set the timing of record input
	    input: 'last' 
	    // When inputting multiple characters, only record the final input 
	    // Selecting 'all' instead of 'last' will record all typed inputs
		  }
	  }
	});
	

```

This example presents the different mouse interactions that may be configured.

``` js
CoralogixRum.init({
  //...
  sessionRecordingConfig:{
	  sampling: {
	    // Configure which kinds of mouse interaction should be recorded
	    mouseInteraction: {
	      MouseUp: true,
	      MouseDown: true,
	      Click: true,
	      ContextMenu: true,
	      DblClick: true,
	      Focus: true,
	      Blur: true,
	      TouchStart: true,
	      TouchEnd: true,
		    },
		  },
	  }
	});


```

## Privacy and security

To protect your users’ privacy and sensitive information, elements containing specific class names are blocked or masked in recordings, as follows:

``` js
CoralogixRum.init({
  // ...
  sessionRecordingConfig: {
    // ..
    blockClass: 'rr-block', // Use a string or RegExp to redact all elements that contain this class, defaults to rr-block.
    ignoreClass: 'rr-ignore', // Use a string or RegExp to Ignore all events that contain this class, defaults to rr-ignore.
    maskTextClass: 'rr-mask', // Use a string or RegExp to mask all elements that contain this class, defaults to rr-mask.
    maskAllInputs: false, // Mask all input content as * (Default false), refer to Input types.
    maskInputOptions: { password:true } // Mask some kinds of input as *, By Default the SDK masking password inputs.
  },
});
```

For example:

```
<div class="rr-block">Dont record me</div>
```

| Element    | Action                                                                                     |
|------------|--------------------------------------------------------------------------------------------|
| .rr-block  | The element will not be recorded. It will replay as a placeholder with the same dimension. |
| .rr-ignore | The element will not record input events.                                                  |
| .rr-mask   | All text of elements will be masked.                                                       |

## Manage session recordings

### Start and stop recordings

To manually start and stop recordings, use the following code snippet:

```
// To start manually the Session Recording
CoralogixRum.startSessionRecording();

// To stop the Session Recording
CoralogixRum.stopSessionRecording();
```

### Avoid performance impact

Session Replay works by recording incremental DOM changes in your web application. To avoid performance issues, the feature will stop recording if it detects an excessive number of mutations. The default is set to 5,000.

``` js
CoralogixRum.init({
  // ...
  sessionRecordingConfig: {
    // ...
    // According to MutationObserver API, A large number of DOM mutations can negatively impact performance
    maxMutations: 5000
  },
});
```

## Getting Started

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Click on the play icon of a specific user session in the [User Sessions Grid](https://www.notion.so/AWS-ECS-EC2-using-OpenTelemetry-a06a401a74534204a7a9d0f890437b5b?pvs=21) or error in the [Error Grid](../error-tracking-user-manual/index.md).
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            You will be redirected to the **SESSION REPLAY** tab.

### Replay screen

The Replay Screen offers users a dynamic interface to relive recorded sessions with advanced control options, ensuring a seamless and insightful playback experience.

![](images/3-3.png)

- **Play & Pause:** Users can effortlessly control the playback by pausing and resuming the video at their convenience

- **Speed Control:** The replay speed can be adjusted to suit user preferences

- **Interaction Focus:** Skipping directly to stages with user interactions enhances efficiency.

- **0-Second Skip:** The ability to skip forward and backward by 10 seconds offers precise navigation.

### Console

The Console presents users with an immersive environment to explore recorded sessions comprehensively, combining various elements for an enriched understanding of user interactions and application behavior. Observe Console output - your application's internal logs and messages - in sync with user actions.

![](images/2-6.png)

## Data usage

To reduce costs and lessen the network load caused by Session Replay, Coralogix compresses the data before sending it. Coralogix eases the strain on a browser's user interface thread by assigning CPU-heavy tasks, like compression, to a separate web worker.

We apply the following logging pipeline to Session Replay data:

| Logs priority                                                                                            | Sent data | Units |
|----------------------------------------------------------------------------------------------------------|-----------|-------|
| [Low (Compliance)](../../../account-management/tco-optimizer/logs/index.md#compliance-data-low-priority) | 1 GB      | 0.12  |

## Limitations

Session Replay records up to one hour of activity.

## Additional resources

::: additional-content items="id: m4UMXK2tCoQ, title: Session Replay in Coralogix RUM" 


## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to contact us **via our in-app chat** or by emailing [<EMAIL>](mailto:<EMAIL>).
