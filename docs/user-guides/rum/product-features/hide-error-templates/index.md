---
title: "Hide Error Templates"
date: "2024-06-26"
description: "The Real User Monitoring (RUM) dashboard allows you to retrieve data and filter your view to display only items that meet specific criteria. You can use filters and queries to examine specific subsets of RUM experiences on your application, filtering by application, page URL, request status code, error message, or any log field you choose. You can save these filters and queries for future use, allowing you to focus on relevant data."
---

# Hide Error Templates

## Overview

Manage error visibility and minimize user disruption by preventing specific [error templates](../error-template-view/index.md) from appearing in your [Real User Monitoring](../../getting-started/real-user-monitoring/index.md) (RUM) interface.

Hidden errors remain available throughout the rest of the Coralogix platform, ensuring that your monitoring and alerting systems continue to function seamlessly. For instance, if you have alerts configured based on error occurrences, these hidden errors will still be included in the relevant alert queries.

## Hide an error template

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            On your [Error Tracking](../error-tracking-user-manual/index.md) screen, click the check box for one or more [error templates](../error-template-view/index.md) you wish to hide and click **Hide Error**. Alternatively, click on a particular template to navigate to the template drill-down.  Click **Hide Error** in the upper right.
            
            ![](images/image1.png)
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            In the **Hide Error** modal, define the filter parameters for the selected template, specifying the days and hours when the filter should be active. Click **SAVE**.

            ![](images/image2.png)

## Manage hidden error templates

You can manage your hidden error templates by selecting **Hidden Errors** in the **View** dropdown menu. Here, you can view all hidden errors, along with their parameters and statuses. Search and apply filters as needed.

### Edit your settings

To modify the parameters of a hidden error, click on the error and select **Edit**.

### Undo hidden error template settings

To remove a hidden error filter and restore a template’s visibility in your UI, click the checkbox for the relevant template or templates and click **Undo Hide**. 

![](images/image3.png)

## Support

**Need help?**

Our world-class customer success team is available 24/7 to assist with your setup and answer any questions.

Feel free to reach out to us via our **in-app chat** or by emailing us at [<EMAIL>](mailto:<EMAIL>).