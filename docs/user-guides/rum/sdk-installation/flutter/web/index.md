---
title: "Web"
date: "2024-07-15"
description: "The RUM browser SDK can be integrated into the Flutter web framework for telemetry instrumentation, enabling real-time performance monitoring and analytics."
show_blocks:
  - global_category_selector
global_category: [ "web_flutter" ]
---

# Web Flutter

 The RUM browser SDK can be integrated into the Flutter web framework for telemetry instrumentation, enabling real-time performance monitoring and analytics. Learn how to use it with Coralogix's [CDN Browser SDK ](../../javascript/cdn-browser/index.md). 


## Installation

1. Add the following CDN to your `index.html` file:

    ```
    <script src="https://cdn.rum-ingress-coralogix.com/coralogix/browser/latest/coralogix-browser-sdk.js"></script>

    ```

2. Use the Web Flutter SDK in your Dart code as follows.

``` dart
import 'package:flutter/material.dart';
import 'dart:js' as js;

void main() {
runApp(const MyApp());

var rum = js.JsObject.fromBrowserObject(js.context['CoralogixRum']);

rum.callMethod('init', [js.JsObject.jsify({
  environment: 'test',
  application: 'my-app',
  version: '1.0.0',
  public_key: 'my-key-123',
  coralogixDomain: 'EU2',
})]);
}


```

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
