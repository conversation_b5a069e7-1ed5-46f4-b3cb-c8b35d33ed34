---
title: "SvelteKit"
date: "2024-07-15"
description: "The Coralogix RUM SDK is a library (plugin) for SvelteKit that provides telemetry instrumentation. Learn how to integrate with Coralogix's Real User Monitoring (RUM)."
show_blocks:
  - global_category_selector
global_category: [ "web_sveltekit" ]
---

# SvelteKit

The Coralogix RUM SDK is a library (plugin) for SvelteKit that provides telemetry instrumentation. Learn how to integrate with Coralogix's Real User Monitoring (RUM).

## Installation

1. Add the following code to the `layout.svelte` file:

``` js
<script lang="ts">
	import { CoralogixRum } from '@coralogix/browser';
	import { onMount } from 'svelte';
	import '../app.css';

	let { children } = $props();

	onMount(() => {
		CoralogixRum.init({
			public_key: 'my-key-123',
			environment: 'test',
			application: 'my-app',
			version: '1.0.0',
			coralogixDomain: 'EU1'
		});
	});

</script>

{@render children()}
```
