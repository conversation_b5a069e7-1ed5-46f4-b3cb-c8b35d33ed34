---
title: "DOM Navigation Indications"
date: "2024-12-10"
description: "Customize your application’s performance management by capturing a wide range of browser document navigation indications."
---

## Overview
Customize your application’s performance management by capturing a wide range of browser document navigation indications. Collecting DOM navigation data allows you to:

- Identify the component with the longest load time.
- Measure the time required to load or unload a document.
- Pinpoint slow-loading phases to identify bottlenecks.
- Determine when key content becomes interactive or visible.
- Trace issues such as rendering delays or excessive redirects.
- Monitor loading performance across various user devices, browsers, or networks.
- Support SEO optimization efforts.


## DOM navigation attributes
Your browser document navigation indications are stored in the `cx_rum.web_vitals_context` field. For example, `cx_rum.web_vitals_context.domComplete`.

### Examples
```json
"web_vitals_context": {
    "activationStart": 92.10000002384186,
    "domComplete": 583.1999999880791,
    "domContentLoadedEventEnd": 544.1000000238419,
    "domContentLoadedEventStart": 544,
    "domInteractive": 313.9000000357628,
    "id": "v3-1733232401662-9386591953927",
    "loadEventEnd": 583.4000000357628,
    "loadEventStart": 583.1999999880791,
    "name": "LT",
    "navigationType": "navigate",
    "redirectCount": 0,
    "unloadEventEnd": 0,
    "unloadEventStart": 0,
    "url": "https://coralogix.com/",
    "value": 583.4000000357628
}
```

### Supported attributes

| Property | Description |
| --- | --- |
| activationStart | The time between when a document starts prerendering and when it is activated. |
| domComplete | The state in which the document and all sub-resources have finished loading. |
| domContentLoadedEventStart | The time immediately before the DOMContentLoaded event starts. |
| domContentLoadedEventEnd | The time immediately after the DOMContentLoaded event completes. |
| domInteractive | The time when the document becomes interactive. |
| loadEventStart | The time when the load event of the document starts. | 
| loadEventEnd | The time when the load event of the document completes. |
| redirectCount | The number of redirects since the last non-redirect navigation in the current browsing context. |
| type | Type of navigation (navigate, reload, back_forward, or prerender). |
| unloadEventStart | The time immediately before the unload event handler starts. |
| unloadEventEnd | The time immediately after the unload event handler completes. |
| name | The type of the web_vitals_context field, loading time (LT) in this case. |
| value | The loading time of the page. |
| navigationType | Which navigation event triggered the page load event. |


## Support

**Need help?** 

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up. 

Feel free to reach out to us **via our in-app chat** or by sending us an email to [<EMAIL>](mailto:<EMAIL>).