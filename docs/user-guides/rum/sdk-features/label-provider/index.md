---
title: "Label Provider"
date: "2024-04-09"
description: "The RUM Browser SDK captures RUM logs and events and populates their main attributes. Label Provider gives you access to every event or URL collected by the RUM SDK, allowing you to add labels using custom-defined functions and then easily filter and aggregate your data once ingested by Coralogix."
---

The RUM Browser SDK captures RUM logs and events and populates their main attributes. **Label** **Provider** gives you access to every event or URL collected by the RUM SDK, allowing you to add labels using custom-defined functions and then easily filter and aggregate your data once ingested by Coralogix.

## Overview

Take advantage of Label Provider to:

- Add labels to an event based on the event data or its network or page URLs

- Filter data easily once ingested by Coralogix

- Unleash feature mapping to streamline investigation and troubleshooting

For instance, attach a `team-APM` label to each event on your APM page. This allows your team to track and examine only the events relevant to them.

## Configuration

``` js
import { CoralogixRum } from '@coralogix/browser';

const featurePageUrlLabelProvider = new UrlBasedLabelProvider({
  urlType: UrlType.PAGE,
  urlPatterns: [
    {
      regexps: [/apm/],
      labels: { featureGroupId: 'apm' },
    },
  ],
  defaultLabels: {
    featureGroupId: 'unknown-feature-group',
  },
});

const regularExpErrorLabelProvider: GenericLabelProvider = {
  providerFunc: (url, event) => {
    if (event.error_context?.error_message?.includes('Invalid regular expression')) {
      return {
        regular_expression_error: 'true',
      };
    }

    return {};
  },
};

CoralogixRum.init({
  // ...
  labelProviders: [
    featurePageUrlLabelProvider,
    regularExpErrorLabelProvider
  ]
});
```

## Filter with Labels

Once you’ve configured your labels, use them to filter your data and enjoy feature mapping in any of our RUM products.

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by emailing [<EMAIL>](mailto:<EMAIL>).
