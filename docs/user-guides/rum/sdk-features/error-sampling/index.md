---
title: "RUM Conditional Error Sampling"
date: "2024-05-16"
description: "Coralogix allows you to selectively track user sessions with and without errors, minimizing data overhead while focusing on critical issues."
---

Coralogix allows you to selectively track user sessions with and without errors, minimizing data overhead while focusing on critical issues.

## Overview

Coralogix's Real User Monitoring (RUM) allows you to manage the volume of data being sent to our platform. **Conditional Error Sampling** allows you to capture valuable data from errors and [user sessions](https://coralogix.com/docs/user-sessions/) without overwhelming your system with too much information. By selecting a percentage of data to collect, you can ensure that you’re focusing on the most relevant information, balancing visibility with performance.

## Configuration

### Sampling all user sessions

Capture a specific percentage of all user sessions within your application. For instance, if you set the `sessionSampleRate` to 50%, the system will randomly capture 50% of all user sessions, regardless of whether those sessions encounter errors, performance issues, or function normally.  In this case, if there is a total of 80 user sessions, a random sample of 40 user sessions will be captured.

```js
CoralogixRum.init({
  // ...
  sessionConfig: {
    sessionSampleRate: 50, // Percentage of overall sessions being tracked, defaults to 100%
  },
});
```

### Sampling user sessions with errors

!!! note

    The following instrumentation parameters may only be used with this sampling option: `maxRumEvents`, `maxRecordTime`, and `instrumentationsToSend`.

Capture 100% of user sessions that contain errors, ensuring that error-prone sessions are adequately monitored. 

In parallel, this option allows you to send specific instrumentation data for 100% of user sessions, regardless of errors and performance issues.  This allows you, for example, to receive performance metrics such as `loading_time` for all sessions, even when errors do not occur. 

```js
CoralogixRum.init({
  // ...
  sessionConfig: {
    onlyWithErrorConfig: {
      enable: true, // Whether to track only sessions with errors, defaults to false
      maxRumEvents: 5000, // Maximum number of cached RUM events to store in cache up until an error occurs. Defaults to 5000 with max of 20000
      maxRecordTime: 10_000, // Maximum time in milliseconds to store last recorded events in cache before the error occurs. Defaults to 10000(10s) with max of 60000(1m)
      /**
       * Specifies the instrumentation data to be sent immediately for all user sessions, defaulting to web-vitals only.
       * The instrumentation data will only be sent if it is enabled in the main configuration.
       */
      instrumentationsToSend: {
        [CoralogixEventType.WEB_VITALS]: true,
        // more SDK instrumentation can be added
        // [CoralogixEventType.LONG_TASK]: true,
        //...
      },
    },
  },
});
```

**Configuration parameters**

| **Parameter** | **Type** | **Default** | **Description** |
| --- | --- | --- | --- |
| `enable` | Boolean | `False` | When set to true, only sessions with errors will be tracked. |
| `maxRumEvents` | Number | `5000` | Specifies the maximum number of RUM events to store in the cache until an error occurs. This helps in capturing relevant data leading up to the error. Maximum value is 20000. |
| `maxRecordTime` | Number | `10000` | Maximum time in milliseconds to store last record events in cache before an error occurs. Defaults to 10000(10s), max 60000(1m). |
| `instrumentationsToSend` | Object | `{ [CoralogixEventType.WEB_VITALS]: true }` | Defines the instrumentation parameters to send for user sessions. If enabled, these parameters are sent immediately, even if no error has occurred, and they are not part of the cached RUM events. |

### Advanced sampling

Capture 100% of sessions with errors and a percentage of sessions without errors.  For instance, a user may capture all sessions with errors to diagnose issues thoroughly and capture 20% of sessions without errors to maintain a baseline understanding of normal user behavior.

```jsx
CoralogixRum.init({
  // ...
  sessionConfig: {
    sessionSampleRate: 20,
    alwaysTrackSessionsWithErrors: true, 
  },
});
```

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email to [<EMAIL>](mailto:<EMAIL>).