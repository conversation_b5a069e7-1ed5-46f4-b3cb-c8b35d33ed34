---
title: "Configure Your Browser RUM Data Proxy"
date: "2024-05-02"
description: "Set up the RUM Browser SDK to route requests through a proxy."
---

Set up the RUM browser SDK to route requests through a proxy.

## Overview

Proxy configuration enables the routing of requests. Specifying a proxy URL will direct all RUM data to this URL via the POST method. Subsequently, the data must be relayed from the proxy to Coralogix.

### Use case

Ad blocking software or browser privacy tools may identify Coralogix RUM as a third-party agent, blocking it from capturing user data and interactions. To mitigate this, send the user data to a trusted backend proxy server and apply the tracking scripts when relaying the data to Coralogix. This ensures uninterrupted data collection while maintaining user trust. 

## Prerequisites

To ensure successful request forwarding to Coralogix, your proxy must:

- Add an `X-Forwarded-For` header containing the request client IP address for accurate geoIP

- Forward the request to the Coralogix ingress URL using the POST method

- Leave the request body unchanged

- Ensure that your proxy implementation forwards the raw body without conversion

## Relay from the proxy to Coralogix

- The Coralogix route for each request sent to the proxy is available in the request's `cxforward` parameter (e.g., https://www.your-proxy.com/endpoint?cxforward=https%3A%2F%2Fingress.eu1.rum-ingress-coralogix.com%2Fbrowser%2Fv1beta%2Flogs). Utilize it to transmit the data to Coralogix.

```
CoralogixRum.init({
  // ...
  coralogixDomain: 'EU1',
  proxyUrl: '<https://www.your-proxy.com/endpoint>'
});

```

- Include your Coralogix public key as a request header in the following format: `Authorization: Bearer <public-key>`.

## Support

**Need Help?**

Our dedicated customer success team is available 24/7 to guide you through setup and address any inquiries.

Feel free to contact us via our in-app chat or by emailing [<EMAIL>](mailto:<EMAIL>).
