---
title: "Data Privacy and Logging Settings"
date: "2024-04-02"
description: "Censure fields in your RUM logs with data masking to safeguard end-user privacy and prevent sensitive data collection. In addition, determine whether user IP and geolocation data is collected or ignored."
---

# Data Privacy and Logging Settings


## Overview

Coralogix offers privacy features to safeguard sensitive or personal data for organizations of all sizes as part of [Real User Monitoring](../../getting-started/real-user-monitoring/index.md). Mask sensitive clikable elements and fields in your RUM logs via the [RUM Browser SDK](../../sdk-installation/javascript/npm-browser/index.md). This masking ensures that the Coralogix SDK does not capture sensitive data and that it is consequently not transmitted to the backend. The clickable elements include buttons, labels, links, inputs, and options. They will appear in your UI as stars in the log text. In addition, determine whether user IP and geolocation data is collected or ignored.

## Configuration

### Masking UI elements in RUM logs 

User interactions with specific clickable elements can be masked to prevent sensitive data exposure. Use `maskInputTypes` to specify the types of inputs to mask. This defaults to `['password', 'email', 'tel']`. Define any input and modify as desired. Use `maskClass` to specify the class name used to mask any element, string, or RegExp. The default is `cx-mask`.

``` ts
CoralogixRum.init({
  // ...
  maskInputTypes: ['password', 'date'], // will only mask password and date inputs
  maskClass: 'mask-me' // will mask any element with class 'mask-me'
});
```
**Examples** 

Hide the entire button, including its text.

``` ts
<button class="cx-mask">
  <span>Text</span>
</button>
```

Hide the text inside the button (child element), while keeping the button (container element) visible.

``` ts
<button>
  <span class="cx-mask">Text</span>
</button>
```

Apply the masking logic to the user-defined class to mask only the clickable element hierarchy. 

``` ts
<my-button-component class="cx-mask">
   <button>Text</button>
</my-button-component>
```

### User IP and geolocation data collection

Use `collectIPData` to determine whether user IP and geolocation data is collected (`true`, default) or ignored (`false`). 

``` ts
CoralogixRum.init({
  // ...
  collectIPData: true
});
```

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by emailing [<EMAIL>](mailto:<EMAIL>).
