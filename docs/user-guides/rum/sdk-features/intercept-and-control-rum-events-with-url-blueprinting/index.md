---
title: "Intercept & Control RUM Events with URL Blueprinting"
date: "2024-04-09"
description: "The RUM Browser SDK captures RUM logs and events and populates their main attributes. URL Blueprinting gives you access to every event page or network URL collected by the RUM SDK. It allows you to modify its fields using custom-defined functions and then easily aggregate your data once ingested by Coralogix."
---

The RUM Browser SDK captures RUM logs and events and populates their main attributes. **URL Blueprinting** gives you access to every event page or network URL collected by the RUM SDK. It allows you to modify its fields using custom-defined functions and then easily aggregate your data once ingested by Coralogix.

## Overview

Intercepting the RUM events allows you to:

- Modify event page or network URL fields using custom-defined functions

- Aggregate errors RUM events across URLs with different domains, subdirectories, paths, queries, or parameters

For example, URL blueprinting allows you to capture all shoe-related events on an e-commerce site using https://www.store.com/shop/shoes/`<shoe-id>`, without specifying a specific shoe ID.

## Configuration

``` js
import { CoralogixRum } from '@coralogix/browser';

CoralogixRum.init({
  // ...
  urlBlueprinters: {
    pageUrlBlueprinters: [
      (url) => {
        const hostnameParts = new URL(url).hostname.split('.');
        hostnameParts[0] = '{team-id}';
        return 'https://' + hostnameParts.join('.');
        // "https://alpha.company.com" => "https://{team-id}.company.com"
      },
    ],
    networkUrlBlueprinters: [(url) => url.replace('api/v1', '{server}')]
    // "https://path/api/v1/logs" => "https://path/{server}/logs"
  },
});
```

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by emailing [<EMAIL>](mailto:<EMAIL>).
