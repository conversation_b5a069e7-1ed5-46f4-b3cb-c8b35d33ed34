---
title: "Trace Capturing"
date: "2022-06-15"
description: "Specifying URLs for trace header propagation involves defining the specific endpoints in your application where trace headers will be transmitted during requests."
---

## Overview

Specifying URLs for trace header propagation involves defining the specific endpoints in your application where trace headers will be transmitted during requests. This practice is essential for maintaining context in distributed systems and ensuring effective monitoring and debugging. 

## Configuration

### Define URLs for trace header propagation

Coralogix allows you to control the URLs for trace capturing, using the `allowedTracingUrls` parameter. It’s used for first-party URLs only; for third-part URLs, use the `propagateTraceHeaderCorsUrls` parameter. 

!!! note

    When the `allowedTracingUrls` parameter is omitted, the trace header is propagated to all URLs.

**Example**

To propagate the trace header only for URLs that contain the word `alpha`:

``` ts
CoralogixRum.init({
  // ...
  traceParentInHeader: {
    enabled: true,
    options: {
      allowedTracingUrls: [new RegExp('alpha')],
    },
  },
});
```


### Specify backend domain

When the backend domain is different from the app domain, specify the backend domain using the `propagateTraceHeaderCorsUrls` command.

**Example** 

If an app is hosted on `https://app.com` and the backend is hosted on `https://webapi.com`, specify the backend domain as follows.

```jsx
CoralogixRum.init({
  // ...
  traceParentInHeader: {
    enabled: true,
    options: {
      propagateTraceHeaderCorsUrls: [new RegExp('https://webapi.*')],
    },
  },
});
```

## Support

**Need help?** 

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up. 

Feel free to reach out to us **via our in-app chat** or by sending us an email to [<EMAIL>](mailto:<EMAIL>).