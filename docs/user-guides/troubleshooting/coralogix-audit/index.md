---
title: "Coralogix Audit"
date: "2025-01-22"
coverImage: "Log-query-1000X1000-.png"
---

## Overview

Users with audit permissions can utilize Coralogix audit logs to monitor organizational activity within Coralogix, including all user actions and API operations. This tool empowers you to track actions, ensure compliance, and maintain transparency across your team or organization. 

Use Coralogix Audit to: 

- Review infrastructure-related user activities like payment or resource configuration changes.
- Track before-and-after states of any system changes.
- Monitor API operations during specific timeframes — from the API call or request, to the resulting action, and its outcome.
- Create analytics to evaluate activity across your account by product.


## How it works

Audit events from all teams are centralized within a designated audit team. With **Audit v2**, events are logged and delivered to the audit team in real-time as they occur (e.g., when a query is executed or an API operation takes place). These events follow a standardized structure, ensuring efficient querying and compliance-focused analysis.


## Permissions

To view or manage audit team settings, users must have the required permissions:

| **Resource** | **Action** | **Description** |
| --- | --- | --- |
| team-auditing | ReadConfig | View team audit settings |
| team-auditing | UpdateConfig | Manage team audit settings |

Membership in the audit team allows you to view audit events. 


## Setup

### Access Audit

To access Coralogix Audit, navigate to **Settings** > **Audit Account**. 

### Create an audit team

**STEP 1**. Click on **Create new audit team**.

**STEP 2**. Once the team is configured, you can either open the activity of your entire team by clicking **Open audit history** or monitor the activity of a specific user by clicking on **User audit history**.  

![auditv2_create_step2.png](./images/auditv2_create_step2.png)

You will be rerouted to [Explore](../../monitoring-and-insights/logs-screen/logs-in-explore-screen/index.md) to view the relevant audit events in the audit team environment.

## Audit team settings

### Attaching additional teams to an audit team

Once you have created an audit team for your organization, click **Attach** to connect other new teams to the audit team.

![auditv2_attach.png](./images/auditv2_attach.png)

### Change your audit team

Change your audit team by clicking **Change audit account**.


## Migrate from Audit v1 to v2

We **strongly recommend** that existing users migrate to Audit v2 for enhanced functionality. 

- Enable v2 by toggling the switch to activate the new feature.

![auditv2_enable.png](./images/auditv2_enable.png)

- During the transition, v1 logs will continue to be sent to your audit account.
- To filter exclusively v1 events:
    
    **DataPrime**
    
    `source logs | filter $d.audit_schema_version == 'v1'`
    
    **Lucene**
    
    `audit_schema_version:"v1"`
    
- To filter exclusively v2 events:
    
    **DataPrime**
    
    `source logs | filter $d.auditVersion == 'v2'`
    
    **Lucene**
    
    `auditVersion:"v2”`
    
- Once you’ve confirmed v2 is functioning as expected, you can disable v1 logs using the toggle in your settings. Any dashboards, alerts, or features configured with Audit v1 events will cease to function. Users must manually recreate these configurations to ensure functionality with Audit v2.

!!! Note
    - During the transition, both v1 and v2 logs will contribute to your data usage. Be mindful of your audit account quota. Contact your CS representative if you anticipate exceeding your quota.
    - In v2 logs the application name remains unchanged (it’s equal to the `teamName`), but the subsystem name is changed from `service` to `feature group`.


### Billing & usage

The audit team has a daily quota of up to 0.025 GB and a retention period of 7 days.

This quota should generally be sufficient for audit purposes. However, using the [quota management CLI](../../account-management/payment-and-billing/quota-management/index.md), you can increase it or reallocate it between teams if needed.

Configure your [S3 archive](../../data-flow/s3-archive/connect-s3-archive/index.md) to retain audit logs for longer periods. You can query these logs in [Explore](../../monitoring-and-insights/logs-screen/logs-in-explore-screen/index.md) or use [Background Queries](../../data-query/background-queries/index.md) for long-running queries.

## Audit events structure

Events follow a unified structure, comprising an **audit envelope** and an **audit payload**. Payloads are standardized per event type (e.g., `after` for `create`, `before`/`after` for `update`, etc.).

## Audit envelope

The envelope structure consists of the following fields:

| Field | Description |
| --- | --- |
| `auditVersion` | Audit log structure version (e.g., v2, v3, etc.). |
| `eventName` | The name of the event reflects its related features and functionality. |
| `cxFeature` | The specific Coralogix feature the event data is related to. |
| `cxFeatureGroup` | Stable Coralogix feature groups, used as the subsystem name of each audit event. |
| `eventKind` | The type of the event (Action/Update/Delete/Create/Get/List). `eventData` payload structure is determined by this key. |
| `endpointInfo` | Provides connection information, like the protocol used to connect to the system, the IP address that sent the request, and the request protocol. |
| `authDetails` | Authentication details that depend on the type of authentication (API key,  user login, or internal context). |
| `actorDetails` | Details about the actor responsible for the action (organization, user, or team).  |
| `eventData` | The event payload, which contains details about the resources affected by the event and/or the resources themselves. |
| `outcome` | The logical outcome of the actions. Contains the outcome status and failure type if relevant. |

**Example**

```json
 {
    "endpointInfo":{
        "clientInfo":{
            "ipAddress": <ip_address>,
            "userAgent": <user_agent>
        },
        "protocol": "HTTP",
        "httpDetails": {
            "statusCode": 200
        }
    },
    "cxFeatureGroup": "metrics",
    "actorDetails": {
        "actorType": "User",
        "username": <username>,
        "teamId": <team_id>,
        "teamName": <team_name>,
        "orgName": <org_name>
    },
    "auditVersion": "v2",
    "eventData": { 
        ...
    },
    "eventKind": "Action",
    "eventName": "logs-data-api.ReadData",
    "authDetails": {
        "authType": <Api Key>,
        "apiKeyId": <api_key_id>,
        "apiKeyName": <api_key_name>,
        "apiKeyOwnerType": <User>
    },
    "outcome": {
        "status": "Success",
        "statusMessage": "200 OK"
    },
    "cxFeature": "metrics.data-api"
}
```

## Audit payload

### Event type

Each event type has a common payload structure, which is contained in the `eventData` field and is determined by the event type (`eventKind` field).

!!! Note
    Large payloads are converted to strings and contained in the `stringifiedEventData` field for increased indexing efficiency.


- **Action**
    
    Contains complex event data with varying structure.
    
- **List**
    
    Contains a filter field with the resource request.
    
    ```json
    "eventData": {
      "filter": <query>
    }
    ```
    
- **Get**
    
    Contains an identifier of the received resource.
    
    ```json
    "eventData":{
      "identifier": <resource_id>
    }
    ```
    
- **Delete**
    
    Contains the deleted resource.
    
    ```json
    "eventData": {
      "data": {
        "displayName": <display_name>,
        "description": <description>,
        "filters": [
          ...
        ],
        "id": <resource_id>
      }
    }
    ```
    
- **Create**
    
    Contains the created resource.
    
    ```json
    "eventData": {
      "data": {
        "data": { ... },
        "name": <created_resource_name>,
        "id": <resource_id>
      }
    }
    ```
    
- **Update**
    
    Contains the original and new versions of a resource.
    
    ```json
    "stringifiedEventData": {
      "eventType": 7,
      "before": { ... },
      "after": { ... }
      }
    ```
    

### **Event type per feature**

- [Coralogix Actions](../../monitoring-and-insights/logs-screen/coralogix-actions/index.md)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | actions.order | ACTION_EVENT_TYPE_ACTION_ORDER | Action |
    | actions.list | LIST_EVENT_TYPE_ACTION_LIST | List |
    | actions.get | GET_EVENT_TYPE_ACTION_GET | Get |
    | actions.delete | DELETE_EVENT_TYPE_ACTION_DELETE | Delete |
    | actions.create | CREATE_EVENT_TYPE_ACTION_CREATE | Create |
    | actions.update | UPDATE_EVENT_TYPE_ACTION_REPLACE | Update |

- [Alerts](../../alerting/introduction-to-alerts/index.md)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | alert.snooze | ACTION_EVENT_TYPE_ALERT_SNOOZE | Action |
    
    | alerts.list | LIST_EVENT_TYPE_ALERT | List |
    | --- | --- | --- |
    | alerts.get | GET_EVENT_TYPE_ALERT | Get |
    | alerts.delete | DELETE_EVENT_TYPE_ALERT | Delete |
    | alerts.create | CREATE_EVENT_TYPE_ALERT | Create |
    | alerts.update | UPDATE_EVENT_TYPE_ALERT | Update |

- [Alert Scheduler](../../../developer-portal/apis/data-management/alert-suppression-rules-api/index.md)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | alert_scheduler.list | LIST_EVENT_TYPE_ALERT_SCHEDULER | List |
    | alert_scheduler.get | GET_EVENT_TYPE_ALERT_SCHEDULER | Get |
    | alert_scheduler.delete | DELETE_EVENT_TYPE_ALERT_SCHEDULER | Delete |
    | alert_scheduler.create | CREATE_EVENT_TYPE_ALERT_SCHEDULER | Create |
    | alert_scheduler.update | UPDATE_EVENT_TYPE_ALERT_SCHEDULER | Update |

- [API Keys](../../account-management/api-keys/api-keys/index.md)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | api-key.get | GET_EVENT_TYPE_API_KEY | Get |
    | api-key.delete | DELETE_EVENT_TYPE_API_KEY | Delete |
    | api-key.create | CREATE_EVENT_TYPE_API_KEY | Create |
    | api-key.update | UPDATE_EVENT_TYPE_API_KEY | Update |
    | api-keys.list | LIST_EVENT_TYPE_API_KEY | List |
    | team-api-keys-security-settings.update | UPDATE_EVENT_TYPE_API_KEY_SECURITY_SETTING | Update |
    
- [APM](../../apm/getting-started/introduction-to-apm/index.md)
    
    | apm.entity.action | ACTION_EVENT_TYPE_APM_ENTITY | Action |
    | --- | --- | --- |
    | apm.entity.list | LIST_EVENT_TYPE_APM_ENTITY | List |
    | apm.entity.get | GET_EVENT_TYPE_APM_ENTITY | Get |
    | apm.entity.delete | DELETE_EVENT_TYPE_APM_ENTITY | Delete |
    | apm.entity.create | CREATE_EVENT_TYPE_APM_ENTITY | Create |
    | apm.settings.update | UPDATE_EVENT_TYPE_APM_SETTINGS | Update |
    | apm.entity.update | UPDATE_EVENT_TYPE_APM_ENTITY | Update |

- Archive Setup
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | archive-setup.get | GET_EVENT_TYPE_ARCHIVE_SETUP | Get |
    | archive-setup.update | UPDATE_EVENT_TYPE_ARCHIVE_SETUP | Update |

- [Cloud Metadata Enrichment](../../data-transformation/enrichments/aws-resource-enrichment/index.md)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | cloud-metadata-enrichment.delete | DELETE_EVENT_TYPE_CLOUD_METADATA_ENRICHMENT | Delete |
    | cloud-metadata-enrichment.create | CREATE_EVENT_TYPE_CLOUD_METADATA_ENRICHMENT | Create |
    | cloud-metadata-enrichment.update | UPDATE_EVENT_TYPE_CLOUD_METADATA_ENRICHMENT | Update |

- [Custom enrichment](../../data-transformation/enrichments/custom-enrichment/index.md)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | custom-enrichment.overwrite | ACTION_EVENT_TYPE_OVERWRITE_CUSTOM_ENRICHMENTS | Action |
    | custom-enrichment.delete | DELETE_EVENT_TYPE_CUSTOM_ENRICHMENT | Delete |
    | custom-enrichment.create | CREATE_EVENT_TYPE_CUSTOM_ENRICHMENT | Create |
    | custom-enrichment.update | UPDATE_EVENT_TYPE_CUSTOM_ENRICHMENT | Update |
    | custom-enrichment-data.list | LIST_EVENT_TYPE_CUSTOM_ENRICHMENT_DATA | List |
    | custom-enrichment-data.get | GET_EVENT_TYPE_CUSTOM_ENRICHMENT_DATA | Get |
    | custom-enrichment-data.delete | DELETE_EVENT_TYPE_CUSTOM_ENRICHMENT_DATA | Delete |
    | custom-enrichment-data.create | CREATE_EVENT_TYPE_CUSTOM_ENRICHMENT_DATA | Create |
    | custom-enrichment-data.update | UPDATE_EVENT_TYPE_CUSTOM_ENRICHMENT_DATA | Update |

- [Dashboard report](../../custom-dashboards/tutorials/save-a-dashboard-as-a-pdf/index.md)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | dashboard-report.get | GET_EVENT_TYPE_DASHBOARD_REPORT | Get |
    | dashboard-report-schedule.list | LIST_EVENT_TYPE_DASHBOARD_REPORT_SCHEDULE | List |
    | dashboard-report-schedule.get | GET_EVENT_TYPE_DASHBOARD_REPORT_SCHEDULE | Get |
    | dashboard-report-schedule.delete | DELETE_EVENT_TYPE_DASHBOARD_REPORT_SCHEDULE | Delete |
    | dashboard-report-schedule.create | CREATE_EVENT_TYPE_DASHBOARD_REPORT_SCHEDULE | Create |
    | dashboard-report-schedule.update | UPDATE_EVENT_TYPE_DASHBOARD_REPORT_SCHEDULE | Update |

- [Dashboards](../../custom-dashboards/getting-started/index.md)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | dashboards.list | LIST_EVENT_TYPE_DASHBOARD | List |
    | dashboards.get | GET_EVENT_TYPE_DASHBOARD | Get |
    | dashboards.delete | DELETE_EVENT_TYPE_DASHBOARD | Delete |
    | dashboards.create | CREATE_EVENT_TYPE_DASHBOARD | Create |
    | dashboards.update | UPDATE_EVENT_TYPE_DASHBOARD | Update |

- [Data usage](../../account-management/payment-and-billing/data-usage/index.md)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | datausage.get-daily | ACTION_EVENT_TYPE_GET_DAILY_DATA_USAGE | Action |
    | datausage.export | ACTION_EVENT_TYPE_EXPORT_DETAILED_DATA_USAGE | Action |
    | datausage.data-usage-to-metrics-enable | ACTION_EVENT_TYPE_DATA_USAGE_TO_METRICS_ENABLE | Action |

- [Enrichment](../../data-transformation/enrichments/getting-started/index.md)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | enrichment.list | LIST_EVENT_TYPE_ENRICHMENT | List |
    | enrichment.get | GET_EVENT_TYPE_ENRICHMENT | Get |

- [Events2Metrics](../../monitoring-and-insights/events2metrics/index.md)
    
    | events-2-metrics.list | LIST_EVENT_TYPE_EVENTS_2_METRICS | List |
    | --- | --- | --- |
    | events-2-metrics.get | GET_EVENT_TYPE_EVENTS_2_METRICS | Get |
    | events-2-metrics.delete | DELETE_EVENT_TYPE_EVENTS_2_METRICS | Delete |
    | events-2-metrics.create | CREATE_EVENT_TYPE_EVENTS_2_METRICS | Create |
    | events-2-metrics.update | UPDATE_EVENT_TYPE_EVENTS_2_METRICS | Update |

- [Extensions](../../../integrations/extensions/index.md)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | extensions.undeploy | ACTION_EVENT_TYPE_EXTENSION_UNDEPLOY | Action |
    | extensions.update | ACTION_EVENT_TYPE_EXTENSION_UPDATE | Action |
    | extensions.deploy | ACTION_EVENT_TYPE_EXTENSION_DEPLOY | Action |
    | extensions.list | LIST_EVENT_TYPE_EXTENSION | List |
    | extensions.get | GET_EVENT_TYPE_EXTENSION | Get |

- [Forwarders](../../data-flow/forwarders/index.md)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | forwarders.test | ACTION_EVENT_TYPE_FORWARDERS_TEST | Action |
    | forwarders.list | LIST_EVENT_TYPE_FORWARDERS | List |
    | forwarders.get | GET_EVENT_TYPE_FORWARDERS | Get |
    | forwarders.delete | DELETE_EVENT_TYPE_FORWARDERS | Delete |
    | forwarders.create | CREATE_EVENT_TYPE_FORWARDERS | Create |
    | forwarders.update | UPDATE_EVENT_TYPE_FORWARDERS | Update |

- [Geo Enrichment](../../data-transformation/enrichments/geo-enrichment/index.md)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | geo-enrichment.delete | DELETE_EVENT_TYPE_GEO_ENRICHMENT | Delete |
    | geo-enrichment.create | CREATE_EVENT_TYPE_GEO_ENRICHMENT | Create |
    | geo-enrichment.update | UPDATE_EVENT_TYPE_GEO_ENRICHMENT | Update |

- Group
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | group.delete | DELETE_EVENT_TYPE_GROUP | Delete |
    | group.create | CREATE_EVENT_TYPE_GROUP | Create |
    | group.update | UPDATE_EVENT_TYPE_GROUP | Update |

- [Incidents](../../alerting/incidents/index.md)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | incidents.close | ACTION_EVENT_TYPE_INCIDENTS_CLOSE | Action |
    | incidents.assign | ACTION_EVENT_TYPE_INCIDENTS_ASSIGN | Action |
    | incidents.unassign | ACTION_EVENT_TYPE_INCIDENTS_UNASSIGN | Action |
    | incidents.acknowledge | ACTION_EVENT_TYPE_INCIDENTS_ACKNOWLEDGE | Action |
    | incidents.list | LIST_EVENT_TYPE_INCIDENTS | List |
    | incidents.get | GET_EVENT_TYPE_INCIDENTS | Get |

- [Livetail](../../monitoring-and-insights/livetail/index.md)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | livetail.subscribe | ACTION_EVENT_TYPE_LIVETAIL_SUBSCRIBE | Action |

- [Logs TCO](../../account-management/tco-optimizer/logs/index.md)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | logs.tco.test | ACTION_EVENT_TYPE_LOGS_TCO_TEST | Action |
    | logs.tco.order | ACTION_EVENT_TYPE_LOGS_TCO_ORDER | Action |
    | logs.tco.list | LIST_EVENT_TYPE_LOGS_TCO | List |
    | logs.tco.get | GET_EVENT_TYPE_LOGS_TCO | Get |
    | logs.tco.delete | DELETE_EVENT_TYPE_LOGS_TCO | Delete |
    | logs.tco.create | CREATE_EVENT_TYPE_LOGS_TCO | Create |
    | logs.tco.update | UPDATE_EVENT_TYPE_LOGS_TCO | Update |

- [Logs Parsing Rules](../../data-transformation/parsing/log-parsing-rules/index.md)

    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | logs.parsing-rules.order | ACTION_EVENT_TYPE_LOGS_RULES_GROPS_ORDER | Action |
    | logs.parsing-rules.list | LIST_EVENT_TYPE_LOGS_RULES_GROUP | List |
    | logs.parsing-rules.get | GET_EVENT_TYPE_LOGS_RULES_GROUP | Get |
    | logs.parsing-rules.delete | DELETE_EVENT_TYPE_LOGS_RULES_GROUP | Delete |
    | logs.parsing-rules.create | CREATE_EVENT_TYPE_LOGS_RULES_GROUP | Create |
    | logs.parsing-rules.update | UPDATE_EVENT_TYPE_LOGS_RULES_GROUP | Update |

- Logs data API    
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | logs-data-api.ReadData | ACTION_EVENT_TYPE_DATA_API_READ_DATA | Action |

- [Metrics](../../data-query/metrics-api/index.md)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | metrics.data-api.read | ACTION_EVENT_TYPE_METRICS_READ_DATA | Action |

- [Notification Center Presets](../../notification-center/presets/introduction/index.md)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | notification-center-preset.set-as-default | ACTION_EVENT_TYPE_NOTIFICATION_CENTER_PRESET_SET_AS_DEFAULT | Action |
    | notification-center-preset.delete | DELETE_EVENT_TYPE_NOTIFICATION_CENTER_PRESET | Delete |
    | notification-center-preset.create | CREATE_EVENT_TYPE_NOTIFICATION_CENTER_PRESET | Create |
    | notification-center-preset.update | UPDATE_EVENT_TYPE_NOTIFICATION_CENTER_PRESET | Update |

- [Organization](../../account-management/organization-management/create-an-organization/index.md)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | organization.add-domain | ACTION_EVENT_TYPE_ADD_DOMAIN | Action |
    | organization.move-quota | ACTION_EVENT_TYPE_MOVE_QUOTA | Action |
    | organization.settings.get | GET_EVENT_TYPE_ORGANIZATION_SETTINGS | Get |
    | organization.settings.update | UPDATE_EVENT_TYPE_ORGANIZATION_SETTINGS | Update |
    | organization.update | UPDATE_EVENT_TYPE_ORGANIZATION | Update |

- [Webhooks](../../alerting/outbound-webhooks/generic-outbound-webhooks-alert-webhooks/index.md)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | outbound-webhook-types.list | LIST_EVENT_TYPE_OUTBOUND_WEBHOOK_TYPES | List |
    | outbound-webhooks.test | ACTION_EVENT_TYPE_OUTBOUND_WEBHOOK_TEST | Action |
    | outbound-webhooks.list-ibm-ens-instances | ACTION_EVENT_TYPE_OUTBOUND_WEBHOOK_LIST_IMB_ENS_INSTANCES | Action |
    | outbound-webhooks.list | LIST_EVENT_TYPE_OUTBOUND_WEBHOOK | List |
    | outbound-webhooks.get | GET_EVENT_TYPE_OUTBOUND_WEBHOOK | Get |
    | outbound-webhooks.delete | DELETE_EVENT_TYPE_OUTBOUND_WEBHOOK | Delete |
    | outbound-webhooks.create | CREATE_EVENT_TYPE_OUTBOUND_WEBHOOK | Create |
    | outbound-webhooks.update | UPDATE_EVENT_TYPE_OUTBOUND_WEBHOOK | Update |

- [Recording rules](../../data-transformation/metric-rules/recording-rules/index.md)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | recording-rule.delete | DELETE_EVENT_TYPE_RECORDING_RULE | Delete |
    | recording-rule.create | CREATE_EVENT_TYPE_RECORDING_RULE | Create |
    | recording-rule.update | UPDATE_EVENT_TYPE_RECORDING_RULE | Update |
    | recording-rule-group.delete | DELETE_EVENT_TYPE_RECORDING_RULE_GROUP | Delete |
    | recording-rule-group.create | CREATE_EVENT_TYPE_RECORDING_RULE_GROUP | Create |
    | recording-rule-group.update | UPDATE_EVENT_TYPE_RECORDING_RULE_GROUP | Update |
    | recording-rule-group-set.delete | DELETE_EVENT_TYPE_RECORDING_RULE_GROUP_SET | Delete |
    | recording-rule-group-set.create | CREATE_EVENT_TYPE_RECORDING_RULE_GROUP_SET | Create |
    | recording-rule-group-set.update | UPDATE_EVENT_TYPE_RECORDING_RULE_GROUP_SET | Update |

- [Roles](../../account-management/user-management/create-roles-and-permissions/index.md)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | role.list | LIST_EVENT_TYPE_ROLE | List |
    | role.get | GET_EVENT_TYPE_ROLE | Get |
    | role.delete | DELETE_EVENT_TYPE_ROLE | Delete |
    | role.create | CREATE_EVENT_TYPE_ROLE | Create |
    | role.update | UPDATE_EVENT_TYPE_ROLE | Update |

- [RUM](../../rum/getting-started/real-user-monitoring/index.md)

    - [RUM settings](../../rum/getting-started/real-user-monitoring/index.md)
        
        | **Event Name** | **Event Type** | **Field Type** |
        | --- | --- | --- |
        | rum-settings.get | GET_EVENT_TYPE_RUM_SETTINGS | Get |
        | rum-settings.update | UPDATE_EVENT_TYPE_RUM_SETTINGS | Update |
        |  |  |  |

    - SDK versions
        
        | **Event Name** | **Event Type** | **Field Type** |
        | --- | --- | --- |
        | sdk-version.get | GET_EVENT_TYPE_SDK_VERSION | Get |

    - [Session recording](https://coralogix.com/docs/user-guides/rum/product-features/session-replay/)
        
        
        | **Event Name** | **Event Type** | **Field Type** |
        | --- | --- | --- |
        | session-recording.get | GET_EVENT_TYPE_SESSION_RECORDING | Get |

    - [Source map](../../rum/sdk-features/source-maps/index.md)
        
        | **Event Name** | **Event Type** | **Field Type** |
        | --- | --- | --- |
        | source-map.get | GET_EVENT_TYPE_SOURCE_MAP | Get |

    - [Hide errors](../../rum/product-features/hide-error-templates/index.md)
        
        | **Event Name** | **Event Type** | **Field Type** |
        | --- | --- | --- |
        | hide-errors.list | LIST_EVENT_TYPE_HIDE_ERRORS | List |
        | hide-errors.get | GET_EVENT_TYPE_HIDE_ERRORS | Get |
        | hide-errors.delete | DELETE_EVENT_TYPE_HIDE_ERRORS | Delete |
        | hide-errors.create | CREATE_EVENT_TYPE_HIDE_ERRORS | Create |
        | hide-errors.update | UPDATE_EVENT_TYPE_HIDE_ERRORS | Update |

    - [Saved filters](../../monitoring-and-insights/logs-screen/fields-filters-and-counters/index.md)
        
        | **Event Name** | **Event Type** | **Field Type** |
        | --- | --- | --- |
        | saved-filter.list | LIST_EVENT_TYPE_SAVED_FILTER | List |
        | saved-filter.get | GET_EVENT_TYPE_SAVED_FILTER | Get |
        | saved-filter.delete | DELETE_EVENT_TYPE_SAVED_FILTER | Delete |
        | saved-filter.create | CREATE_EVENT_TYPE_SAVED_FILTER | Create |
        | saved-filter.update | UPDATE_EVENT_TYPE_SAVED_FILTER | Update |

- [SCIM](https://coralogix.com/docs/user-guides/account-management/user-management/scim/)
    
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | scim.user.delete | ACTION_EVENT_TYPE_SCIM_DELETE_USER | Action |
    | scim.group.replace | ACTION_EVENT_TYPE_SCIM_REPLACE_GROUP | Action |
    | scim.user.create | ACTION_EVENT_TYPE_SCIM_CREATE_USER | Action |
    | scim.user.replace | ACTION_EVENT_TYPE_SCIM_REPLACE_USER | Action |
    | scim.user.update | ACTION_EVENT_TYPE_SCIM_UPDATE_USER | Action |
    | scim.group.delete | ACTION_EVENT_TYPE_SCIM_DELETE_GROUP | Action |
    | scim.group.update | ACTION_EVENT_TYPE_SCIM_UPDATE_GROUP | Action |
    | scim.group.create | ACTION_EVENT_TYPE_SCIM_CREATE_GROUP | Action |
    | scim.user.list | LIST_EVENT_SCIM_USER | List |
    | scim.group.list | LIST_EVENT_SCIM_GROUP | List |
    | scim.schema.list | LIST_EVENT_SCIM_SCHEMA | List |
    | scim.schema.get | GET_EVENT_SCIM_SCHEMA | Get |
    | scim.user.get | GET_EVENT_SCIM_USER | Get |
    | scim.group.get | GET_EVENT_SCIM_GROUP | Get |

- [Scopes](https://coralogix.com/docs/user-guides/account-management/user-management/scopes/)
    
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | scopes.list | LIST_EVENT_TYPE_SCOPE_LIST | List |
    | scopes.get | GET_EVENT_TYPE_SCOPE_GET | Get |
    | scopes.delete | DELETE_EVENT_TYPE_SCOPE_DELETE | Delete |
    | scopes.create | CREATE_EVENT_TYPE_SCOPE_CREATE | Create |
    | scopes.update | UPDATE_EVENT_TYPE_SCOPE_UPDATE | Update |

- [Security enrichments](https://coralogix.com/docs/user-guides/data-transformation/enrichments/unified-threat-intelligence/)
    
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | security-enrichment.delete | DELETE_EVENT_TYPE_SECURITY_ENRICHMENT | Delete |
    | security-enrichment.create | CREATE_EVENT_TYPE_SECURITY_ENRICHMENT | Create |
    | security-enrichment.update | UPDATE_EVENT_TYPE_SECURITY_ENRICHMENT | Update |

- [Spans TCO](https://coralogix.com/docs/user-guides/account-management/tco-optimizer/traces/)
    
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | spans.tco.order | ACTION_EVENT_TYPE_SPANS_TCO_ORDER | Action |
    | spans.tco.list | LIST_EVENT_TYPE_SPANS_TCO | List |
    | spans.tco.get | GET_EVENT_TYPE_SPANS_TCO | Get |
    | spans.tco.delete | DELETE_EVENT_TYPE_SPANS_TCO | Delete |
    | spans.tco.create | CREATE_EVENT_TYPE_SPANS_TCO | Create |
    | spans.tco.update | UPDATE_EVENT_TYPE_SPANS_TCO | Update |

- [SSO](https://coralogix.com/docs/user-guides/account-management/account-management/sso-with-saml/)
    
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | sso.set-idp-parameters | ACTION_EVENT_TYPE_SSO_SET_IDP_PARAMETERS | Action |
    | sso.set-active | ACTION_EVENT_TYPE_SSO_SET_ACTIVE | Action |
    | sso.configuration.get | GET_EVENT_SSO_CONFIGURATION | Get |
    | sso.sp-parameters.get | GET_EVENT_SSO_SP_PARAMETERS | Get |

- [Team IP access](https://coralogix.com/docs/user-guides/account-management/account-settings/ip-access-control/)
    
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | team-ip-access.list | LIST_EVENT_TYPE_IP_FILTER | List |
    | team-ip-access.delete | DELETE_EVENT_TYPE_IP_FILTER | Delete |
    | team-ip-access.create | CREATE_EVENT_TYPE_IP_FILTER | Create |
    | team-ip-access.update | UPDATE_EVENT_TYPE_IP_FILTER | Update |

- Team management
    
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | team-landing-page.update | UPDATE_EVENT_TYPE_TEAM_LANDING_PAGE | Update |
    | team-member.remove | ACTION_EVENT_TYPE_TEAM_MEMBER_REMOVE | Action |
    | team-member.unlock | ACTION_EVENT_TYPE_TEAM_MEMBER_UNLOCK | Action |
    | team-member.add | ACTION_EVENT_TYPE_TEAM_MEMBER_ADD | Action |
    | team-member.invite | ACTION_EVENT_TYPE_TEAM_MEMBER_INVITE | Action |
    | team-member.list | LIST_EVENT_TEAM_MEMBER | List |
    | team-sessions.session-timeout.update | UPDATE_EVENT_TYPE_TEAM_SESSIONS_SESSION_TIMEOUT | Update |
    | team-sessions.force-logout.update | UPDATE_EVENT_TYPE_TEAM_SESSIONS_FORCE_LOGOUT | Update |
    | team-url.update | UPDATE_EVENT_TYPE_TEAM_URL | Update |

- Templates
    
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | templates.list.read | ACTION_EVENT_TYPE_TEMPLATES_READ_DATA | Action |
    | templates.count.read | ACTION_EVENT_TYPE_TEMPLATES_COUNT_READ_DATA | Action |
    | templates-dates.get | GET_EVENT_TYPE_TEMPLATES_DATES | Get |

- User management
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | user-landing-page.update | UPDATE_EVENT_TYPE_USER_LANDING_PAGE | Update |
    | user-settings.get | GET_EVENT_TYPE_USER_SETTING | Get |
    | user-settings.update | UPDATE_EVENT_TYPE_USER_SETTING | Update |

- [Version benchmarks](https://coralogix.com/docs/user-guides/monitoring-and-insights/version-benchmarks/)
    
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | version-benchmarks.list | LIST_EVENT_TYPE_VERSION_BENCHMARKS | List |
    | version-benchmarks.get | GET_EVENT_TYPE_VERSION_BENCHMARKS | Get |
    | version-benchmarks.delete | DELETE_EVENT_TYPE_VERSION_BENCHMARKS | Delete |
    | version-benchmarks.create | CREATE_EVENT_TYPE_VERSION_BENCHMARKS | Create |
    | version-benchmarks.update | UPDATE_EVENT_TYPE_VERSION_BENCHMARKS | Update |

- [Views](https://coralogix.com/docs/user-guides/monitoring-and-insights/explore-screen/create-and-manage-saved-views/)
    
    | **Event Name** | **Event Type** | **Field Type** |
    | --- | --- | --- |
    | view.list | LIST_EVENT_TYPE_VIEW | List |
    | view.get | GET_EVENT_TYPE_VIEW | Get |
    | view.delete | DELETE_EVENT_TYPE_VIEW | Delete |
    | view.create | CREATE_EVENT_TYPE_VIEW | Create |
    | view.update | UPDATE_EVENT_TYPE_VIEW | Update |
    
    - [View folder](https://coralogix.com/docs/user-guides/monitoring-and-insights/explore-screen/create-and-manage-saved-views/)
        
        | **Event Name** | **Event Type** | **Field Type** |
        | --- | --- | --- |
        | view-folder.list | LIST_EVENT_TYPE_VIEW_FOLDER | List |
        | view-folder.get | GET_EVENT_TYPE_VIEW_FOLDER | Get |
        | view-folder.delete | DELETE_EVENT_TYPE_VIEW_FOLDER | Delete |
        | view-folder.create | CREATE_EVENT_TYPE_VIEW_FOLDER | Create |
        | view-folder.update | UPDATE_EVENT_TYPE_VIEW_FOLDER | Update |


## Monitoring audit events

To monitor your audit events, take the following steps:

**STEP 1**. Sign in to the team designated as your audit team.

**STEP 2**. View audit events in [Explore](../../monitoring-and-insights/logs-screen/logs-in-explore-screen/index.md) or [Visual Explorer](../../monitoring-and-insights/visual-explorer/index.md) or by creating visualizations in [Custom Dashboards](../../custom-dashboards/getting-started/index.md).

### Monitor & query audit events in Explore

Your audit events will appear in the Logs screen in Explore. For any log, click on the left-hand ellipsis and select **Show graph for key** from the dropdown menu.

![auditv2_explore.png](./images/auditv2_explore.png)

This graph shows the breakdown of actions taken on the organization, team, and user levels for `actorType`.

Let’s say an admin is interested in querying users with the most actions taken, grouped by user name and team name.  The [DataPrime](../../../dataprime/introduction/welcome-to-the-dataprime-reference/index.md) query will look as follows:

```json
source logs | filter actorDetails.username != null | filter actorDetails.teamName != null | groupby $d.actorDetails.username as username, actorDetails.teamName as team_name agg count() as count
```

### Create analytics in Custom Dashboards

Create a visual in Custom Dashboards to view audit event analytics.  

![auditv2_analytics1.png](./images/auditv2_analytics1.png)

In this example, the user has created a pie chart to understand the breakdown of events per Coralogix environment. Here you can see the user query failure errors per Coralogix event.

![auditv2_analytics2.png](./images/auditv2_analytics2.png)


## Audit v1 (Legacy)

Existing users are strongly encouraged to migrate from Audit v1 to v2. 

Audit v1 users can track actions based on `action_details.operation.action`; for example, by using the following query: `action_details.operation.action:"POST:/api/v1/user/login"`.

The following is the list of the most common actions which include `action_details.operation.operation_payload` key:

| **POST:/api/v1/logquery/saved** | **Save log query** |
| --- | --- |
| POST:/api/v1/logsparser/rules/group | Rules - New parsing rule group |
| POST:/api/v1/logsparser/groups/reorder | Rules - Rule groups reorder |
| POST:/api/v1/archiveproviders | Archive - Configure S3 Archive bucket |
| POST:/api/v1/archiveproviders/edit | Archive - Edit S3 Archive bucket configuration |
| POST:/api/v1/invite | Invites - An invitation sent |
| POST:/api/v1/invite/request/approve | Invites - A user was approved |
| POST:/api/v1/invite/request/decline | Invites - A user was declined |
| POST:/api/v1/user/forgotpassword/:key | Password - Changes the password from the forgot password email |
| POST:/api/v1/user/forgotpassword | Password - Send forgot password email |
| POST:/api/v1/user/changepassword | Password - Change password |
| POST:/api/v1/user/team/switch | Log out / switch team |
| POST:/api/v1/companies/:companyId/rbac/groups | Groups - Get all RBAC groups |
| POST:/api/v1/companies/:companyId/rbac/groups/:groupId/roles | Groups - Add RBAC group role for group |
| POST:/api/v1/rbac/:companyId/users/:userId/removeFromGroup | Groups - Remove user from RBAC group |
| POST:/api/v1/rbac/:companyId/users/:userId/moveToGroup | Groups - Move user to different RBAC group |
| POST:/api/v1/rbac/:companyId/users/:userId/addToGroup | Groups - Add user to RBAC group |
| POST:/api/v1/rbac/:companyId/users/:userId/addUserToGroups | Groups - Add user to RBAC groups |
| POST:/api/v1/rbac/:companyId/users/:userId/removeUserFromGroups | Groups - Remove user to RBAC groups |
| POST:/api/v1/company/saml/metadata | SAML - Update SAML metadata |
| PUT:/api/v1/company/saml | SAML - Get company SAML configuration |
| POST:/api/v1/alert/:id/snooze | Alerts - Snooze alert |
| POST:/api/v1/alert/snoozed | Alerts - Get all snoozed alert |
| POST:/api/v1/customenrichments/getUploadUrl | Custom Enrichment - Upload custom enrichment csv |
| POST:/api/v1/cloudsecurity/install | Cloud Security - Installation |
| POST:/api/v1/cloudsecurity/getstatus | Cloud Security - Get installation status |
| POST:/api/v1/rulesapi/crud/ruleset | Rules - Create rule-set |
| PUT:/api/v1/rulesapi/ruleset/reorder | Rules - Reorder rule set |
| POST:/api/v1/archivequeries | Archive query - New archive query |
| POST:/api/v1/archivequeries/:id/reindex | Archive query - Create new archive query reindex |
| POST:/api/v1/archivequeries/:id/reindex/:reindexId/query | Archive query - Cache reindex query |
| POST:/api/v1/quota-policies | TCO Optimizer - Create quota policy |
| PUT:/api/v1/quota-policies/reorder | TCO Optimizer - Reorder quota policies |
| PUT:/api/v1/quota-policies/:id | TCO Optimizer - Update quota policy |
| PUT:/api/v1/quota-policies/toggle/:id | TCO Optimizer - Toggle quota policy |
| PUT:/api/v1/quota-overrides | TCO Optimizer - Create quota override |
| POST:/api/v1/external/alerts | Alerts - Add new alert |
| POST:/api/v1/external/alerts/bulk | Alerts - Add new alerts |
| PUT:/api/v1/external/alerts | Alerts - Update alert |
| POST:/api/v1/external/group | Rules - Create rule parsing group |
| PUT:/api/v1/external/group/:parsingThemeId | Rules - Update rule parsing group |
| PUT:/api/v1/external/group/toggle/:parsingThemeId | Rules -  Toggle rule parsing group |
| POST:/api/v1/external/rule/:parsingThemeId | Rules - Add parsing rule to parsing group |
| PUT:/api/v1/external/rule/:ruleId/group/:parsingThemeId | Rules - Update parsing rule |
| POST:/api/v1/external/rules/export | Rules - Export rules |
| POST:/api/v1/external/actions/rule | Rules - Add parsing rule group |
| PUT:/api/v1/external/actions/rule/:groupId | Rules - Update parsing rule group |
| POST:/api/v1/external/action/rule/:groupId | Rules - Create parsing rule group |
| PUT:/api/v1/external/action/:ruleId/rule/:groupId | Rules - Update parsing rule |
| PUT:/api/v1/external/customenrichments/:customEnrichmentId | Custom Enrichment - Update custom enrichment |
| POST:/api/v1/external/tags | Tags - Get new tag |
| POST:/api/v1/external/bitbucket | Tags - Get new Bitbucket tag |
| POST:/api/v1/external/tfs | Tags - Get new tfs tag |
| POST:/api/v1/external/gitlab | Tags - Get new Gitlab tag |
| POST:/api/v1/external/tco/policies | TCO Optimizer - Create new policy |
| PUT:/api/v1/external/tco/policies/reorder | TCO Optimizer - Reorder policies |
| PUT:/api/v1/external/tco/policies/:id | TCO Optimizer - Update policy |
| PUT:/api/v1/external/tco/policies/:id/toggle | TCO Optimizer - Toggle policy |
| POST:/api/v1/external/tco/overrides | TCO Optimizer - Add new TCO override |
| POST:/api/v1/external/tco/overrides/bulk | TCO Optimizer - Add new TCO overrides |
| POST:/api/v1/user/settings/es_api_key | API Access - Generate new Logs Query Key for user |
| POST:/api/v1/user/settings/teams_api_key | API Access - Generate new Teams API Key for user |
| POST:/api/v1/payment/subscriber | Plan - Subscribe to payment |
| POST:/api/v1/payment/unsubscribe | Plan - Unsubscribe to payment |
| POST:/api/v1/payment/changePlan | Plan - Change payment plan |

