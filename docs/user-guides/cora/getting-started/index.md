---
title: "Getting Started"
date: "2024-06-05"
description: "<PERSON> is Coralogix AI assistant that enhances observability, empowers users across all skill levels, and accelerates insights for smarter decision-making."
---

# Getting Started

Cora is the Coralogix AI assistant that enhances observability, empowers users across all skill levels, and accelerates insights for smarter decision-making. 
<PERSON> accelerates query creation and streamlines log interpretation, enabling faster incident resolution. By providing actionable insights in real-time during ongoing issues, it minimizes back-and-forth communication with internal teams, improving overall efficiency.

## Capabilities

### Query your data using natural language

Cora simplifies data querying by turning plain language requests into structured DataPrime queries, removing the need for complex query languages. For details, see [DataPrime Query Assistance](../dataprime-query-assistance/index.md).

### Understand logs easily and identify issues faster

Cora’s Explain Log streamlines log analysis by offering clear explanations, outlining their significance, immediate impact, and potential reason for log generation. For details, see [Explain Log](../explain-log/index.md).

### Explore Corlaogix platform using natural language

The Knowledge Assistance uses an advanced large-language model (LLM) to provide detailed information about any platform feature. Enter your question about the platform in plain English, and <PERSON> analyzes the prompt. Then, it delivers a documentation-based response, along with relevant resources to support the answer. For details, see [Knowledge Assistance](../knowledge-assistance/index.md).

## Enabling Cora

1. Verify that the user has the `team-ai-settings:Manage` permission.
2. Navigate to **Settings > Account Preferences**.
3. In the **CORA - Coralogix Observation and Research Assistant** section, toggle the CORA switch to **ON** to activate the use of all AI-powered capabilities.<br/>
When Cora is enabled, all its features and capabilities become active simultaneously. When it is disabled, all functionalities are turned off.

![](images/enable-cora.png)

## **Frequently asked questions**

This section includes a series of questions commonly asked by customers. The questions cover such topics as Cora usage, compliance, data processing, privacy, etc.

<div class="accordion-wrapper" markdown="1">
<div class="accordion-item">
<div class="accordion-title">
Does Cora send data to a 3rd party LLM service?
</div>
<div class="accordion-content" id="dropdown1">
Yes, the data is sent to OpenAI.
</div>
</div>
<div class="accordion-item" markdown="1">
<div class="accordion-title">
Does OpenAI use the data for training?
</div>
<div class="accordion-content" id="dropdown2" markdown="1">
OpenAI does not use the data for training. For details, see the [OpenAI guidelines](https://platform.openai.com/docs/models/how-we-use-your-data/).
</div>
</div>
<div class="accordion-item">
<div class="accordion-title">
Which data is sent to OpenAI?
</div>
<div class="accordion-content" id="dropdown2" markdown="1">
Refer to the specific Cora capability to understand exactly what data is sent for external processing.
</div>
</div>
<div class="accordion-item" markdown="1">
<div class="accordion-title">
Is any of the Cora data stored externally?
</div>
<div class="accordion-content" id="dropdown2" markdown="1">
No data is stored permanently on an external platform. For operational and security reasons, such as abuse prevention, OpenAI may retain the data for up to 30 days. For details, see [OpenAI guidelines](https://platform.openai.com/docs/models/how-we-use-your-data/).
</div>
</div>
<div class="accordion-item">
<div class="accordion-title">
Is Cora GDPR compliant? 
</div>
<div class="accordion-content" id="dropdown2">
Yes, we use OpenAI, which complies with GDPR requirements. In addition, Coralogix and OpenAI signed a data processing agreement (DPA) with updated SCCs. Coralogix has added OpenAI as an authorized sub-processor in case customers leak personal data into prompts. However, Coralogix does not have control over customers’ data input. Coralogix has checked OpenAI’s compliance with GDPR and thoroughly verified technical and organizational measures taken to ensure its compliance.
</div>
</div>
<div class="accordion-item">
<div class="accordion-title">
Is the Cora HIPAA compliant? 
</div>
<div class="accordion-content" id="dropdown2">
No, we use OpenAI, which is not explicitly stated to be compliant with the Health Insurance Portability and Accountability Act (HIPAA) requirements. Customers must avoid processing Protected Health Information (PHI) through Cora and refrain from opting in if this is the case. Customers can also disable the use of Cora to prevent any PHI exposure.
</div>
</div>
<div class="accordion-item" markdown="1">
<div class="accordion-title">
Where is the OpenAI instance that processes the Cora data located? 
</div>
<div class="accordion-content" id="dropdown2" markdown="1">
Cora uses OpenAI instances hosted by US-located Microsoft Azure data centers. For details, see the [OpenAI Security Portal documentation](https://trust.openai.com/?itemUid=a0c2d606-48f6-4519-8db5-9029a98328d6&source=click).
</div>
</div>
</div>

## Additional resources

|               |                                                                                                                                           |                                                          
|---------------|-------------------------------------------------------------------------------------------------------------------------------------------|
| DataPrime Query Assistance | [Documentation](../dataprime-query-assistance/index.md) |
| Explain Log      | [Documentation](../explain-log/index.md)                                           |
| Knowledge Assistance      | [Documentation](../knowledge-assistance/index.md)                                           |

::: additional-content items="id: fo9LjED0_CI, title: Introduction to Cora AI; id: Z844taMUxr8, title: Data Privacy and Compliance with Cora AI" 

## Support

**Need help?** 

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up. 

Feel free to reach out to us **via our in-app chat** or by sending us an email to [<EMAIL>](mailto:<EMAIL>).