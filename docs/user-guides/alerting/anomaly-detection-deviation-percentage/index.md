---
title: "Customizing Anomaly Detection Alert Sensitivity"
date: "2020-03-05"
description: "Tailor alert sensitivity for both logs and metrics-based anomaly detection alerts."
---

# Customizing Anomaly Detection Alert Sensitivity

Anomaly detection alerts leverage advanced artificial intelligence algorithms to analyze incoming logs and metrics. Using data from the previous 24 hours, the system forecasts expected behavior for the next 24 hours. As part of this predictive model, the algorithm establishes upper and lower thresholds of expected behavior, serving as boundaries. When data crosses these thresholds, an alert is triggered, enabling proactive corrective actions.

With Coralogix, you can tailor alert sensitivity for both [logs-based](../create-an-alert/logs/anomaly-detection-alerts/index.md) and [metrics-based](../create-an-alert/metrics/anomaly-detection-alerts/index.md) anomaly detection alerts. To avoid excessive alerting, you can adjust the sensitivity by specifying a **deviation percentage** from the predictive thresholds.

## Configuring deviation percentage

You can customize sensitivity thresholds for anomaly detection alerts while defining your [alert conditions](../multiple-alert-conditions/index.md).

![](../anomaly-detection-deviation-percentage/images/multiple-conditions-anomaly-detection-alerts.png)

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Navigate to **Advanced settings** in your alert configuration.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Enter the **deviation percentage** from the predictive threshold that will trigger an alert.

            For example, if the upper threshold is 100 and you set the deviation to 10%, an alert will be triggered if the value exceeds 110.

## Guiding notes

- The predictive model calculates the average expected behavior and defines upper and lower thresholds as boundaries for expected values.  
- The baseline distance is the range between the average behavior and each threshold.  
- You can specify a deviation percentage relative to this baseline to adjust alert sensitivity.

### Example
If the baseline distance is 50, with an upper threshold of 150, and you set the deviation to 10%, the alert threshold will be calculated as:  
**150 + (10% of 50) = 150 + 5 = 155.**  
An alert will be triggered if the value exceeds 155.

By customizing these parameters, you can ensure that alerts are both relevant and actionable.

## Support

**Need help?** 

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up. 

Feel free to reach out to us **via our in-app chat** or by sending us an email to [<EMAIL>](mailto:<EMAIL>).