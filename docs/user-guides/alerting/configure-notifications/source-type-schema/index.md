---
title: "Alerts as a Notification Source Type"
date: "2024-08-07"
description: "This tutorial describes entity types and subtypes for Notification Center."
---

{% raw %}

# Alerts as a Notification Source Type

Alerts are one example of a **notification source type**, a component of the Coralogix infrastructure capable of sending notifications.   

## Supported alert subtypes

A notification source subtype adds an added layer of granularity to a notification source type.  For alerts, a source subtype consists of three elements: an alert type, a condition, and status. 

The following alert source subtypes are supported: 

| Source  | Type                           | Condition                                                         | Status               | Description                                                                                                                                                                         |
|---------|--------------------------------|-------------------------------------------------------------------|----------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Logs    | `LOGS_IMMEDIATE`               | —                                                                 | Triggered / Resolved | Alert immediately when a log of interest is detected. [Docs](https://coralogix.com/docs/user-guides/alerting/create-an-alert/logs/immediate-notifications/)                         |
| Logs    | `LOGS_THRESHOLD`               | More than / Less than                                             | Triggered / Resolved | Alert when the sum of logs crosses a predefined threshold. [Docs](https://coralogix.com/docs/user-guides/alerting/create-an-alert/logs/threshold-alerts/)                           |
| Logs    | `LOGS_RATIO_THRESHOLD`         | More than / Less than                                             | Triggered / Resolved | Alert when the ratio between two log queries reaches a set threshold. [Docs](https://coralogix.com/docs/user-guides/alerting/create-an-alert/logs/ratio-alerts/)                    |
| Logs    | `LOGS_TIME_RELATIVE_THRESHOLD` | More than / Less than                                             | Triggered / Resolved | Alerts when a fixed ratio reaches a set threshold compared to a past time frame. [Docs](https://coralogix.com/docs/user-guides/alerting/create-an-alert/logs/time-relative-alerts/) |
| Logs    | `LOGS_ANOMALY`                 | More than usual                                                   | Triggered / Resolved | Alert when a log crosses an AI-generated baseline. [Docs](https://coralogix.com/docs/user-guides/alerting/create-an-alert/logs/anomaly-detection-alerts/)                           |
| Logs    | `LOGS_NEW_VALUE`               | —                                                                 | Triggered            | Alert on a newly detected value in a time series. [Docs](https://coralogix.com/docs/user-guides/alerting/create-an-alert/logs/new-value-alerts/)                                    |
| Logs    | `LOGS_UNIQUE_COUNT`            | More than                                                         | Triggered            | Alert based on a unique value count per key. [Docs](https://coralogix.com/docs/user-guides/alerting/create-an-alert/logs/unique-count-alerts/)                                      |
| Metrics | `METRIC_THRESHOLD`             | More than / Less than / More than or equals / Less than or equals | Triggered / Resolved | Alert when a metric crosses a predefined threshold. [Docs](https://coralogix.com/docs/user-guides/alerting/create-an-alert/metrics/threshold-alerts/)                               |
| Metrics | `METRIC_ANOMALY`               | More than usual / Less than usual/                                | Triggered /Resolved  | Alert when a metric crosses an AI-generated baseline. [Docs](https://coralogix.com/docs/user-guides/alerting/create-an-alert/metrics/anomaly-detection-alerts/)                     |
| Tracing | `TRACING_IMMEDIATE`            | —                                                                 | Triggered            | Alert immediately when span duration exceeds a set latency. [Docs](https://coralogix.com/docs/user-guides/alerting/create-an-alert/traces/tracing-alerts/)                          |
| Tracing | `TRACING_THRESHOLD`            | More than                                                         | Triggered            | Alert when trace latency crosses a predefined threshold. [Docs](https://coralogix.com/docs/user-guides/alerting/create-an-alert/traces/tracing-alerts/)                             |
| Flow    | `FLOW`                         | —                                                                 | Triggered / Resolved | Alert when any combination of alerts occur in a specific sequence within a defined timeframe. [Docs](https://coralogix.com/docs/user-guides/alerting/create-an-alert/flow-alerts/)  |

All supported alert notification source subtypes, consisting of `EntityType` and `EntitySubType`, can be found in the [Alerts API](../../../../developer-portal/apis/data-management/alerts-api/alerts-grpc-api/index.md).

## Alerts Notification Source Type Schema  

### General reference

::: json-schema
    ::: json-schema-item title="alert" description="Represents the alert details, including timestamp, ID, status, and group information."
        ::: json-schema-prop title="alertDataUrl"
            Link to the incident page (e.g., https://coralogix.com/#/insights?id=76c411be-gg4d-4fb1-a987-5fce042deaaf).
        ::: json-schema-prop title="groupingKey"
            Used as the deduplication key.
        ::: json-schema-item title="groups" description="List of alert groups associated with the alert (including priority and key-values)."
            ::: json-schema-item title="groups[n]"
                ::: json-schema-item title="keyValues" description="Key-value pairs associated with the alert group; e.g., \"team\": \"{{alert.groups[0].keyValues.resource.attributes.Team}}\" or \"service\":\"{{alert.groups[0].keyValues.coralogix.metadata.subsystemName}}\""
                    ::: json-schema-item title="resource"
                        ::: json-schema-item title="attributes"
                            ::: json-schema-prop title="Team"
                    ::: json-schema-item title="coralogix"
                        ::: json-schema-item title="metadata"
                            ::: json-schema-prop title="subsystemName"
        ::: json-schema-prop title="id"
            Unique identifier of the alert. Use it to create an alert URL in your notification; e.g., "alert_url": "https://teamname-prod.app.eu2.coralogix.com/#/alerts/{{alert.id}}"
        ::: json-schema-prop title="priority"
            Priority level of the alert group (e.g., "P1").
        ::: json-schema-prop title="status"
            Current status of the alert (e.g., "Triggered").
        ::: json-schema-prop title="timestamp"
            The timestamp indicating when the alert was triggered.

::: json-schema
    ::: json-schema-item title="alertDef" description="Defines the alert configuration, including name, description, groupings, incident settings, and priority."
        ::: json-schema-prop title="alertVersionId"
            The version ID associated with the alert definition.
        ::: json-schema-prop title="createdTime"
            The timestamp when the alert definition was created.
        ::: json-schema-prop title="description"
            A textual description of the alert definition.
        ::: json-schema-prop title="entityLabels"
            Labels associated with the alert definition (e.g., host, region).
        ::: json-schema-prop title="groupByKeys"
            List of keys by which the alert is grouped (e.g., host, region).
        ::: json-schema-prop title="id"
            The unique identifier of the alert definition.
        ::: json-schema-item title="incidentSettings" description="Configuration related to incident settings (e.g., retriggering period)."
            ::: json-schema-prop title="notifyOn"
                Notification setting for the alert definition (e.g., "Triggered").
        ::: json-schema-prop title="name"
            Name of the alert definition (e.g., "CPU Usage Alert").
        ::: json-schema-prop title="priority"
            Priority level of the alert definition (e.g., P1).
        ::: json-schema-prop title="retriggeringPeriod"
            Time in minutes after which the alert can be retriggered.
        ::: json-schema-prop title="status"
            Current status of the alert definition.
        ::: json-schema-prop title="type"
            Type of the alert (e.g., "metricThreshold").
        ::: json-schema-prop title="updatedTime"
            The timestamp when the alert definition was last updated.

### Context reference

All templates have access to the `_context` variable, which contains metadata about the notification source type, including system identifiers and trigger details. This context allows you to dynamically customize your template content based on the notification's origin.

Find out more [here](../../../notification-center/dynamic-templating/index.md#context-reference)

{% endraw %}