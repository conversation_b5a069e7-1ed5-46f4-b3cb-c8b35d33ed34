---
title: "Define Alert Details"
date: "2022-01-04"
coverImage: "alerts.png"
description: "As part of Coralogix Alerting, metric alerts serve as your frontline defense, ensuring the uninterrupted performance, reliability, and security of your systems and applications."
---
# Define Alert Details

## Getting started

As part of your initial [alert definition setup](../introduction-to-alerts/index.md), define your alert details.

In your Coralogix toolbar, go to **Alerts** > **Alert Management**. Click **NEW ALERT**.

## Alert details

Define the following alert details:

- Alert name
- Alert description
- Labels

![](../define-alert-details/alert-details.png)

## Labels

Labels help you filter alerts in [Incidents](../incidents/index.md) and organize views. You can create a new label or select an existing one. To nest a label, use the `key:value` format (e.g., `env:prod`).

### APM
To classify an alert as an APM alert, use labels related to service or database catalogs, including:

**Service Catalog alerts**

- `product:apm-service-catalog`

- `serviceName:<service name>`

**Database Monitoring alerts**

- `product:apm-db-catalog`

- `apm-db-catalog.databaseName:<db.system>.<db.namespace>`

Learn more about APM alerts [here](../../apm/features/monitoring-with-alerts/index.md).

### Security

To designate an alert as a security alert, check the **Set as security alert** option or use the label `alert_type:securitycheck`.
