---
title: "PagerDuty Outbound Webhooks"
date: "2020-04-19"
coverImage: "PagerDuty-1000X1000.png"
---

Enhance your observability workflows by sending real-time event notifications and log data to **PagerDuty**. With this outbound webhook, you can easily integrate Coralogix with PagerDuty, automate responses to critical events, and improve your organization's incident management and alerting processes.

## **How It Works**

- When alert conditions are met, [Coralogix Alerts](../../../alerting/introduction-to-alerts/index.md) will send an event to your service in PagerDuty.

- Events from Coralogix will trigger a new incident in the corresponding PagerDuty service.

- Once the conditions have returned to their designated range, a Resolve event will be sent to the PagerDuty service to resolve the alert status and associated incident.

## Prerequisites

- PagerDuty service set up

- PagerDuty integration key

### PagerDuty Service

If you do not have an existing PagerDuty service to add the integration, create a new service by following [these directions](https://support.pagerduty.com/docs/services-and-integrations#section-create-a-new-service).

If you have an existing PagerDuty service, access it.

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            From the PagerDuty **Configuration** menu, select **Services**.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Click the **service** **name** to which you want to add the integration.

### Add the Integration to the PagerDuty Service

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Select the PagerDuty **Integrations** tab from the lower toolbar and click **New Integration**.
            
            ![](images/Outgoing-Webhooks-PagerDuty-Integrations.png)
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Enter an **Integration Name** (such as `Coralogix-Alerts-Notifier`) and select **Coralogix** from the **Integration Type** menu.
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Click the **Add Integration** button to save your new integration. You will be redirected to the **Integrations** tab for your service.
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            An integration key will be generated on this screen. Copy and save it in a secure location for use in your webhook setup.

## Create a PagerDuty Webhook

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            From the Coralogix toolbar, navigate to **DATA FLOW** > **EXTENSIONS.**
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            In the **Outbound Webhooks** section, click **PAGERDUTY WEBHOOK**.
            
            ![](images/Outgoing-Webhooks-PagerDuty-Overview.png)
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Click **\+ ADD NEW**.

            ![](images/Outgoing-Webhooks-PagerDuty-Settings.png)
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Enter the following details for your webhook:

            - **Webhook Name**. Input a name for your webhook that will enable you to quickly identify this webhook later when attaching it to one of your alerts.
            - **Integration Key.** Paste the PagerDuty integration key.
    ::: custom-list-item marker=5
        ::: list-item-header

        ::: list-item-content
            Click **TEST CONFIG**.

            The system will create a test incident in the PagerDuty dashboard to check that your configuration is valid. If the test incident is created successfully, a confirmation message is displayed.
    ::: custom-list-item marker=6
        ::: list-item-header

        ::: list-item-content
            [Configure your alert notifications](../configure-alert-notifications-for-outbound-webhooks/index.md).

## Additional Resources
|               |                                                                                                                        |
|---------------|------------------------------------------------------------------------------------------------------------------------|
| Documentation | [Configure Alert Notifications for Outbound Webhooks](../configure-alert-notifications-for-outbound-webhooks/index.md) |

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
