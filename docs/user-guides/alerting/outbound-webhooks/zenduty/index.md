---
title: "Zenduty"
date: "2023-02-08"
coverImage: "Zenduty-1000X1000.png"
description: "Coralogix now supports seamless integration with Zenduty."
---

Zenduty is an incident management platform that helps businesses manage and resolve incidents quickly and efficiently. It provides a range of features such as alert management, on-call scheduling, incident response automation, and post-mortem analysis. Coralogix now supports seamless integration with [Zenduty](https://www.zenduty.com/).

## Configuration

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Navigate to **‘Teams’** on your Zenduty dashboard and click on the team to which the integration will be added.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Select **‘Services’** and click on the relevant Service.
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Under **‘Integrations’**, click **‘Add New Integration’**. Give it a name and select the application **‘Coralogix’** from the dropdown menu.
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Provide the details 'Name', 'Summary', 'Create incidents for ', 'set default Urgency' and click on **‘Add Integration’**
            
            ![](images/image-18-1024x863.png)
    ::: custom-list-item marker=5
        ::: list-item-header

        ::: list-item-content
            Click **‘Configure’** and copy the generated Webhook URL.  

            ![](images/image-19-1024x672.png)

## Coralogix Setup

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Navigate to **Data Flow** > **Webhooks** in your Coralogix account. 
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Select the **Webhooks** tab to create a new webhook. 
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            For the new webhook, give it a suitable name and paste the copied URL from Zenduty Configure.   
            
            ![](images/image-20-1024x421.png)
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Save the webhook.
    ::: custom-list-item marker=5
        ::: list-item-header

        ::: list-item-content
            Validate your configuration by clicking the **‘Test Configuration’** button. The test alert should appear on your Zenduty Alert log.
    ::: custom-list-item marker=6
        ::: list-item-header

        ::: list-item-content
            Click **Alerts** in your navigation bar to set the destination in the new alert.   

            ![](images/image-24-1024x154.png)
    ::: custom-list-item marker=7
        ::: list-item-header

        ::: list-item-content
            Define the **‘Alert Name’** and **‘Description’**. Severity is to be set to **‘Warning’** or **‘Critical’** for a new incident to be created. ![](images/coralogix7.png)
    ::: custom-list-item marker=8
        ::: list-item-header

        ::: list-item-content
            Define the conditions for which the alert is to be triggered.
    ::: custom-list-item marker=9
        ::: list-item-header

        ::: list-item-content
            Under **‘Recipients’**, search and add the previously defined Zenduty webhook. ![](images/coralogix4.png)

            **Note**: If ‘Notify when Resolved’ option is enabled, then when the conditions go back to normal, the incident will be auto-resolved. ![](images/coralogix5.png)  
            In Zenduty, Incident will be auto resolved.
            
            ![](images/image-23-1024x126.png)
    ::: custom-list-item marker=10
        ::: list-item-header

        ::: list-item-content
            Save the Alert.

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
