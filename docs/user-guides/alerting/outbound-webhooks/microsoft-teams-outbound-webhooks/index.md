---
title: "Microsoft Teams Outbound Webhooks"
date: "2020-08-14"
coverImage: "Teams-1000X1000.png"
---

Enhance your observability workflows by sending real-time event notifications and log data to **Microsoft Teams**. With this outbound webhook, you can easily integrate Coralogix with **Microsoft Teams**, automate responses to critical events, and improve your organization's incident management and alerting processes.

**Note!** Starting from August 15, 2024, Microsoft will begin deprecating Office 365 connectors within Microsoft Teams. This will prevent the creation of new MS Teams webhooks. To continue sending alert notifications, use Microsoft Teams Workflows. Refer to this [guide](../workflow-based-microsoft-teams-outbound-webhooks/index.md) for details.

## Prerequisites

Configure Your Microsoft Teams endpoint URL if you have not already done so.

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Navigate to **Apps** (in the left sidebar) in Microsoft Teams.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Search the apps for **Incoming Webhook**. Select **Incoming Webhook.**
            
            ![](images/Outgoing-Webhooks-Microsoft-Teams-Search-Incoming-Webhook-1024x528.png)
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Click **Add to a team**.

            ![](images/Outgoing-Webhooks-Microsoft-Teams-Add-to-a-Team-1024x816.png)
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Select a channel to use for the webhook. Click **Set up a connector**.

            ![](images/Outgoing-Webhooks-Microsoft-Teams-Set-Up-a-Connector.png)
    ::: custom-list-item marker=5
        ::: list-item-header

        ::: list-item-content
            Provide a name for the webhook. Click **Create**.

            ![](images/image-4-1024x927.png)
    ::: custom-list-item marker=6
        ::: list-item-header

        ::: list-item-content
            Once you create the webhook, copy the endpoint URL at the bottom of the dialog box.

            ![](images/image-5-1024x935.png)

## Create a Microsoft Teams Webhook

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            From the Coralogix toolbar, navigate to **Data Flow** > **Outbound Webhooks.**
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            In the **Outbound Webhooks** section, select **Microsoft Teams**.
            
            ![](images/Outgoing-Webhooks-Microsoft-Teams-Overview-1024x635.png)
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Click **\+ ADD NEW**.

            ![](images/Outgoing-Webhooks-Microsoft-Teams-Settings-1024x635.png)
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Enter the following details for your webhook:

            - **Webhook Name.** Input a name for your webhook that will enable you to quickly identify this webhook later when attaching it to one of your alerts.
            - **URL.** Paste the Microsoft Teams webhook URL previously copied in \[Prerequisities\].
    ::: custom-list-item marker=5
        ::: list-item-header

        ::: list-item-content
            Click **TEST CONFIG**.

            The system will send a test message to the channel you specified in the Microsoft Teams webhook URL to check that your configuration is valid. If the test message is received successfully, a confirmation message is displayed.
    ::: custom-list-item marker=6
        ::: list-item-header

        ::: list-item-content
            Once the configuration is confirmed, [configure your alert notifications](../configure-alert-notifications-for-outbound-webhooks/index.md).

## Additional Resources
|               |                                                                                                                        |
|---------------|------------------------------------------------------------------------------------------------------------------------|
| Documentation | [Configure Alert Notifications for Outbound Webhooks](../configure-alert-notifications-for-outbound-webhooks/index.md) |

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
