---
title: "Slack Outbound Webhooks"
date: "2021-02-02"
coverImage: "Slack-1000X1000.png"
description: "Enhance your observability workflows by sending real-time event notifications and log data to <PERSON>lack. With this outbound webhook, you can easily integrate Coralogix with <PERSON>lack, automate responses to critical events, and improve your organization's incident management and alerting processes."
---

Enhance your observability workflows by sending real-time event notifications and log data to **Slack**. With this outbound webhook, you can easily integrate Coralogix with Slack, automate responses to critical events, and improve your organization's incident management and alerting processes.

## Prerequisites

Access your Slack webhook URL.

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            While logged into Slack, click [here](https://my.slack.com/services/new/incoming-webhook)**.**
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Choose the room name. Click **Add incoming webhook integration.**
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Copy the webhook URL.

## Create a Slack Webhook

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            From the Coralogix toolbar, navigate to **DATA FLOW** > **EXTENSIONS.**
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            In the **Outbound Webhooks** section, click **SLACK WEBHOOK**.
            
            ![](images/Outgoing-Webhooks-Slack-Overview.png)
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Click **\+ ADD NEW**.

            ![](images/Outgoing-Webhooks-Slack-Settings.png)
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Enter the following details for your webhook:

            - **Webhook Name.** A memorable name for your webhook that will enable you to quickly identify this webhook later when attaching it to one of your alerts.
            - **URL.** Paste the Slack webhook URL you previously copied (prerequisite step).
    ::: custom-list-item marker=5
        ::: list-item-header

        ::: list-item-content
            Select the types of events for which you would like to receive notifications. The **Notify About** section allows you to select any of four general notifications and send them to the Slack channel webhook you are creating.

            The four general notification types are:
            
            - Error and critical logs
            - Flow anomalies
            - Spike anomalies
            - Data usage
    ::: custom-list-item marker=6
        ::: list-item-header

        ::: list-item-content
            Click **SAVE & TEST**.

            The system sends a test message to the channel you specified in the Slack webhook URL and to check that your configuration is valid. If the test message is received successfully, a confirmation message is displayed.
    ::: custom-list-item marker=7
        ::: list-item-header

        ::: list-item-content
            Once the configuration is confirmed and the webhook is in place, you can choose the alert or alerts in which this webhook will be used once the alert is triggered.

            ![](images/Outgoing-Webhooks-Slack-Alert-Notifications.png)
    ::: custom-list-item marker=8
        ::: list-item-header

        ::: list-item-content
            [Configure your alert notifications](../configure-alert-notifications-for-outbound-webhooks/index.md).

## Additional Resources
|               |                                                                                                                        |
|---------------|------------------------------------------------------------------------------------------------------------------------|
| Documentation | [Configure Alert Notifications for Outbound Webhooks](../configure-alert-notifications-for-outbound-webhooks/index.md) |

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
