---
title: "Send Log Outbound Webhooks"
date: "2023-10-25"
description: "Enhance your observability workflows by sending real-time event notifications and log data to Coralogix. With this webhook, you can easily receive logs in Coralogix, automate responses to critical events, and improve your organization's incident management and alerting processes."
---

Enhance your observability workflows by sending real-time event notifications and log data to Coralogix. With this webhook, you can easily receive logs in Coralogix, automate responses to critical events, and improve your organization's incident management and alerting processes.

## Create a Send Log Webhook

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            From the Coralogix toolbar, navigate to **Data Flow** > **Outbound Webhooks.**
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            In the **Outbound Webhooks** section, click **SEND LOG WEBHOOK**.
            
            ![](images/Outgoing-Webhooks-Send-Log-Overview-1024x635.png)
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Click **\+ ADD NEW**.

            ![](images/Outgoing-Webhooks-Send-Log-Settings-1024x635.png)
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Enter a memorable name for your webhook that will enable you to easily identify this webhook later when attaching it to one of your alerts.

            **Note:** The **URL** and **UUID** fields are auto-populated.
    ::: custom-list-item marker=5
        ::: list-item-header

        ::: list-item-content
            Click **NEXT**.
    ::: custom-list-item marker=6
        ::: list-item-header

        ::: list-item-content
            \[Optional\] Edit the message body that will be sent with the webhook message. The fields contained within the `logEntries` key will be displayed in the log.

            ![](images/Outgoing-Webhooks-Send-Log-Edit-Message-1024x635.png)

            !!! note

                `applicationName` and `subsystemName` are defined statically.
    
    ::: custom-list-item marker=7
        ::: list-item-header

        ::: list-item-content
            Click **TEST & SAVE**.

            The system creates a log on the Coralogix Explore page in the dashboard to check that your configuration is valid. If the log is created successfully, a confirmation message is displayed.
    ::: custom-list-item marker=8
        ::: list-item-header

        ::: list-item-content
            [Configure your alert notifications](../configure-alert-notifications-for-outbound-webhooks/index.md) once the configuration is confirmed and the webhook is in place.

## Additional Resources
|               |                                                                                                                        |
|---------------|------------------------------------------------------------------------------------------------------------------------|
| Documentation | [Configure Alert Notifications for Outbound Webhooks](../configure-alert-notifications-for-outbound-webhooks/index.md) |

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
