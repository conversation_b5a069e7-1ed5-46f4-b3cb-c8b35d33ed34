---
title: "Jira Outbound Webhooks"
date: "2020-08-24"
coverImage: "Akami-1000X1000-1.png"
---

Enhance your observability workflows by sending real-time event notifications and log data to **JIRA**. With this outbound webhook, you can easily integrate Coralogix with JIRA, automate responses to critical events, and improve your organization's incident management and alerting processes.

## Prerequisites

### Access Your JIRA API Token

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            On [this page](https://id.atlassian.com/manage/api-tokens), click **Create API token**.
            
            ![](images/Outgoing-Webhooks-JIRA-Create-API-Token-1024x598.png)
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Input a label. Click **Create**.

            ![](images/Outgoing-Webhooks-JIRA-Enter-API-Token-Label-1024x345.png)
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Please copy the generated API token and save it in a text file. You will need this token when configuring the webhook in Coralogix.

### Access Your JIRA Project Key

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            While logged into JIRA with your credentials, navigate to **Projects** from your JIRA toolbar.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            The project key can be found within the parentheses to the right of the project name in the **Projects** dropdown menu.
            
            ![](images/Outgoing-Webhooks-JIRA-Access-Project-Key-1024x418.png)

## Create a JIRA Webhook

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            From the Coralogix toolbar, navigate to **Data Flow** > **Outbound Webhooks.**
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            In the **Outbound Webhooks** section, click **JIRA WEBHOOK**.
            
            ![](images/Outgoing-Webhooks-JIRA-Overview-1024x635.png)
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Click **\+ ADD NEW**.

            ![](images/Outgoing-Webhooks-JIRA-Settings-1024x635.png)
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Enter the following details:

            - **Webhook Name.** Enter a name for your webhook that will enable you to easily identify this webhook later when attaching it to one of your alerts.
            - **URL.** Enter the JIRA webhook URL. The JIRA webhook URL is `https://YOUR-ATLASSIAN-DOMAIN.atlassian.net/rest/api/3/issue`.
            - **API Token.** Enter the API token copied in the \[prerequisites\].
            - **Email.** Enter the email used in your JIRA credentials.
            - **Project Key.** Enter the project key from JIRA that you found in the \[prerequisites\].
    ::: custom-list-item marker=5
        ::: list-item-header

        ::: list-item-content
            Click **SAVE & TEST**.

            The system creates a task in your JIRA dashboard to test your configuration. If the task is created successfully, a confirmation message is displayed.
    ::: custom-list-item marker=6
        ::: list-item-header

        ::: list-item-content
            [Configure your alert notifications](../configure-alert-notifications-for-outbound-webhooks/index.md).

## Additional Resources
|               |                                                                                                                        |
|---------------|------------------------------------------------------------------------------------------------------------------------|
| Documentation | [Configure Alert Notifications for Outbound Webhooks](../configure-alert-notifications-for-outbound-webhooks/index.md) |

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
