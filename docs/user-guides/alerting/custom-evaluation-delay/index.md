---
title: "Custom Evaluation Delay"
date: "2025-01-07"
description: "The Custom Evaluation Delay feature helps mitigate false alerts by shifting an alert's evaluation timeframe backward by a configurable amount."
---

## Overview

Lags in your data pipeline can cause delays in log and metric ingestion, potentially leading to false alerts. While alert conditions are evaluated in real-time, delayed data arriving later can retroactively affect whether those conditions are met.

When an alert should be triggered above a user-defined threshold, missing data may delay it or lead to an incorrect resolution. Conversely, when an alert should be triggered below a user-defined threshold, incomplete data can cause a false positive alert or delayed alert resolution.

The **Custom Evaluation Delay** feature helps mitigate this issue by shifting an alert's evaluation timeframe backward by a configurable amount. This adjustment ensures that alert conditions are evaluated against a more complete dataset, accounting for late-arriving logs or metrics. 

By using Evaluation Delay, you can reduce the risk of false positives or negatives caused by real-time data fluctuations and improve the accuracy and reliability of your alerts.


## Configuration

1. Navigate to the Alert creation window. This can be done in two ways:
    1. **Logs** - Perform a query to filter the logs that will be returned as part of the alert. Click **CREATE ALERT** in the upper right-hand corner.
    2. **Logs/metrics/traces** - Click on **Alerts** > **Alert Management** in the Coralogix toolbar. Click **NEW ALERT** on the upper right-hand corner of your dashboard.
2. Click on **Advanced Settings**.
3. Select **Delay alert evaluation** and choose a specific time period for the delay.


## Support

**Need help?** 

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up. 

Feel free to reach out to us **via our in-app chat** or by sending us an email to [<EMAIL>](mailto:<EMAIL>).