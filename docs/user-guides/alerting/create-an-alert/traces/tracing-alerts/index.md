---
title: "Tracing Alerts"
date: "2022-09-15"
coverImage: "alerts.png"
description: "Tracing is one of the fundamental pillars of observability. It allows you to track interactions between all of your applications and understand where performance bottlenecks or errors are occuring.Coralogix allows you not only to visualize your traces, but also set alerts on them. Our tracing alert allows you to be alerted automatically of specific tags and services on the basis of a specified latency."
---

Tracing is one of the fundamental pillars of observability. It allows you to track interactions between all of your applications and understand where performance bottlenecks or errors are occuring.

Coralogix allows you not only to visualize your traces, but also set alerts on them. Our tracing alert allows you to be alerted automatically of specific tags and services on the basis of a specified latency.

## Create a Tracing Alert

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Create a new Alert.
            
            - Click on **Alerts** > **Alert Management** in the Coralogix toolbar.
            - Click **NEW ALERT** on the upper right-hand corner of your dashboard.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Define alert details:

            - **Alert Name.**
            - **Alert Description**.
            - **Priority**. Set alert priority: P1, highest to P5, lowest.
            - **Labels**. Define a new label or choose from an existing one. Nest a label using `key:value`.
            - **Set as Security Alert**. Check this option to to add the `alert_type:security` label. This will help Security customers filter for this alert type in the **[Incidents](../../../incidents/index.md)** screen.
            
            ![](images/Screenshot-2024-07-10-at-15.11.20-1024x785.png)
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Select **TRACING** **Alert Type**.

            ![Tracing Alert Coralogix](images/STEP3-3-1024x742.png)
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Define **Query** parameters.

            ![Tracing Alert Coralogix](images/STEP4-3-1024x969.png)
            
            - **Latency is above (ms)**. Set this value to maximum latency - that is, the latency you are prepared to tolerate. Values greater than this will cause your spans to be counted as part of the data that will trigger your alerts.
            - **\+ ADD SPAN TAG**. You may choose to add span tag to filter your spans. For example, if your span has a label that indicates it is a customer-facing component, you can add this **Key** - **Value** pair to ensure the alert does not consider any of those spans. **Value** may be followed by any of the following: **Is**, **Starts With**, **Includes**, **Ends With** or **Is**.
            
            ![Tracing Alert Coralogix](images/STEP4-1-1-1024x315.png)
            
            - You may choose to add **Application**, **Subsystem** and **Services** filters. Maintain default options by selecting the **All** option. **Note**: Each span may have a different application and / or subsystem.
    ::: custom-list-item marker=5
        ::: list-item-header

        ::: list-item-content
            Define Alert **Conditions**.

            ![Tracing Alert Coralogix](images/STEP5-6-1024x561.png)
            
            - **Alert if**. Select whether to trigger the alert immediately, or define a rule based on the number of occurrences within a specified time window.
                - An alert is triggered whenever the condition threshold is met for a specific aggregated value of the tag selected within the specified timeframe.
                - If using 2 labels for **Group By**, matching metrics will first be aggregated by the parent label (ie. **region**), then by the child label (ie. **pod_name**). An alert will fire when the threshold meets the unique combination of both parent and child. Only metrics that include the **Group By** labels will be included in the count.
    ::: custom-list-item marker=6
        ::: list-item-header

        ::: list-item-content
            Define **Notification** settings. Select how to be notified when an alert is triggered. By default, a single notification, aggregating all values matching an alert query and conditions, will be sent to your Coralogix **Insights** screen.

            ![](images/Screenshot-2024-07-15-at-14.01.58.png)
            
            - **\+ ADD WEBHOOK**. Define additional alert recipient(s) and notification channels.
            - **Notify** **Every**. Sets the alert cadence. After an alert is triggered and a notification is sent, the alert will continue to work, but notifications will be suppressed for the duration of the suppression period.
            - An **individual notification** for each of the values of the **Group By** fields will be sent when query conditions are met. Choose the recipient of the notification and the notification parameters.
            - **Phantom Mode**. Toggle the **Phantom Mode** switch to silence the alert. In the Phantom mode, alerts can serve as building blocks for flow alerts without triggering independent notifications or creating an incident.
    ::: custom-list-item marker=7
        ::: list-item-header

        ::: list-item-content
            Set a **Schedule** for trigger the alert.

            ![Tracing Alert Coralogix](images/STEP7-2-1024x385.png)
    ::: custom-list-item marker=8
        ::: list-item-header

        ::: list-item-content
            To finalize your alert, click **CREATE ALERT**.

## **Support**

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at **[<EMAIL>](mailto:<EMAIL>)**.
