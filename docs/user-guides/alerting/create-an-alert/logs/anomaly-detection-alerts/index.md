---
title: "Anomaly Detection Alerts"
date: "2020-03-05"
coverImage: "Dynamic-Alerts.png"
---
# Anomaly Detection Alerts

**Anomaly detection alerts** utilize artificial intelligence algorithms to analyze incoming logs and predict their expected behavior for 24 hours. When a log falls above or below a predefined threshold, something unusual may have occurred, indicating an opportunity for corrective action.

For example, an anomaly detection alert can help you discover when a transaction’s response time exceeds its usual duration, allowing you to pinpoint and address performance bottlenecks. Or it can alert you when the outgoing traffic of a host exceeds its usual levels, indicating a potential security breach.

Dynamic alerts are powered by our [Streama© technology](https://coralogix.com/how-it-works/), which allows them to run on the Coralogix monitoring pipeline at a third of the cost, without prior indexing.

## Create an alert
Set up a logs-based anomaly detection alert to notify you if a log **exceeds** an AI-generated baseline threshold. 

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Navigate to **Alerts** > **Alert Management**. Click **New Alert**.
::: list-wrapper
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            When defining your alert conditions, select to be alerted when an event is **more-than-usual** compared to the baseline condition.
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Define the [alert conditions](../../../multiple-alert-conditions/index.md).
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Add one or more group-by keys. An alert is triggered whenever the condition threshold is met for a specific aggregated key within the specified time window. Our machine-learning model establishes the baseline standard for every group-by key.
    ::: custom-list-item marker=5
        ::: list-item-header

        ::: list-item-content
            [Optional] Configure the **advanced settings**, including [custom evaluation delay](../../../custom-evaluation-delay/index.md) and [percentage deviation](../../../anomaly-detection-deviation-percentage/index.md).
    ::: custom-list-item marker=6
        ::: list-item-header

        ::: list-item-content
            Finalize the alert setup.

## Limitations

Our machine-learning model establishes the baseline standard for your logs for every group-by key in your alert definition. It is applied daily for the next 24 hours, using data from the past 7 days, and is based on a **maximum of 500** permutations.

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Contact us **via our in-app chat** or by emailing [<EMAIL>](mailto:<EMAIL>).
