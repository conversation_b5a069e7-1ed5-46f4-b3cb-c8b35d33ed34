---
title: "Pricing Model"
date: "2024-02-04"
description: "The Coralogix AI Observability pricing model is designed to align costs with the workload and complexity of AI operations."
---

# Pricing Model

The Coralogix AI Observability pricing model is built around a simple, usage-based metric called Coralogix Units (CUs), designed to align costs with the scale and complexity of your AI operations. Generally, CUs act as the currency within the Coralogix system, offering a flexible and tailored pricing approach. Since pricing is based on data volume, you only pay for what you ingest, eliminating the need for a fixed commitment.

## Benefits

- **Transparency**. Clients can easily understand how their usage, both in data processing and analysis complexity, impacts their costs.
- **Scalability**. The model scales seamlessly with usage, ensuring fair pricing that's directly aligned with the value delivered.
- **Predictability**. With a fixed rate per Coralogix Unit, customers can easily estimate their monthly or project costs.

This simple approach ensures our pricing remains competitive and accurately reflects resource consumption, fostering greater clarity and trust with our customers.

## Calculating Coralogix Unit usage

1. **Understand tokens**. Every piece of text processed by an AI agent is measured in *tokens*. A token represents a chunk of text (words, parts of words, or punctuation).
2. **Define evaluators**. An *eval* refers to an additional layer of processing or analysis applied to the tokens. Multiple evaluators can be used for the same set of tokens, depending on the complexity of the task.
3. **Calculate usage**. To determine the total Coralogix Units used, multiply the number of tokens by the number of evaluators:</br> **Coralogix Units = (Tokens × Enabled Evaluators) / 1,000,000**.

### Example

If an AI agent processes **10,000 tokens** and uses **5 evaluators**, the total consumption is calculated as follows:</br> 
(10,000 tokens × 5 evaluators)/1,000,000 = **0.05 Coralogix Units**.