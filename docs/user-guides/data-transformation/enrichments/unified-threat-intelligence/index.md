---
title: "Unified Threat Intelligence"
date: "2019-03-06"
coverImage: "Security.png"
---

Malware detection is an essential capability for modern businesses that helps them identify threats and malicious activity posing a risk to their environments, investigate them, and respond quickly.

Threat intelligence feeds are trusted sources of information about potential cyber threats, such as malicious activity. With that said, incorporating threat feeds into your data to detect suspicious activity can require complex configuration, often leading to the sub-utilization of this important source of information.

The Coralogix **Unified Threat Intelligence** relies on our [Streama© technology](https://coralogix.com/how-it-works/) to provide you with built-in seamless integration with some of the world’s leading threat intelligence feeds with hundreds of thousands threat entities curated by our security experts.

Coralogix does not require any API integration, special syntax, or format change. It automatically enriches your data with malicious indicators in real-time as they are streamed, allowing you to query, visualize, and alert on potential threats.

Enriched information is then stored to your own remote storage. This allows you to query directly from Coralogix with infinite retention and even research the data with external tools. 

## **How Can the Unified Threat Intelligence Help to Improve My Security?**

Take a look at these use-cases to get a feel for the many ways that our ****Unified Threat Intelligence**** can serve you.

**Use-Case 1: Detection of Phishing Attempts**

Phishing is a social engineering attack practice, causing people to share credentials, sensitive information or install malware by impersonating a legitimate website. The ******Unified Threat Intelligence****** helps you detect any network activity from your organization’s environment to a reported phishing website, allowing you to identify which users were involved and investigate the impact.

**Use-Case 2: Detection of Browsing of Malicious Websites**

Phishing is the number one attack vector to infiltrate your organization by obtaining user credentials. The ******Unified Threat Intelligence****** helps you detect any user visiting a phishing site (through domain/URL) or a phishing site that is hosted on your infrastructure.

**Use-Case 3: Detection of Potential Data Exfiltration**

Malware installed on an internal machine in your environment may gain access to your sensitive data and exfiltrate it by uploading this data to an attacker’s website. The ****Unified Threat Intelligence**** helps you to immediately detect network activities to such websites reported as malicious, block them, and assess the data leakage that may have occurred.

**Use-Case 4: Bot Detection by Command and Control Communication Monitoring**

Hackers use bots to perform large scale attacks. These include denial of service (DDoS) attacks that can flood a website with connection requests, causing it to stop serving legitimate customers, distribute spam emails, make fraudulent purchases, and more. C&C communication is used by hackers to send the operation commands to the bots. The ******Unified Threat Intelligence****** allows you to detect this command and control communication by inspecting your network activity logs and discovering communication to/from command and control servers. Using this feature, you can easily see which machines perform this network activity and are infected with bots.

**Use-Case 5: Detection of Brute-Force Scanning**

Hackers use brute-force scanning to scan your network environment and find exposed resources which allow them to penetrate your environment and attack it. The ******Unified Threat Intelligence****** allows you to detect incoming network traffic coming from IPs and servers reported as malicious. With this improved security posture, you can block those requests, assess any risk and its impact, and immediately decide upon mitigation steps.

## **How Does the ****Unified Threat Intelligence**** Work?**

Threat intelligence feeds are trusted sources of information about potential cyber threats, curated by researchers and security analysts. For example, some feeds contain lists of servers that are detected to be involved in malicious activities. Organizations can use this information to detect any network activity between their environment and these potentially malicious servers, which may result in malware infection and risk to their environment.

### Threat Intelligence Feed List

Coralogix security experts curated this leading threat intel to work with:

- Emerging Threats

- IPsum

- Blocklist.de Blocklist

- Tor Nodes

- URLhaus

- OpenPhish

- Feodo Tracker

- Zerodot1

- Brute Force Blocker

- Brute Force Hosts

- PhishTank

- Talos IP Blacklist

- Coralogix \[internal feed reported by Coralogix security experts\]

## Lookup Malicious Indicators

The ******Unified Threat Intelligence****** searches the IP, URL, and domain values in selected log keys in your network activity logs and checks if they are reported as malicious by these threat intelligence feeds. Coralogix reads these feeds once a day to provide you with immediate, up-to-date detection of newly reported threats.

If an IP, URL, or domain value in your log is reported as malicious, a new field named `<key_name>_suspected` is added to the relevant log, with `key_name` serving as the original log key, as shown in the example below.

The `<key_name>_suspected` field contains the following fields:

| Field           | Description                                                                                                          |
|-----------------|----------------------------------------------------------------------------------------------------------------------|
| malicious_value | Actual value reported to be malicious                                                                                |
| reporting_feeds | List of feeds that reported this value as malicious                                                                  |
| total_feeds     | Includes the number of the feeds in **reporting_feeds** and can be used in queries and alerts as a confidence level. |

```json
{
    "ip": "**************",
    "ip_suspected": {
        "malicious_value": "**************",
        "total_feeds": "4",
        "reporting_feeds": {
            "IPsum": {
                "feed_name": "IPsum"
            },
            "Emerging Threats": {
                "feed_name": "Emerging Threats"
            },
            "Brute Force Blocker": {
                "feed_name": "Brute Force Blocker",
                "last_reported": "2023-07-11 21:44:16",
                "count": "4",
                "id": "2628157"
            },
            "Blocklist.de Blocklist": {
                "feed_name": "Blocklist.de Blocklist"
            }
        }
    }
}
```

This enrichment is extecuted automatically while your logs are being streamed into Coralogix. All you need to do is to define which log keys contain IPs, URLs, or domains that should be looked up in the threat intelligence feeds and enriched upon a match.

To get the best value, it is **recommended** to create alerts based on these enriched logs, to be notified as soon as possible upon detection of potential malicious network traffic and respond immediately.

## **Get Started**

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            In your Coralogix toolbar, navigate to **Data Flow** > **Data Enrichment**.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Select the JSON keys containing IPs, URLs or domains to be looked up and enriched with malicious activity indicators.
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Click **ADD KEY**.

            ![](images/UpdatedUTI-1024x284.png)

            **Note**:
            
            - If your logs don’t have a dedicated field for IP, URL, or domain, or your data isn’t JSON-formatted, you can use our [parsing rules](../../parsing/log-parsing-rules/index.md#extract) to extract these precise values from your log record into a dedicated key, to be looked up in the threat intelligence fields and enriched if they match.
            
            Coralogix will search the IP, URL and domain values in the selected log keys to check if they are reported as malicious using threat intelligence feeds.

## **Explore Malicious Traffic**

After configuring the log keys to be looked up and enriched by the ******Unified Threat Intelligence******, it is **recommended** to use the following methods to gain optimal visibility into malicious network traffic in your environment, be notified in real-time, and respond immediately.

### Query Logs

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            In the Explore screen, run a query to retrieve all enriched logs with `<key_name>_suspected` field.

            For example: `_exists_:"ip_suspected"`
            
            If you want to rely on a specific feed that you trust, search for the enriched field with the name of the specific feed from the list [above](#threat-intelligence-feed-list), as in this example: `_exists_:"ip_suspected.reporting_feeds.IPsum"`.
            
            To get higher confidence, run a query by filtering a minimum value of `total_feeds`. For example, retrieve all enriched logs that were reported as malicious by at least 2 feeds: `ip_suspected.total_feeds.numeric:[2 TO *]`.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Look at the log fields to learn who the user was, which machine was involved and might be infected, and more. Some of the feeds include additional fields with more details about the threat, such as the time when it was first detected as malicious or a link to the feed’s website allowing you to read more details about the particular threat.

            This will allow you to investigate the issue, examine the impact on your organization, assess the risk and plan the remediation steps if required.

### Alerts

To be notified immediately once network traffic to potential malicious server is detected, it is recommended to create an alert to be triggered once a log with `<key_name>_suspected` field is received.  
  
![](https://lh4.googleusercontent.com/SAqU1EajXP0v3sWIc_Wzfo8404cpzZW3vusUaZAkHDhECWUkkvUJzN9qfH84TkA_Gi-v9-28xMFum-TuNvTXNWcCP_yr0Q89YFgiB0kL4-aBrmQGbsvUsIwz2XOXVKdmJO73hpbH4p9g)  

Some of the extensions, such as "Snowbit STA", automatically configure their product log keys to be enriched by the ******Unified Threat Intelligence******, as well as add predefined alerts to notify you immediately upon detection of malicious activity.

### Dashboards

Build [custom dashboards](../../../custom-dashboards/getting-started/index.md) to get an overview of all malicious activities done in your environment, including involved users, infected machines, and trends over time by querying logs that contain the enriched `<key_name>_suspected` field.

## Additional resources
::: additional-content items="id: r6T8MX1Ndwc, title: Simplify Threat Detection with Unified Threat Intelligence"

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
