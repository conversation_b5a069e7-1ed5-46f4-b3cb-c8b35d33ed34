---
title: "APM Onboarding Tutorial"
date: "2024-03-14"
description: "This guide provides step-by-step instructions on configuring and using Coralogix Application Performance Monitoring (APM)."
---

This guide provides step-by-step instructions on configuring and using Coralogix Application Performance Monitoring (APM). Follow these steps to effectively onboard your services, install collectors, correlate resources, and leverage advanced features.

!!! note

    APM presents data assigned to TCO medium role or higher. Make sure to assign the Medium (Monitoring) [TCO pipeline](../../../account-management/tco-optimizer/traces/index.md#tco-data-pipelines) to the traces sent to Coralogix.

## APM basic setup

To set up APM, instrument your service and install the OTel Collector.

### Service instrumentation

To get started with Coralogix APM, generate spans from your services by instrumenting them. Choose the relevant documentation for your service’s programming language:

- [Node.js](../../../../opentelemetry/instrumentation-options/nodejs-opentelemetry-instrumentation/index.md)

- [Golang](../../../../opentelemetry/instrumentation-options/golang-opentelemetry-instrumentation/index.md)

- [PHP](../../../../opentelemetry/instrumentation-options/php-opentelemetry-instrumentation/index.md)

- [Java](../../../../opentelemetry/instrumentation-options/java-opentelemetry-instrumentation/index.md)


### OTel collector installation

Install the OpenTelemetry Collector. The Collector is an intermediary component responsible for aggregating and transmitting data, playing a key role in the overall APM workflow. Once installed, the Coralogix [Events2Metrics](../../../monitoring-and-insights/events2metrics/index.md) feature automatically converts your spans and logs to metrics to be displayed in your UI. Select from any of the following configuration options:

- [Kubernetes Complete Observability using OpenTelemetry](../../../../opentelemetry/kubernetes-observability/kubernetes-complete-observability-basic-configuration/index.md)

- [Docker](../../../../opentelemetry/configuration-options/opentelemetry-using-docker/index.md)

- [EC2](../../../../opentelemetry/configuration-options/install-opentelemetry-on-an-ec2-instance/index.md)

- [ECS-Fargate](../../../../integrations/aws/opentelemetry-ecs-fargate/index.md)

- [ECS-EC2](../../../../opentelemetry/configuration-options/aws-ecs-ec2-using-opentelemetry/index.md)

**APM should run once your service is instrumented and the Collector is installed**.

### Data pipeline

You can enjoy our APM features in multiple ways, using either of our data pipelines: [Events2Metrics](../../../monitoring-and-insights/events2metrics/index.md) and [Span Metrics](../span-metrics/index.md).

We **recommend** configuring span sampling to get the best results using Span Metrics. This allows the user to view traces, service connections, and maps in the Coralogix platform.

## Resource correlation

Investigate service-resource correlation within your Kubernetes environment in your [APM Resources tab](../../features/service-catalog/index.md#resources) or [Kubernetes Dashboard](../../../monitoring-and-insights/kubernetes-dashboard/kubernetes-dashboard/index.md). This correlation relies on span tags and metric labels.

**Span tags** are metadata attached to requests moving through a distributed system, facilitating traceability and correlation. Each microservice adds or propagates span tags, creating a trace that visualizes the request's path, aiding in identifying performance bottlenecks and dependencies.

**Metric labels** are key-value pairs associated with metrics collected from Kubernetes resources. These labels offer additional context to metrics, such as CPU usage or network traffic, making organizing and analyzing data based on service names, pod names, or environments easier.

### Kubernetes OpenTelemetry extension

Coralogix **recommends** the [Kubernetes Complete Observability](../../../../opentelemetry/kubernetes-observability/kubernetes-complete-observability-basic-configuration/index.md) integration for comprehensive Kubernetes and application observability. Deploying the Kubernetes OpenTelemetry [extension](../../../getting-started/packages-and-extensions/extension-packages/index.md) enables telemetry data collection and **full service-resource correlation** in your [APM Resources tab](../../features/service-catalog/index.md#resources) or [Kubernetes Dashboard](../../../monitoring-and-insights/kubernetes-dashboard/kubernetes-dashboard/index.md), **without any additional steps**.

### Kubernetes resource correlation without the OTel extension

If you are not using the [Kubernetes Complete Observability](../../../../opentelemetry/kubernetes-observability/kubernetes-complete-observability-basic-configuration/index.md) integration, take the following steps:

1. Ensure your span tags include the `K8s.deployment.name` label. In case of using StatefulSets or DaemonSets, replace `K8s.deployment.name` with `K8s.DaemonSets.name` or `K8s.StatefulSets.name`, respectively.

3. In cases where metrics or metric labels are missing, you will be prompted to deploy our [Kubernetes Complete Observability](../../../../opentelemetry/kubernetes-observability/kubernetes-complete-observability-basic-configuration/index.md) integration or reload the missing labels and tags.

![](images/Screenshot-2024-02-08-at-15.03.08-1.png)

### ECS resource correlation

Correlate ECS-EC2 or ECS Fargate performance metrics with your service as follows:

- [ECS-EC2](../../../../opentelemetry/configuration-options/aws-ecs-ec2-using-opentelemetry/index.md#opentelemetry-configuration)

- [ECS Fargate](../../../../integrations/aws/opentelemetry-ecs-fargate/index.md)

Service-resource correlation within your Kubernetes environment is presented in your [APM Resources tab](../../features/service-catalog/index.md#resources).

### Correlation using Prometheus

If you are using Prometheus to send us telemetry, ensure that kube state metrics and node exporter metrics are sent by [Prometheus Operator](https://github.com/coralogix/telemetry-shippers/tree/master/metrics/prometheus/operator) to set up metric labels for Kubernetes resource correlation in your Kubernetes Dashboard. You may validate the metrics by following [these instructions](../../../../opentelemetry/integrations/apm-kubernetes/index.md#host-metrics-prometheus).

## Correlate logs with services

Investigate the logs produced by your service by enriching your logs with a [subsystem name](../../../account-management/account-settings/application-and-subsystem-names/index.md) matching the service name. Alternatively, set up the correlation by following [these instructions](../../features/service-catalog/index.md#logs).

## Add your SLOs

Set up your service level objectives by following [these instructions](../../features/../features/service-slos/index.md).

## Advanced

### Instrument serverless functions

Auto-instrument AWS Lambda by following [these instructions.](../../../../opentelemetry/instrumentation-options/opentelemetry-lambda-auto-instrumentation/index.md)

### Tail sampling

Learn about [Tail Sampling with OpenTelemetry using Kubernetes](../../../../opentelemetry/kubernetes-observability/tail-sampling-with-opentelemetry-using-kubernetes/index.md) and [Docker](../../../../opentelemetry/tail-sampling/tail-sampling-with-opentelemetry-using-docker-compose/index.md).

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
