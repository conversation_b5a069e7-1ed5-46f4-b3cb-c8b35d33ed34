---
title: "Pod & Host"
date: "2023-12-31"
description: "Our application performance monitoring (APM) provides you with all logs relevant to a particular span context, granting a full picture of the services that power your applications. Use our newest layers of observability – POD and HOST – to: instantly view all of your pod and host metrics, including resource consumption and associated network information; compare metrics within a specific pod and across pods from a specific service; correlate between Kubernetes spans, logs, and metrics for a specific pod and/or host; troubleshoot log span errors; and annotate deployment tags based on span context."
---

Our [application performance monitoring](https://coralogix.com/blog/application-performance-monitoring-visibility/) (APM) provides you with all logs relevant to a particular span context, granting a full picture of the services that power your applications.

Use our newest layers of observability – `**POD**` and `**HOST**` – to:

- Instantly view all of your pod and host metrics, including resource consumption and associated network information

- Compare metrics within a specific pod and across pods from a specific service

- Correlate between Kubernetes spans, logs, and metrics for a specific pod and/or host

- Troubleshoot log span errors

- Annotate deployment tags based on span context

## **Concepts**

Spans and traces form the basis of your Coralogix APM journey.

Using this telemetry data, Coralogix allows you to observe **application resource consumption** and **infrastructure resource consumption** using two new observability layers.

| POD  | application resource consumption    | Key factors that impact response times and throughput of applications |
|------|-------------------------------------|-----------------------------------------------------------------------|
| HOST | infrastructure resource consumption | Usage of IT resources, systems, and processes                         |

## Access Pod & Host Features

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            In your navigation pane, click **Explore** > Tracing. Click on the trace of interest.
            
            ![](images/trace-of-interest-1024x575.png)
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Click **VIEW RESOURCES →** in your right-hand tool bar.

            ![](images/show-resources-1024x575.png)
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Click on the `POD` and `HOST` tabs, in addition to `RELATED LOGS` and `SPAN LOGS`.

            ![](images/pod-1024x575.png)
            
            ![](images/host-1024x575.png)

## Additional Resources
|               |                                                                                                                                                                                                                              |
|---------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Documentation | [Application Performance Monitoring](https://coralogix.com/blog/application-performance-monitoring-visibility/)<br/>[Distributed Tracing](../../../monitoring-and-insights/distributed-tracing/distributed-tracing/index.md) |
| Blog          | [One Click Visibility: Coralogix Expands APM Capabilities to Kubernetes](https://coralogix.com/blog/application-performance-monitoring-visibility/)                                                                          |

## **Support**

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at **[<EMAIL>](mailto:<EMAIL>)**.
