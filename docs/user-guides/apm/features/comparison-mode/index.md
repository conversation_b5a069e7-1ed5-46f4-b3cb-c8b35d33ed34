---
title: "Metrics Contextualization with APM Comparison Mode"
date: "2024-08-28"
description: "You can use service Comparison mode to view the current requests, errors, or latency compared to data from 1, 2, 7 days ago, previous consecutive period or custom timeframe). Thus, you can see how different service performance metrics change or evolve across different time periods."
---

## Overview

Comparison mode is designed to provide context to your APM metrics data. It allows you to compare the current application performance (requests, errors, or latency) with data from 1, 2, 4, 7 days ago, previous consecutive period or a custom timeframe. This comparison not only gives you a snapshot of the current performance but also helps you understand the evolution of each metric. Using this mode, you can effectively contextualize your metric data, gaining valuable insights to troubleshoot your application performance. Moreover, it allows you detecting service anomalies within a specific timeframe by introducing a trend indicator and turning your service and database catalogs into powerful continuous improvement tools.

## Configuration

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Click the Comparison Mode button, which is available on the following APM-related Coralogix UI pages:
            
            - Service Catalog
            - Overview and Operations tabs in a service drill-down
            - Databases Catalog
            - Overview and Operations tabs in a database drill-down
            
            ![](images/image-29-1024x514.jpg)
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            From the **Compare To** menu, select the time period that the current performance data will be compared to:

            - 1 day ago - Performance data collected 1 day ago.
            - 2 days ago - Performance data collected 2 days ago.
            - 4 days ago - Performance data collected 4 days ago.
            - 7 days ago - Performance data collected 7 days ago.
            - Previous consecutive period - Performance data collected during a previous preset timeframe relative to the present time. For example, if your currently-selected timeframe is **Last 15 minutes**, the performance will be compared to the previous 15-minute period, based on the same clock time.
            - Custom timeframe - Performance data collected during a user-specified period of time. When you choose this option, you must select a date from the calendar view. The timeframe for comparison will be calculated according to:
                - A day selected from the calendar view
                - Current time
                - Interval selected in the time picker
            
            For example, if the current date and time are July 23rd, 14:45:00, and the time picker is set  to **Last 24 hours**, when the user selects July 4th, the current data will be compared with metrics from July 3rd, 14:45:00 to July 4th, 14:45:00, based on the same clock time.
            
            ![](images/image1.png)
            
            Your selection is displayed next to the **Compared To** button. The Service Catalog page displays changes, reflecting improvement (green) or deterioration (red) of the services during selected timeframe.
            
            ![](images/image-30-1024x515.jpg)

