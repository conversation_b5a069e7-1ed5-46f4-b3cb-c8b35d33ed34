---
title: "Quick-Start Extensions"
date: "2020-11-24"
coverImage: "Extensions.png"
description: "Coralogix offers a variety of out-of-the-box data extensions. Each tailored extension unlocks a set of predefined items - alerts, parsing rules, dashboards, saved views, actions, and more - allowing you to jumpstart Coralogix monitoring of your external-facing resources."
---

# Quick-Start Extensions

Coralogix offers a variety of out-of-the-box data extensions. Each tailored extension unlocks a set of predefined items - alerts, parsing rules, dashboards, saved views, actions, and more - allowing you to jumpstart Coralogix monitoring of your external-facing resources.

## Overview

Extension packages provide you with a series predefined features, whose manual setup demands time and resources:

- Predefined alerts, parsing rules, and [Events2Metrics](../../../monitoring-and-insights/events2metrics/index.md)

- Predefined OpenSearch and Grafana dashboards

- Coralogix internal [Custom Dashboards](../../../custom-dashboards/getting-started/index.md)

- Built-in saved views in your [Explore Screen](../../../monitoring-and-insights/logs-screen/logs-in-explore-screen/index.md)

- Enrichments

- Default actions

## Deploy an Extension

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            In your navigate pane, click **Data Flow** > **Extensions**.

    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Select the extension of choice by scrolling through the page or searching for one in a free text search, using **Label** and **Status** filters.
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Click **+DEPLOY** on the extension of choice.
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Define the parameters of extension deployment.
            
            - Select **Version**.
            - Select **Applications** and **Subsystems** for which alerts and parsing rules will be applied.
    ::: custom-list-item marker=5
        ::: list-item-header

        ::: list-item-content 
            Select the extension items you wish to install.
          
            - By default, all items are selected. Choose **Select All** as per our **recommendation** or choose selectively based on your personal needs.
            - Hover over any of the items to view a brief summary. Click on it to preview the settings in their entirety.
    
    ::: custom-list-item marker=6
        ::: list-item-header

        ::: list-item-content
            Click **\+ DEPLOY**.
    ::: custom-list-item marker=7
        ::: list-item-header

        ::: list-item-content
            You will receive a pop-up message summarizing all the features to be deployed as part of the extension package.

            **Notes**:
            
            - The number of package items is limited per team.
            
            - The **\+ DEPLOY** button will be inactive if your current deployment selections will cause you to exceed your limit of package items. Follow the instructions in red and unselect items as necessary in order to complete the deployment.
                - For example, each team is allowed 35 parsing rules. If you attempt to deploy an extension package with 3 rules once your team has used 34 parsing rules in total, you will be required to unselect at least 2 of the 3 rules in the current extension package in order to activate the **\+ DEPLOY** button.
            
                - To expand the number of package items - **Rules**, **Alerts**, and **Dashboards** - available to your team, contact us via our in-app chat or by sending us an email to [<EMAIL>](mailto:<EMAIL>).
            
            - Once the necessary items have been removed, the **\+ DEPLOY** button will be activated.

## Update an Extension

**When you access your Extension page, you may encounter a message that one or more of your deployed extensions requires an update**. **This occurs when a newer version of the extension has been published, following additions or modifications.**

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            In your navigate pane, click **Data Flow** > **Extensions**.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Select the extension of choice by scrolling through the page or searching for one in a free text search, using Label and Status filters.

    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Click **↑UPDATE** on the extension of choice.
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            In the Extension page, NEW or UPDATED package items will be automatically selected. Unselect any undesired items.
    ::: custom-list-item marker=5
        ::: list-item-header

        ::: list-item-content
            Click **↑UPDATE.**

            **Note:** If the extension was deployed using a deprecated installation method, the ↑**UPDATE** button may be greyed out. In this case, you need to remove the extension and reinstall it, before you can update it.

    ::: custom-list-item marker=6
        ::: list-item-header

        ::: list-item-content
            You will receive a pop-up message summarizing all the features to be modified as part of the update. Click **UPDATE**.

## Uninstall an Extension

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            In your navigate pane, click **Data Flow** > **Extensions**.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Select the extension of choice by scrolling through the page or searching for one in a free text search, using Label and Status filters.
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Click **REMOVE**. This will remove the extension and all associated items deployed as part of the extension package.

## Limitations

Teams are bound by the following restrictions:

- [Parsing Rules](../../../data-transformation/parsing/log-parsing-rules/index.md): 30

- [Alerts](../../../alerting/introduction-to-alerts/index.md): 500

- [Anomaly Detection Alerts](../../../alerting/create-an-alert/logs/anomaly-detection-alerts/index.md): 10

- Enrichments: 8

If deploying an extension package exceeds these limits, you will need to remove existing items or contact **Coralogix Support**.

## Learn more
::: additional-content items="id: lk8PciWDM_c, title: Coralogix Extensions"

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
