---
title: "Get Started with Coralogix"
date: "2023-11-21"
coverImage: "Cx_large.png"
description: "To get started with Coralogix, sign up for a free account and import your system's telemetry. Once data is ingested by our platform, you can use our core features to obtain full observability in your Coralogix dashboard."
---

Coralogix is a cloud-based, SaaS analytics and monitoring platform that combines [logs, metrics, and traces](https://coralogix.com/blog/understand-three-pillars-observability/) to gain full observability into your system using one tool. The platform ingests data from any digital source and transforms it using our [core features](../coralogix-features-tour/index.md), allowing you to fully understand your system, analyze that data efficiently, and respond to incidents before they become problems.

To get started with Coralogix, sign up for a free account and import your system's telemetry. Once data is ingested by our platform, you can use our core features to obtain full observability in your Coralogix dashboard.

![](images/1-3.png)

## **Sign Up for a Free Account**

Coralogix offers free account setup. If you don't already have an account, you can sign up [here](https://dashboard.eu2.coralogix.com/#/signup). You will be prompted to create a new team.

![](images/2-3.png)

If your organization already has an account, you may have different signup options depending on the permissions set by your organization administrator.

## **Send Data to Coralogix**

Coralogix supports logs, metrics, and traces from many different sources using any of the following [integrations](https://coralogix.com/integrations/). All integrations require:

- Your Coralogix [Send-Your-Data API key](../../account-management/api-keys/send-your-data-api-key/index.md)

- An [endpoint](../../../integrations/coralogix-endpoints.md) associated with your Coralogix account [domain](../../account-management/account-settings/coralogix-domain/index.md)

- [Application and subsystem names](../../account-management/account-settings/application-and-subsystem-names/index.md) to organize the data in your Coralogix account

### Integration Packages

The **easiest method** for sending us your data is using our two-step, out-of-the box [integration packages](../packages-and-extensions/integration-packages/index.md).

![](images/3-2.png)

### Integrations (Interactive)

For those integrations which are yet to be packaged, select a shipper for which to send us your data from our full list of [integrations](https://coralogix.com/integrations/).

- **Cloud-Based Integrations**. We offer a wide range of cloud-based shippers, including AWS, Azure, and GCP integrations.

- **Telemetry Shippers**. Choose from our many shippers, including integrations using [OpenTelemetry](../../../opentelemetry/getting-started/index.md), [Prometheus](../../../integrations/prometheus/prometheus-server/index.md), [Fluentd](../../../integrations/files/fluentd/index.md) and [Fluent Bit](../../../integrations/files/fluent-bit/index.md).

- **Push & Pull** **Integrations**. Choose from our list of push and pull integrations, including [Cloudflare](../../../integrations/cdns/cloudflare/index.md), [Nagios](../../../integrations/metrics/nagios/index.md), and [Okta](../../security/security-data-sources/okta-contextual-logs/index.md).

- **Use-Cases.** Select a use-case integration on the basis of the particular logs, metrics, or traces you'd like to send us.

![](images/4-2.png)

## Coralogix APIs

Optimize Coralogix's observability monitoring and unlock its most powerful features by using our wide range of [APIs](../../../developer-portal/apis/getting-started/getting-started-with-coralogix-apis/index.md). Use them to send data to Coralogix, build visualizations, manage your data, and query it.

- **Data Ingestion APIs.** Data is ingested seamlessly and reliably into the Coralogix platform using a wide range of APIs.

- **Data Management APIs.** Configure the Coralogix platform, customize your user interface, and optimize it for your observability requirements.

- **Data Query APIs**. Use these APIs to access and query your data.

Our APIs can be configured using our Helm charts, Terraform modules, or the [Coralogix Operator](../../../developer-portal/infrastructure-as-code/coralogix-operator/index.md).

## Get Familiar with Coralogix Features

Once you have started sending us your data, Coralogix offers a rich bank of [extensions](../packages-and-extensions/extension-packages/index.md) to enrich your data with a set of predefined items – alerts, parsing rules, dashboards, saved views, actions, and more. Take our [features tour](../coralogix-features-tour/index.md) to better understand these concepts and kick-start your observability monitoring process.

![Concept image showing the product working with data](images/intro-to-siem-technical-700x389.jpg)

## Coralogix Academy

Dive into Coralogix and discover the platform's vast capabilities tailored for both new and experienced users. Our newest [Coralogix Academy Course](https://coralogix.com/academy/get-to-know-coralogix/) will introduce you to Coralogix's main features and functionalities, so that you're ready to utilize the platform effectively. Through straightforward lesson plans, interactive sessions and practical exercises, you'll gain a complete understanding of how to turn Coralogix into an asset for all your observability operations.

## Additional resources
::: additional-content items="id: 7gbeX0k1Z5Y, title: Introduction to the Observability World; id: xSapiCXKfwU, title: What Are Logs?; id: HXQOaCiaEEs, title: What Are Metrics?; id: NhSWlbv1cAo, title: What are Traces?"

## Support

**Need help?**

Our world-class customer success team is available 24/7 to answer any questions that may come up. Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
