---
title: "Flow Anomaly"
date: "2016-08-11"
coverImage: "Flow-Anomaly-1000X1000.png"
noindex: true
search:
  exclude: true
---

When we examined the Log Analytics market, we saw quite a few companies with great products for indexing and visualizing logs. However, the competition between all these tools was narrowed to who makes the most flexible query language or who is the fastest in indexing log data. In other words: "who applies the most brute force to big data?".

The problem with this approach was that Log Analytics users didn’t really know how to make the best out of the valuable log data they collect since they had to know what to search for and in what timeframe. Moreover, they reacted to their production problems instead of proactively tackling them.

Our goal at Coralogix is to disrupt this market with a whole new approach: get the data you need by push, and not by pull.

Coralogix automatically learns the system’s log sequences in order to detect production software problems in real-time. The algorithm identifies which logs arrive together and in what arrival ratio and alerts the user in case this ratio was broken.

An example from one of our customers was a pattern that consisted of 3 logs that always arrived together with a ratio of 33% for each log within the sequence:

1. About to send data to customer ID XXXXX in X seconds

3. Sending data to customer ID XXXXX

5. Total data sent to customer ID XXXXX is X KB

In this case, Coralogix detected a production bug in which data wasn’t sent to customers, this bug was reflected by the absence of log #2 describing the sending process. What Coralogix found was that log 1# arrived and then log #3 arrived with the value 0 for the amount of data sent in KB. Our user was notified in real-time and the problem was solved (one web server was badly configured).

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
