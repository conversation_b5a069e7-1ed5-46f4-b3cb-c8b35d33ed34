---
title: "LiveTail"
date: "2017-05-20"
coverImage: "Live-Tail-1000X1000.png"
---

Coralogix provides a real-time pre-index **LiveTail**, allowing you to view all your logs from all your servers in one place and with zero latency. 

1) Click on **LiveTail** in the top dashboard menu.

2) Filter your data by Application (environment) and Subsystem (Component), or simply hit "Start" to view all your logs at once:

![](images/Screen-Shot-2021-11-22-at-14.47.40-1024x552.png)

3) View all your logs in real-time and filter them by any |grep/text/regex query you want. In case your logs are structured JSON logs, you can use "Choose fields" to present only the chosen fields on the monitor

![](images/Screen-Shot-2021-11-22-at-14.52.17-1024x554.png)

4) Choose "Prettify" to structure your logs as objects and use formatting

![](images/Screen-Shot-2021-11-22-at-14.54.29-1024x557.png)

![](images/Screen-Shot-2021-11-22-at-14.53.41-1024x551.png)


**Examples**

The following example shows how text and regex search can be combined. First, a case-insensitive regex search of "opentelemetry.io" is performed. The search results are then filtered with an exact text search of "schemas". Finally, a case-insensitive search of "TimE" is applied to the filtered results.
The search results are highlighted as shown in the screenshot below.

```
| grep -i opentelemetry\.io | grep "schemas" | grep -i TimE
```

![](images/livetail_combined_query.png)