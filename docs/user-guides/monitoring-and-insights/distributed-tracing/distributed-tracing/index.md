---
title: "Distributed Tracing"
date: "2022-04-03"
coverImage: "Screen-Shot-2022-04-03-at-14.55.40.png"
description: "Coralogix has created a unique tracing experience, allowing you to observe and gain instant insights into your modern micro-services infrastructure. Use our updated Tracing function, alongside Logs and Templates, to enjoy powerful data visualization of your traces and spans."
---

Coralogix has created a unique tracing experience, allowing you to observe and gain instant insights into your modern micro-services infrastructure. Use our updated **Tracing** function, alongside **Logs** and **Templates**, to enjoy powerful data visualization of your traces and spans.

Enjoy distributed tracing using our **Tracing** function. Optimize performance, troubleshoot bottlenecks, monitor latency, and link traces to logs in an improved visual format.

## What is tracing?

Tracing refers to the ability to trace the flow of a request or transaction through a system. Tracing provides a detailed view of how a request moves through various components of a system, helping to identify bottlenecks and performance issues and understand the overall system behavior.

Traces are made up of spans. A span represents a unit of work in a trace. It encapsulates information about a specific operation within the system, such as a method call or an HTTP request. Spans have a start time and duration and may contain additional metadata.

## Query traces and spans

To access our **Tracing** function:

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            From the Coralogix navigation toolbar, click **Explore** > **Tracing**.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Choose whether you want to view spans or traces by clicking the **SPANS**/**TRACES** button in the toolbar. View only Frequent Search (high priority) data or data of all [priority levels](../../../account-management/tco-optimizer/traces/index.md) (Frequent Search, Monitoring, and Archive).
            
            ![](images/Screenshot-2024-07-18-at-14.27.45.jpg)
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Search for traces/spans of interest with the relevant filters. For spans, you can use Lucene or [DataPrime](../../../../dataprime/introduction/welcome-to-the-dataprime-reference/index.md) to run queries.

            **Notes**:
            
            - When viewing traces, we will present the 50 latest traces that meet your specified filter criteria. Each displayed row is a trace, which may have multiple spans within it.
            - When viewing spans, 15,000 rows are displayed in either ascending or descending order based on the timestamp. Each row is a single span.
            - Hover over the trace or span number and click the ellipsis that appears to visualize the data, copy the ID, view the raw span, or export as a JSON or CSV file.

## Filter

On the left-hand sidebar, you will find the following default filters:

- **[Application & Subsystem](../../../account-management/account-settings/application-and-subsystem-names/index.md)**

- **Action**

- **Service**

- **Duration.** The **Duration** filter allows you to find traces that last a long time or are within a certain min-max range that you can easily define, either by adjusting the double range slider from each side or by inputting the exact time in milliseconds in the start-end boxes above the slider.

- **Teams.** When a user is a member of multiple teams, they can search for a trace across multiple teams in a single action. The list of teams will be displayed under the **Teams** filter.

Once you have added all of the relevant filters, click APPLY.

## Save your traces view

If you would like to use the same filters and column order in the future:

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Click **SAVE VIEW** at the top of the grid.
            
            ![](images/Tracing_Save_View.jpg)
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Enter a name for your new view.
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Select whether you want to keep the view private or share it with your team.
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Select whether you would also like to save the query parameters.
    ::: custom-list-item marker=5
        ::: list-item-header

        ::: list-item-content
            Select if you want to set this as your default view.
    ::: custom-list-item marker=6
        ::: list-item-header

        ::: list-item-content
            To access your saved views or your team’s public views, click **SAVE VIEW**. Once the View Menu pops up, scroll down and click on the name of your view. You can toggle between ‘**All views**’/’**My views**’, or search views by text.

            **Note:** When you save the view, you save the view for the Logs and Tracing tabs at the same time.

## Aggregation function

In the tracing screen, the graphs in the top section give you the ability to calculate statistics using any of the supported arithmetic options: Count, AVG, MIN, MAX SUM and Percentiles (50th / 95th / 99th). You have the option of Grouping by: Application, Subsystem, Service and Action.

The tracing screen has 3 default graphs:

- **Max Duration** grouped by **Action**:

![](images/image-6.png)

- **Spans** (count) grouped by **Application**:

![](images/image-10.png)

- **Errors** (count) grouped by **Service**:

![](images/image-11.png)

Choosing any aggregation other than Count will change the Y scale units of the graph into milliseconds.

![](images/AvgGraph-2.png)

## Visualize traces and spans

Click on a trace of interest to view its underlying spans. Select your preferred visualization mode — **Dependencies**, **Gantt**, or **Flame** view — to explore varied views of the span data.

Find out more [here](../visualize-traces/index.md).

## APM features

Choose RELATED DATA in your **Overview** Pane to access our [APM](../../../apm/getting-started/introduction-to-apm/index.md) features: related logs, events, pod, and host.

![](images/image-26.png)

![](images/image-20.png)

**Pairing spans with related logs**

Define the mapping between the trace spans to the related logs by accessing the RELATED LOGS sub-screen and clicking **Setup Correlation**. This will open a menu where you will be able to add the relevant field containing the span ID.

![](images/Untitled-7.png)

![](images/image-22.png)

Use the POD feature to enjoy additional visualized information.

![](images/image-23.png)

Use the HOST feature to explore host performance.

![](images/image-24.png)

There is also the option to be redirected to the relevant logs by clicking on Open Logs Query. This will direct you to a Logs screen, showing you the log containing the correlated span ID.

![](images/Untitled-8.png)

<!--- Without header -->
::: additional-content items="id: ne80DB-T8mk&t=3s, title: Introduction; id: SIUrrTHnBRo&t=2s, title:  Customizing the Tracing UI; id: 0Q2q0Etr7eA&t=2s, title: Archive querying traces; id: x8C03PNnQcc, title: Diving into a trace; id: zWSb48S0wPM&t=1s, title: Use-case: Finding slow error spans"
