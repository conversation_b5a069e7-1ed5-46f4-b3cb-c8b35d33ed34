---
title: "Logs Info Panel"
date: "2025-03-05"
description: "Logs Info Panel provides a seamless and consistent cross-platform experience for analyzing detailed log data. This powerful feature simplifies your interaction with logs by offering enhanced insights, streamlined navigation, and flexible viewing options to make troubleshooting faster and more effective.
Use it to highlight key labels and tags, organize related logs, view resource metrics and telemetry data, and correlate logs with traces and other critical information for comprehensive analysis."
---

## Overview

**Logs Info Panel** provides a seamless and consistent cross-platform experience for analyzing detailed log data. This powerful feature simplifies your interaction with logs by offering enhanced insights, streamlined navigation, and flexible viewing options to make troubleshooting faster and more effective.
Use it to highlight key labels and tags, organize related logs, view resource metrics and telemetry data, and correlate logs with traces and other critical information for comprehensive analysis.

### Key Benefits

- **Kubernetes OTEL-Specific -** Optimized for Kubernetes OTEL data, ensuring compatibility and focus.
- **Enhanced Context -** Correlate logs and traces with resource metrics, like memory and CPU usage of nodes and pods, to provide a holistic view of system behavior.
- **Simplified Navigation -** Group and display related logs based on chosen attributes. Easily navigate between the logs.
- **Flexible Viewing Options -** View your logs in a format that suits your workflow needs best — JSON, raw, or table.

### Use cases

- **Troubleshooting Pod Failures**
    
    Quickly identify the root cause of pod failure by viewing all of the relevant information in one place. Logs Info Panel highlights key labels like **severity**, **pod name**, **namespace**, and **container name** and provides correlated traces.
    
- **Debugging Application Latency**
    
    Pinpoint the component that is causing a delay in your application by deep diving into logs associated with slow-performing workloads. View related logs for a holistic view.
    

## Prerequisites

To make the most out of Logs Info Panel, ensure that your [Kubernetes OTEL integration](../../../../opentelemetry/integrations/introduction-to-kubernetes-observability-using-opentelemetry/index.md) is active. 

## Log details

This section highlights the main log labels, provided both by the integrations (e.g., `Pod`, `Node`, `Container`,`Cluster`) and our platform (e.g., `Severity`, `Timestamp`).

To view log details:

1. Navigate to [Explore](../../../../user-guides/monitoring-and-insights/logs-screen/logs-in-explore-screen/index.md).
2. Focus on a log and press the space key to open a side panel.

![log_info_panel_1.png](./images/log_info_panel_1.png)

![log_info_panel_2.png](./images/log_info_panel_2.png)

### Log message

The log message content will contain the first non-null value of the following keys, based on OTEL log structure conventions: `body`, `msg`, `message`. If none of the keys exist in the log, this section will not be available.

## View and filter related logs

Navigate to the “Related logs” tab in your log side panel. Click on the “By” field to select attributes, like `TraceID`, `Pod`, `Container`, etc. , that share common values with the selected log.

The Related logs table displays logs filtered by a 5 second time window on either side of the selected log’s timestamp, and by the selected attributes’ values. The maximal amount of logs in this table is 500.

![log_info_panel_3.png](./images/log_info_panel_3.png)

## Explore traces

If trace data is available, click on a Trace ID to view detailed [trace data](../../../../user-guides/monitoring-and-insights/distributed-tracing/distributed-tracing/index.md).

![log_info_panel_4.png](./images/log_info_panel_4.png)

![log_info_panel_5.png](./images/log_info_panel_5.png)


## View resource metrics

Click on the Resource tab to access the integrated resource metric data.

![resource_tab.png](./images/resource_tab.png)


## Explain Log

Simplify your log analysis process by [using our AI agent](../../../../user-guides/cora/explain-log/index.md) to provide a clear explanation about your log entries, detailing their significance, immediate impact, and potential causes (for logs with a warning level or higher). To access this feature, click on “Explain Log” at the top right corner of the panel.

![explain_log.png](./images/explain_log.png)


## Navigate between logs

To navigate between adjacent logs, use the up and down arrow keys located in the top right corner of the panel.

![navigation.png](./images/navigation.png)


## Support

**Need help?** 

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up. 

Feel free to reach out to us **via our in-app chat** or by sending us an email to [<EMAIL>](mailto:<EMAIL>).