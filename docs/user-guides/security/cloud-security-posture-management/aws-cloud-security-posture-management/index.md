---
title: "AWS Cloud Security Posture Management (CSPM)"
date: "2022-07-17"
coverImage: "CSPM.png"
---

Cloud Security Posture Management (CSPM) helps to mitigate and minimize cloud data security breaches and to assess the overall posture of the entire cloud environment against best practices and compliance standards to help remediate issues.

CSPM tools verify that cloud configurations follow security best practices and compliance standards such as CIS, Azure, and GCP benchmarks as well as PCI and HIPAA frameworks. As companies are increasingly moving to the cloud, CSPM is becoming a necessary aspect of security insights.

## API Keys
To use these tools you need to create the following API keys:

1. [Send-Your-Data API Key](../../../account-management/api-keys/send-your-data-api-key/index.md)
2. (Optional) A personal or team [API key](../../../account-management/api-keys/api-keys/index.md). It’s recommended to use permission presets, as they are automatically updated with all relevant permissions. Alternatively, you can manually add individual permissions.

    | Preset | Action | Description |
    | --- | --- | --- |
    | Enrichments | `CLOUD-METADATA-ENRICHMENT:READCONFIG` <br> `CLOUD-METADATA-ENRICHMENT:UPDATECONFIG` <br> `GEO-ENRICHMENT:READCONFIG` <br> `GEO-ENRICHMENT:UPDATECONFIG` <br> `SECURITY-ENRICHMENT:READCONFIG` <br> `SECURITY-ENRICHMENT:UPDATECONFIG` <br> `TEAM-CUSTOM-ENRICHMENT:READCONFIG` <br> `TEAM-CUSTOM-ENRICHMENT:READDATA` <br> `TEAM-CUSTOM-ENRICHMENT:UPDATECONFIG` <br> `TEAM-CUSTOM-ENRICHMENT:UPDATEDATA` | View AWS Enrichment Configuration <br> Manage AWS Enrichment Configuration <br> View Geo Enrichment Configuration <br> Manage Geo Enrichment Configuration <br> View Unified Threat Intelligence Enrichment Configuration <br> Manage Unified Threat Intelligence Enrichment Configuration <br> View Custom Enrichment Configuration <br> View Custom Enrichment Data <br> Manage Custom Enrichment Configuration <br> Manage Data for Custom Enrichment Ingestion |


## Installation

The CSPM can be installed using the following methods:

1. Docker container

3. Kubernetes CronJob

For each installation method, we need to pass the following environment variables:

| Environment Variable | Description |
| --- | --- |
| API_KEY | Send-Your-Data API key |
| APPLICATION_NAME | Set the application name |
| SUBSYSTEM_NAME | Set the subsystem name |
| COMPANY_ID | The Company ID from the settings screen in your Coralogix account |
| CORALOGIX_ENDPOINT_HOST | A Coralogix [gRPC endpoint](../../../../integrations/coralogix-endpoints.md#grpc) associated with your [Coralogix domain](../../../account-management/account-settings/coralogix-domain/index.md) |
| CLOUD_PROVIDER | The Cloud Provider that CSPM will be deployed to in lowercase (e.g. aws, gcp, etc) |
| TESTER_LIST | If specified, will run the tests on the specified service, otherwise, will run tests on all the AWS services. Leave empty to run all testers, otherwise, use comma separated tester names without spaces. |
| REGION_LIST | If specified, will check only the specified regions (for global services like AWS S3, IAM and Route53, make sure you add the "global" region). Otherwise, the tests will be conducted in all regions. Leave empty to run on all regions, otherwise, use comma separated region names without spaces. |
| AWS_DEFAULT_REGION | AWS default region for authentication |
| ROLE_ARN_LIST | An additional role(s) that can be assumed from other AWS accounts to scan. Leave empty to run on the current account. For additional accounts, add comma-separated role ARNs without spaces. **Note**: please follow the instructions below for multi-account configuration. |
| CORALOGIX_ALERT_API_KEY | (Optional parameter) When providing this variable, a custom enrichment for failed resources will be created in Coralogix's account at the end of each run if specified.  |


## Installing as a Docker container

### Prerequisites

1. EC2 Instance
    - The minimum Instance Type should be "**t3a.medium**"
    
    - Internet connectivity (thorough Internet/NAT Gateway)

3. IAM Role with the [following policy](https://github.com/coralogix/snowbit-cspm-policies/blob/master/AWS/cspm-aws-policy.json) attached to the instance

5. Docker installed on the EC2 instance - refer to [Docker documentation](https://docs.docker.com/engine/install/ubuntu/)

Note that the instance type will affect the run time, so it's up to a personal preference and is affected by the environment size.

### Deploying

After prerequisites are met, download the docker image using the following command (if the following command hasn't run, the image will still be downloaded automatically in the next step):

``` bash
docker pull coralogixrepo/snowbit-cspm
```

In order to automate the process, use Crontab in the following manner:

Create the crontab using your favorite editor

``` bash
sudo crontab -e
```

Inside the document, on the bottom, paste the following one-liner (note that the API_KEY and the CORALOGIX_ENDPOINT_HOST fields are mandatory)

```
*/10 * * * * docker rm snowbit-cspm ; docker rmi coralogixrepo/snowbit-cspm ; docker run --name snowbit-cspm -d -e PYTHONUNBUFFERED=1 -e CLOUD_PROVIDER="choose_the_cloud_provider - aws / gcp" -e AWS_DEFAULT_REGION="eu-west-1" -e CORALOGIX_ENDPOINT_HOST="coralogix_grpc_endpoint" -e APPLICATION_NAME="application_name" -e COMPANY_ID=<coralogix_company_ID> -e SUBSYSTEM_NAME="subsystem_name" -e TESTER_LIST="" -e API_KEY="<send_your_data_api_key>" -e REGION_LIST="" -e ROLE_ARN_LIST="" -e CORALOGIX_ALERT_API_KEY="" --network host -v ~/.aws:/root/.aws coralogixrepo/snowbit-cspm
```

The above command will **try** to run every 10\* minutes, and consists of two commands:

- docker rm snowbit-cspm - removes the last docker container if exists

- docker rmi coralogixrepo/snowbit-cspm - will remove and redownload the docker images to ensure it have the latest images locally available.

- docker run --name snowbit-cspm \[Options...\] - runs a new container sequence

\*\* The actual scheduling is set in the Coralogix security tab - scroll to the "**Configuring the scan settings**" section for further details

## Installing as a Kubernetes CronJob in EKS – via kubectl and eksctl

In order to deploy the image in a pod that follows the principle of least privileged, we should use a service account that assumes the needed role at the start of each job and doesn't inherit its permission from his host node.

### Prerequisites

1. An existing Amazon EKS cluster

3. Version 2.7.21 or later or 1.25.46 or later of the AWS CLI installed and configured on your device or AWS CloudShell

5. The **kubectl** command line tool is installed on your device or AWS CloudShell

7. The **eksctl** command line tool is installed on your device or AWS CloudShell

9. An existing kubectl config file that contains your cluster configuration

11. IAM policy with the [following permissions](https://github.com/coralogix/snowbit-cspm-policies/blob/master/AWS/cspm-aws-policy.json)

### Before Deploying

Determine whether you have an existing IAM OIDC provider for your cluster:

- Retrieve your cluster's OIDC provider ID and store it in a variable:

``` bash
oidc_id=$(aws eks describe-cluster --name my-cluster --query "cluster.identity.oidc.issuer" --output text | cut -d '/' -f 5)
```

- Determine whether an IAM OIDC provider with your cluster's ID is already in your account

``` bash
aws iam list-open-id-connect-providers | grep $oidc_id
```

Note: If the output is returned from the previous command, then you already have a provider for your cluster and you can skip the next step. If no output is returned, then you must create an IAM OIDC provider for your cluster

### Deploying

If the output from the above test is blank, create an IAM OIDC identity provider for your cluster with the following command. Replace "my-cluster" with your own value

``` bash
eksctl utils associate-iam-oidc-provider --cluster my-cluster --approve
```

Configuring a Kubernetes service account to assume an IAM role (the policy ARN is the policy created in section 6 of the above prerequisites)

``` bash
eksctl create iamserviceaccount --name my-service-account --namespace default --cluster my-cluster --role-name "my-role" --attach-policy-arn arn:aws:iam::************:policy/my-policy --approve
```

Use the following CronJob configurations:

``` yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: snowbit-cspm-cronjob
  namespace: <optional - when using the default, remove entire row>
spec:
  schedule: "*/10 * * * *"
  successfulJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: <my-service-account>
          hostNetwork: true
          containers:
          - image: coralogixrepo/snowbit-cspm
            name: snowbit-cspm-cronjob
            command: ["python3"]
            args: ["lambda_function.py"]
            env:
              - name: API_KEY
                value: "send_your_data_api_key" 
              - name: CORALOGIX_ENDPOINT_HOST
                value: "coralogix_endpoint"
              - name: CLOUD_PROVIDER
                value: "choose_the_cloud_provider - aws / gcp"
              - name: APPLICATION_NAME
                value: "application_name"
              - name: SUBSYSTEM_NAME
                value: "subsystem_name"
              - name: TESTER_LIST
                value: ""
              - name: REGION_LIST
                value: ""
              - name: ROLE_ARN_LIST
                value: ""
              - name: COMPANY_ID
                value: "<coralogix_company_ID>"
              - name: PYTHONUNBUFFERED
                value: "1"
              - name: AWS_DEFAULT_REGION
                value: "eu-west-1"
              - name: CORALOGIX_ALERT_API_KEY
                value: ""
          restartPolicy: OnFailure
```

Save the above content into a .yaml file and execute the following command:

``` bash
kubectl create -f Cronjob.yaml
```

## Additional configurations and permissions

Several tests for AWS services necessitate specific permissions. If you are utilizing any of these services and would like them to be included in the testing, it is essential to adjust your settings to grant the required permissions. Below is a list of the necessary adjustments for each service. Please ensure these permissions are configured to enable comprehensive testing.

### EKS (Elastic Kuberentes Service)

several of the tests are using additional tools that communicating with the clusters using `kubectl`. In order to process actions successfully, your clusters’ configmap `aws-auth` must include CSPM’s IAM role Under `mapRoles` in the following structure:

``` yaml
- rolearn: arn:aws:iam::<ACCOUNT_ID>:role/<ROLE_NAME>
  username: <ROLE_NAME>
  groups:
  - system:masters

```

## Configuring scan settings

Inside the security tab in your Coralogix account, you will find the **SCAN SETTINGS** button:

1. Scan Now will start the scan of the selected environment(s) in up to 10 minutes from pressing (according to the configured cronjob)

3. Scan Schedule allows choosing the frequency of the scans, and the start time of each scan. the default is every 24 hours

![](images/Screenshot-2023-02-17-at-0.58.41-1.png)

## Multi-account configuration

**Definitions**

- Primary Account - the account that the CSPM is initially was deployed at

- Additional Account - an account that you wish to be scanned on top of the Primary Account

#### For each additional account

1. Go to IAM > Roles and click "Create role"

3. Choose "AWS account"

5. Below, choose "Another AWS account" and paste the account ID of the primary account

7. For permissions, create and add the [following policy](https://github.com/coralogix/snowbit-cspm-policies/blob/master/AWS/cspm-aws-policy.json)

9. Give the Role a name and Click "Create role"

11. Go to the role and copy his ARN

#### For primary account

1. Add the [following policy](https://github.com/coralogix/snowbit-cspm-policies/blob/master/AWS/primary_account/assume_roles_permission.json) to the existing CSPM Role (done in the initial deployment)

3. Fill the **resource** key according to the amount of roles to assume

Example for one additional account:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "CSPMMultiAccountAccess",
            "Effect": "Allow",
            "Action": "sts:AssumeRole",
            "Resource": "arn:aws:iam::************:role/cspm_additional_account_to_scan"
        }
    ]
}
```

#### Add the environmental variable to the Docker\\Kubernetes command

1. use the **ROLE_ARN_LIST** environmental variable as listed above to point each run to use the role ARN in all the additional accounts.

3. when using more than one account, use comma-separated strings without spaces.

## Limitations

CSPM does not support [Scopes](../../../account-management/user-management/scopes/index.md). To restrict user access to CSPM test results—such as those specific AWS accounts or resources—follow these steps:

1. Deactivate the user’s CSPM [permissions](../../../../user-guides/account-management/user-management/create-roles-and-permissions/index.md) in the `SNOWBIT` preset:

    | Description | Action |
    | --- | --- |
    | View CSPM Data | `SNOWBIT.CSPM:READ` |
    | View Snowbit Overview Data | `SNOWBIT.OVERVIEW:READ` |
    | View Snowbit Resource Explorer Data | `SNOWBIT.RESOURCE-EXPLORER:READ` |
    | View Snowbit SSPM Data | `SNOWBIT.SETUP:MANAGE` |

2. View CSPM logs in [Explore](../../../monitoring-and-insights/logs-screen/logs-in-explore-screen/index.md) or [Custom Dashboards](../../../custom-dashboards/getting-started/index.md), both supporting Scopes. Filter using `cspm` as application and/or subsystem name.

