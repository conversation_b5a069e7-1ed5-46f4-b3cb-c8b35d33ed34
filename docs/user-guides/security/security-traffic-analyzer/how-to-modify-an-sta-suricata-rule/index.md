---
title: "How to Modify an STA Suricata Rule"
date: "2020-10-14"
coverImage: "aws-traffic-mirroring.png"
---

Once you install our Coralogix Security Traffic Analyzer (STA) it will automatically pull a set of Suricata rules from the Internet and will continue to update them on a daily basis.

However, in the security business, there is no "one size fits all" solution. Some of these rules might suit your organization while others might need a few tweaks to get them to work correctly in your environment. In the new version of the Coralogix Security Traffic Analyzer (STA), we added the support for synchronizing its configuration against an S3 bucket. By using this feature, you can easily fine-tune your Suricata alerts as explained here.

## **Detect the problematic alert's SID**

By navigating to the NIDS dashboard, you should see a dashboard similar to this one:

![](images/Kazam_screenshot_00250-e1637598305304.png)  
  
If you'll scroll down a bit, you should be able to see the list of alerts by IP addresses, like the below example. The two columns marked on this screenshot are the group id (GID) and the Suricata id (SID) of the alert. We'll need them in the next step to disable the relevant alert and create a new one.

![Coralogix STA](images/Kazam_screenshot_00251-e1637598448914.png)  

## **Get the original suricata rule**

Connect to your STA instance via SSH by using the SSH key you provided during the installation of the STA and run the following command (no sudo permissions required):

``` conf
sta-lookup-rule '<the suricata sid of the alert>'
```

You should get an output similar to this:

``` conf
alert tcp $HOME_NET any -> $EXTERNAL_NET $HTTP_PORTS (msg:"ET POLICY curl User-Agent Outbound"; flow:established,to_server; content:"User-Agent|3a| curl/"; nocase; http_header; reference:url,www.useragentstring.com/pages/useragentstring.php; classtype:attempted-recon; sid:2013028; rev:2; metadata:created_at 2011_06_14, updated_at 2011_06_14;)
```

## **Disable the original Suricata rule**

If it does not already exist, add the following configuration to the file sta.conf on the relevant config files from the S3 bucket you configured for the STA to use as its configuration bucket. If it already exists, simply add the Suricata rule ID to the list of disabled_rules under rules_updater in the STA's config file:

``` conf
"rules_updater": {      "disabled_rules": [          "<the suricata sid of the alert>"       ] }
```

## **Create a modified version of the original rule (optional)**

If you only intend to modify an existing rule, copy the rule you found in step 2 above and paste it at the bottom of the local.rules file.  

**Upload the files back to the S3 bucket:** Use the following commands to upload the files to the config S3 bucket (if you only disabled a rule then the second command is not required)

``` bash
aws --region <your aws region> s3 cp /tmp/sta.conf s3://<s3 bucket used for STA configuration>/ aws --region <your aws region> s3 cp /tmp/local.rules s3://<s3 bucket used for STA configuration>/
```

After you've completed the steps above, your STA will automatically detect the changes and apply them after about 10 minutes.

## Best Practices

Here are some best practices to help you write effective Suricata Rules to materially improve your security posture:

**1. Target the vulnerability, not the exploit** – Avoid writing rules for detecting a specific exploit kit because there are countless exploits for the same vulnerability and we can be sure that new ones are being written as you’re reading this.

**2. Your peculiarity is your best asset, so use it** – Every organization has things that make it unique. Many of these can be quite useful when you try to catch malicious activity in your organization – both external and internal. By using this deep internal knowledge to your advantage, you’ll essentially convert the problem from a technological one to a pure old-school intelligence problem, forcing attackers to have a much more intimate understanding of your organization in order to be able to hide their tracks effectively. These things can be technological in nature or based on your organization’s particular working habits and internal guidelines.

Here are some examples:

- **Typical Working Hours**

- **Typical Browser**

- **IP Ranges based on Roles**

- **Unusual Connection Attempts**

- **Uncommon Ports**

**3. Honeytokens** – In a battlefield like the Internet where everyone can be just about anyone, deception works as well for defenders as it does for the attackers, if not better. Tricks like creating fake products, customers, users, and credit card records in the database and then matching Suricata rules for detecting them in the network traffic can be very effective.

For more information and best practices, read our [full guide for writing effective Suricata rules](https://coralogix.com/blog/writing-effective-suricata-rules-for-the-sta/).
