---
title: "AWS Resource Catalog Integration"
date: "2023-12-31"
description: "The AWS Resource Catalog integration enables Coralogix to retrieve metadata for all your AWS resources from your AWS account and present it within the Coralogix Resource Catalog."
noindex: true
search:
  exclude: true
---

# AWS Resource Catalog Integration

The AWS Resource Catalog integration enables Coralogix to retrieve metadata for all your AWS resources from your AWS account and present it within the Coralogix [Resource Catalog](../resource-catalog/index.md).

## Overview

Managing cloud resources and troubleshooting issues can be challenging, especially when they span multiple clouds and regions. Often, critical information, such as metadata, logs, metrics, and alerts, comes from various sources and can be difficult to access or analyze.

The Coralogix Resource Catalog seamlessly integrates with AWS to pull resource metadata and links it to Coralogix data for each resource (logs, metrics, traces, etc.), offering a unified, single-pane view.

### Poll intervals

Currently, the poll interval for AWS resources metadata is permanently set at 3 minutes. 

!!! note

    - The integration pulls metadata for all of the resource types (such as EC2 instances, EKS, Fargate, etc) that exist in your AWS account.
    - Multiple AWS accounts require the integration to be performed separately per account.

## Benefits

- **Improved troubleshooting**. With AWS resource metadata and related data all available within the Coralogix, there's no need to switch to AWS, enabling faster and more comprehensive root cause analysis.
- **Resource optimization and inventory management**. Centralizing all cloud resource information in one view simplifies inventory management and accelerates cloud resource optimization.

## Prerequisites

1. Create IAM policy using [CloudFormation template](https://github.com/coralogix/cloudformation-coralogix-aws/tree/master/coralogix-policies/coralogix-resource-catalog-role).
2. Run the following command in a terminal with installed AWS CLI and configured credentials or AWS CloudShell, replacing the necessary values.

    ``` ts
       aws cloudformation deploy --capabilities CAPABILITY_IAM  CAPABILITY_NAMED_IAM --template-file template.yaml --stack-name <the_name_of_the_stack_to_be_deployed_in_aws> --parameter-overrides RoleName=<role_name> CoralogixRegion=<coralogix_region>
    ```

3.  Get `ARN` of the role for the integration.

## AWS metadata integration deployment

1. From the Coralogix UI, go to **Data Flow** > **Integrations**.
2. In the **Integrations** section, select **AWS Resource Catalog**.
3. Click **Add New** to add a new AWS metadata integration. 
4. Enter configuration parameters according to your application requirements:
    - **Integration name** - A meaningful name assigned to the integration for easier identification of the integration instance.
    - **AWS Role ARN** - The role ARN created as described in the Prerequisites section above.
5. Click **Save** to create the integration.
6. Continue with the [Resource Catalog configuration](../resource-catalog/index.md).

![image.png](images/aws-rc-integration.png)