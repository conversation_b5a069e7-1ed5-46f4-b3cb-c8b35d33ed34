---
title: "Scheduled Reports"
date: "2024-08-15"
description: "You can now create scheduled reports that can be shared with relevant stakeholders. These scheduled reports can be generated as PDFs based on any dashboard you have created. These can be automatically sent to email recipients of your choice. Reports can be flexibly scheduled for daily, weekly, or monthly distribution. You can also use a cron job for even more granular scheduling cadences."
---

## Overview

You can now create scheduled reports that can be shared with relevant stakeholders. These scheduled reports can be generated as PDFs based on any dashboard you have created. These can be automatically sent to email recipients of your choice. Reports can be flexibly scheduled for daily, weekly, or monthly distribution. You can also use a cron job for even more granular scheduling cadences.

## PDF download

When you save your dashboard as a PDF, you create a snapshot of all widgets with the latest available data.

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            In the upper right-hand corner of your custom dashboard, click the three-dot menu (⋮) and select **Save as PDF.**
            
            The dashboard PDF is automatically downloaded to your local drive.
            
            ![](images/image-23.png)

## Schedule a Report

You can share a report by defining a scheduled task for saving the file and sending it to specified emails. Configure the report recurrence via the default builder or customize it using a cron scheduler.

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            In the upper right-hand corner, click the the three-dot menu (⋮) and select **Schedule Report.**
            
            ![](images/image-24.png)
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            In the **Settings** section of the **Schedule Report** dialog box, configure the following:

            - **Name**
            - **Recipients**. Enter destination email(s).
            
            ![](images/image-26.png)
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            In the **Event Recurrence** section, while in the **Builder** mode, configure the following:

            - **Frequency**. Define how frequently your report will be sent: daily, weekly, or monthly.
            - Depending on selected frequency, define time, day of the week, day of the month, time zone and other time-related parameters.
            
            ![](images/image-25.png)
            
            If the default **Builder** mode doesn’t provide required frequency options, use the **Cron Expression** mode for flexible scheduling alternatives. In the **Event Recurrence** section, select the **Cron Expression** mode, and create a cron job according to your scheduling requirements.
            
            ![](images/image-27.png)
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Click **Schedule Report** to finish.

## Limitations

- Maximum number of reports per day is 2.

- A single dashboard can be associated with one scheduling job. If other frequency or destinations are needed, we recommend duplicating the dashboard and defining a new destination list and frequency.

- This feature is only available for users that have the `team-dashboards:Update` permission.

- Reports are based on the attributes of user which initiated the report request (Report Initiator). At the time of the execution, the report is generated according to the Report Initiator’s permissions and data scope.
