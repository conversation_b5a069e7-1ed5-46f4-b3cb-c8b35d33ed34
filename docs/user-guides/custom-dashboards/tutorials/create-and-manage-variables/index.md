---
title: "Create &amp; Manage Variables"
date: "2024-05-15"
---

## Overview

A variable is like a placeholder for a value used in queries. Once set up in [Custom Dashboards](../../getting-started/index.md), your dashboard panel's queries will update to show data for that value.

Variables make dashboards more interactive and dynamic. Instead of putting specific names for servers, applications, or sensors in your queries, you can use variables instead. Once set up, you can easily switch between different data views. This means that if you have very complex, high-cardinality metrics, you can add variables to zoom in on specific values you care about, across widgets for all data types and regardless of the variable source type.

## Variable types

Choose from any of the following variable types:

| Variable type             | Description                                                                                                                                                                                                                                                                                                                                                                               |
|---------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **Static-value variable** | Manually define a fixed list of variable values, such as a number or a string of numbers separated by a comma. For example, if you have country names that never change, you might want to create them as a static-value variable rather than a data source variable.                                                                                                                     |
| **Query variable**        | Variable values are retrieved from a data source query using logs, metrics, or spans. For example, a query variable can generate a list of server names, sensor IDs, or data centers, dynamically updating the values by fetching options via a data source query. Query variables prove beneficial when managing multiple data source instances, especially across diverse environments. |

## Getting started

Navigate to **Dashboards** > **Custom Dashboards**.

In a new or existing dashboard, click the Manage Variable icon **{ }** in the upper left-hand corner of your dashboard. Then click **\+ Variable**.

## General options

Define the following fields for all variable types:

### Variable type

Select a [variable type](#variable-types).

### Variable name

Text entered in this field determines how the variable name will appear in your query, e.g, {{podvalues}}. No capitals or spaces are allowed.

### **Display** **name**

\[Optional\] Text entered in this field will appear in the top bar selection as part of the drop-down, e.g., "My Pods".

### Selection options

Once you have defined your variable, you may choose from these selection options:

- **Multi-value**: Allows multiple selection of values within a specific variable. Note that some queries do not support multiple values, such as {{variablename}}. Choose this option only if your query supports multiple values.

- **Include all**: You may enable this option when the m**ulti-value** option is enabled. The query will select all available and future values for a specific timeframe, regardless of your specific selection.

When both options are selected, all variable options are selected by default, and one or more may be deselected. Opting out of both options will allow you to select one variable value at a time.

## Create a static-value variable

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Select `static-values` as **variable type**.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Define the [general options](#general-options).
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Enter the variable values manually, separated by a comma. For example: `2024, 2025, 2026, 2027`.
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Click **SAVE**.

## Create a logs-based query variable

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Select `query` as **variable type**.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Define the [general options](#general-options).
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            For query options:
            
            - Select `logs` as your source.
            - Select `values` as your **variable type**.
            - Define the **field** by selecting a log key you wish to query. A list of current variable values presented in alpha-numerical order for the field will appear in the **Values Preview**.
            - **Sort values**. Select how to sort your data in the **Values Preview**.
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Click **SAVE**.

## Create a spans-based query variable

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Select `query` as **variable type**.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Define the [general options](#general-options).
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            For query options:
            
            - Select `spans` as your source.
            - Select `values` as your **variable type**.
            - Define the **field** by selecting a log key you wish to query. A list of current variable values presented in alpha-numerical order for the field will appear in the **Values Preview**.
            - **Sort values**. Select how to sort your data in the **Values Preview**.
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Click **SAVE**.

## Create a metrics-based query variable

### Metric names

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Select `query` as **variable type**.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Select [general options](#general-options).
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            For query options:
            
            - Select `metrics` as your **source**.
            - Select `metric names` as your variable **type**.
            - **Metrics regex**. Enter a regex expression to fetch the list of metrics you would like displayed. E.g., `^kube(.*)` will produce all metric names that begin with kube.
            - **Variable value**. Determines how the value will appear in your query. A list of current variable values for the field will appear in the **Values Preview**.
            - **Variable value display name**. Define how the values will be displayed in the drop-down selection in the top bar.
            - **Sort values**. Select how to sort your data in the **Values Preview**.
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Click **SAVE**.

### Metric labels

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Select `query` as **variable type**.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Select **variable** and **display name**.
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            For query options:
            
            - Select `metrics` as your **source**.
            - Select `metric labels` as your variable **type**. Choose the label for which you want the values displayed.
            - **Metrics regex**. Enter a regex expression to fetch the list of metric labels you would like displayed. E.g., `^kube(.*)` will produce all metric names that begin with kube.
            - **Variable value**. Determines how the value will appear in your query. A list of current variable values for the field will appear in the **Values Preview**.
            - **Variable value display name**. Define how the values will be displayed in the drop-down selection in the top bar.
            - **Sort values**. Select how to sort your data in the **Values Preview**.
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Click **SAVE**.

### Metric label values

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            Select `query` as **variable type**.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Define the [general options](#general-options).
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            For query options:
            
            - Select `metrics` as your **source**.
            - Select `label values` as your variable **type**.
            - **Metric name**. Choose the metric from which you want to pull labels.
            - **Metric label**. Choose the label for which you want the values displayed. Make the label dynamic by inserting a variable, e.g., Pod Name = {{pod}}.
            - **Label filters**. Create one or more nested variables and an additional filter layer for your query. (e.g., If you’ve chosen all of `cluster` the labels for a specific metric, you may choose to filter the labels by `cluster`. Select a value that is {{podvalue}} or the hardcoded name of your cluster.
            - **Variable value**. Determines how the value will appear in your query. A list of current variable values for the field will appear in the **Values Preview**.
            - **Variable value display name**. Define how the values will be displayed in the drop-down selection in the top bar.
            - **Sort values**. Select how to sort your data in the **Values Preview**.
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            Click **SAVE**.

## Manage variables

Easily view and manage the variables from the Custom Dashboards upper toolbar.

### Edit or delete

To edit or delete a variable, hover over the variable at the top of your dashboard and click the more actions menu <img src="./images/variable-more-actions.png" alt="More actions icon" style="height:1em; vertical-align:middle;">, then select **Edit** or **Delete** from the dropdown.

![alt text](./images/variable-more-actions-dropdown.png)

## Query with template variables

You can use template variables in Lucene, PromQL, or [DataPrime](../../../../dataprime/introduction/welcome-to-the-dataprime-reference/index.md) queries to build reusable dashboards. This eliminates the need to input the same data points into each widget, and instead allows you to filter all the widgets on your dashboard simultaneously with one variable.

To edit the query, select the widget and navigate to the query builder at the bottom of the screen.

### Lucene

In a Lucene query, a template variable is referenced with `{{variable_name}}` syntax.

**Example:**

```Lucene
 product_id:{{pid}} 
```

In this example, the `pid` is the variable name, which must match the name you defined in the dashboard.

### PromQL

In a PromQL query, a template variable is referenced with `${variable_name}` syntax.

**Example:**

```PromQL
sum(kube_pod_owner{pod=~${pod}})
```

In this example, `{pod=~${pod}}` is a label selector that filters the `kube_pod_owner` metric for the specific pod whose name matches the name selected in the `${pod}` template variable. The `${pod}` is dynamically replaced with the selected pod when the query runs.

#### **Predefined variables**

You may use predefined variables in your PromQL query.

| Variable        | Description                                                                                                                                                                                                                           | Example     |
|-----------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------|
| `${__range}`    | This variable represents the duration of the dashboard time range. It is rendered as an interval string supported by PromQL. e.g., If one selects a time range from 13.00 to 14.30, then ${__range} variable will be rendered as 90m. | 1d, 5m      |
| `${__range_s}`  | Rendered as the number of seconds in the selected time frame                                                                                                                                                                          | 60s, 180s   |
| `${__range_ms}` | Rendered as the number of milliseconds in the selected time frame                                                                                                                                                                     | 60ms, 180ms |

**Example:** 

``` PromQL
sum_over_time(kube_pod_owner[${__range}])
```

In this example, the `${__range_s}` returns the time range in seconds as a number (e.g., 60, 1800). This is useful when using PromQL function to calculate a numeric value over time.

### DataPrime

In a DataPrime query, a template variable is accessed with `$p.<variable_name>` syntax.

**Example:**

```DataPrime
source logs 
| filter $l.applicationname.inArray($p.application) 
| groupby $l.applicationname as Application
, $m.timestamp / 10m as timestamp agg count() as count
```

In this example, the DataPrime query retrieves log activity grouped in 10-minute intervals and filtered by the selected `application` dashboard variable, using a parameter that dynamically updates based on the variable you select. This allows you to compare log volume across one or more applications.

Learn more about [query parameters](../../../../dataprime/beginners-guide/parameters/index.md).

## Additional resources

<!--- Without header -->
::: additional-content items="id: fQL6A0vvQUg&t=1s, title: Custom Dashboard Variables"
