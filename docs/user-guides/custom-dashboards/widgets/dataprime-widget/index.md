---
title: "DataPrime Widget"
date: "2024-01-10"
description: "Coralogix's innovative DataPrime language empowers you to query your data and transform it through various operations tailored to your specific needs, such as calculation, extraction, and aggregation."
---

Leverage the capabilities of DataPrime queries in Custom Dashboards using the **DataPrime Widget**.

## Overview

Coralogix's innovative [DataPrime](../../../../dataprime/introduction/welcome-to-the-dataprime-reference/index.md) language empowers you to query your data and transform it through various operations tailored to your specific needs, such as calculation, extraction, and aggregation.

In [Custom Dashboards](../../getting-started/index.md), the DataPrime Widget enables you to harness the capabilities of DataPrime queries. As your query evolves, this feature automatically generates optimal visualizations to display your data and suggests modifying your query for additional visualizations.

## Create a DataPrime Widget

::: list-wrapper
    ::: custom-list-item marker=1
        ::: list-item-header

        ::: list-item-content
            In a [custom dashboard](../../getting-started/index.md), drag and drop the **DataPrime Widget** from your left-hand sidebar to get started.
            
            A data table will appear in the top panel of the widget, while the DataPrime query will appear in the bottom panel.
    ::: custom-list-item marker=2
        ::: list-item-header

        ::: list-item-content
            Construct your [DataPrime query](../../../../dataprime/introduction/welcome-to-the-dataprime-reference/index.md) with the [Query Builder](../../tutorials/query-builder/index.md). As your query evolves, select from a list of visualizations supporting your query results - [horizontal](../horizontal-bar-charts/index.md) or [vertical bar chart](../vertical-bar-charts/index.md), [line chart](../line-charts/index.md), [gauge](../gauges/basic-gauges/index.md), or [pie chart](../pie-charts/index.md) - in the right-hand column. Hover over faded visualizations to receive suggestions on modifying your query, enabling you to achieve the desired visualization.
    ::: custom-list-item marker=3
        ::: list-item-header

        ::: list-item-content
            Configure the widget settings in the right-hand panel.

            ![](../dataprime-widget/images/dataprime-widget.png)
    ::: custom-list-item marker=4
        ::: list-item-header

        ::: list-item-content
            To save your dashboard with the new widget, click **SAVE** in the upper right-hand corner.

            At any stage, view the raw data of the query by clicking **RAW DATA** on the toolbar of the query pane.
