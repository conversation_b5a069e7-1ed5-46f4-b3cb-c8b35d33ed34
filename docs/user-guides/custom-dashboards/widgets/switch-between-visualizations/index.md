---
title: "Switching Between Visualizations in Custom Dashboards"
date: "2023-06-15"
description: "Effortlessly toggle between different visualizations in **Custom Dashboards** without the need to recreate widgets from scratch."
---
# Switching Between Visualizations in Custom Dashboards

Effortlessly toggle between different visualizations in **Custom Dashboards** without the need to recreate widgets from scratch. This allows you to view your data from various perspectives while maintaining your original query intact.

## How to switch visualizations

### Open widget options

Click the ellipsis (**...**) on the right side of any widget in **Custom Dashboards**.

### Enter edit mode

Select **Edit**. The **Switch Visualization** option will appear in the widget's top bar.

### Choose a new visualization

Pick an alternate visualization for your query. 

![](../switch-between-visualizations/images/select-a-widget.png)

Once selected, the widget updates to the new visualization type, retaining your original query in the [Query Builder](../../tutorials/query-builder/index.md). Parameters for the new widget will appear in the right-hand modal.

## Use case: CPU usage analysis

### Scenario

A user monitors the total CPU usage rate as a percentage for all containers running on specific Kubernetes nodes, using a [Gauge](../gauges/basic-gauges/index.md). Upon receiving an alert that CPU usage has exceeded a critical threshold, the user wants to analyze the trajectory leading up to the breach.

![](../switch-between-visualizations/images/gauge-CPU.png)

### Solution

The user edits the widget and selects a [Line Chart](../line-charts/index.md) instead. By expanding the widget's time window, they can view the long-term trajectory and pinpoint when the threshold was breached.

![](../switch-between-visualizations/images/line-chart.png)

Switching to a time-series visualization provides deeper insights, replacing a snapshot with a detailed timeline.

## Support

**Need help?** 

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up. 

Feel free to reach out to us **via our in-app chat** or by sending us an email to [<EMAIL>](mailto:<EMAIL>).