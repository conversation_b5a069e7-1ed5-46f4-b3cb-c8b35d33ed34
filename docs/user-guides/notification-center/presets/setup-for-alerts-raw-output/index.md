---
title: "Alerts Use-Case: Raw Output Type"
date: "2024-08-07"
description: "This tutorial describes how to set up and manage presets for Notification Center."
---

# Alerts Use-Case: Raw Output Type

The Slack destination type requires you to select between [structured](../../connectors/schema-structures/slack/index.md#structured-output) and [raw](../../connectors/schema-structures/slack/index.md#raw-output) output type. 

For maximum customization, you can opt for raw output by selecting the **Raw** tab. In this mode, the message schema is configured directly using a JSON payload. The templates in the provided payload are rendered, and the result is sent as-is to the Slack API.

![](../setup-for-alerts-raw-output/raw-oupt.png)

To create a custom raw Slack message, paste the required fields from your Slack app’s [Block Kit](https://api.slack.com/block-kit/building) or existing notification templates into a clean JSON file.