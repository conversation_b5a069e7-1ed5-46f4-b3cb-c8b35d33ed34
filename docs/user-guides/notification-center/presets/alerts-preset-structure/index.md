---
title: "Alerts Preset Structure"
date: "2024-08-07"
description: "This tutorial describes the preset structure for alerts as the notification source type."
---

# Alerts Preset Structure

This tutorial describes the preset structure for alerts as the notification source type.

## System presets

For each destination type, Coralogix provides one or more predefined system presets, offering structured, out-of-the-box message configurations. These presets vary in schema structure and content depending on the notification source and destination type.

For alerts, system presets include one or more following: Basic, Detailed, and/or Empty.

### Basic

The Basic system preset is the default option, providing a concise, informative schema.

### Detailed

The Detailed system preset includes comprehensive alert descriptions with multiple variables from the alert schema.

### Empty

The Empty system preset contains schema fields without predefined content. Users must manually configure the desired schema fields for their notifications.

# Preset configuration layers

## Triggered & resolved presets

Each preset has distinct configurations for triggered and resolved notifications. While both structures are similar, the templates differ slightly. Users may view and modify the respective configurations as necessary.

## Final notification output

When an alert is triggered, the final notification is sent based on the preset configuration, with more specific, customized configurations taking precedence over more general, system configurations.