---
title: "Welcome to Notification Center"
date: "2024-08-07"
description: "Notification Center is your home base for all outbound alert notifications sent from Coralogix to a third-party destination such as Slack or PagerDuty."
---

# Welcome to Notification Center

## Overview

**Notification Center** is your centralized hub for managing outbound notifications sent from Coralogix to third-party vendors like Slack or PagerDuty. It consolidates notifications across the Coralogix platform into a single interface, giving you full control over setup, management, and auditing.

Notification Center allows you to send notifications to one or more connectors, whether within the same destination type (e.g., `slack-workspace-1` and `slack-workspace-2`) or across destination types (e.g., Slack and PagerDuty). 

When a notification source, such as an alert, triggers a notification, the system sends a message to your chosen connector based on connector and preset configurations. Connectors link notifications to specific destinations, while presets define the content of the messages based on the notification source.

### Centralize outbound integration management

Create notifications separately per notification source and consolidate them across the Coralogix platform in one UI. Store notifications for auditing and troubleshooting purposes.

### Create rich notifications to send to destination types

Users can format notifications to include specific information for a notification source (e.g., alerts) to send to third-party vendors known as destination types, such as Slack and PagerDuty. 

### Format notifications using schema-driven templates

As a basis for notifications, Coralogix provides fully typed schemas per notification source, such as alerts. Fully typed schemas let users extract data from an event using a variable path in the templates, so this data is included in the notifications. Users may either use the preconfigured notification templates known as presets as-is or customize them. 

### Enjoy data-driven routing

Route notifications to different teams within the same destination type or across different destination types by defining conditions in the connector. For example, you can configure different metric alerts to be sent to specific R&D teams based on the triggered application.

## Documentation

This guide will walk you through Notification Center.  Here's what you'll find:

**Introduction**

- [Welcome to Notification Center](../introduction/index.md)

- [Terminology](../introduction/terminology/index.md)

- [Connectors Explained](../introduction/connectors-explained/index.md)

- [Presets Explained](../introduction/presets-explained/index.md)

- [User Flow](../introduction/user-flow/index.md)

**Destination Types**

- [Introduction](../destination-types/introduction/index.md)

- **HTTPS**
    - [Connector Configuration](../destination-types/https/connector-config/index.md)
    - [Message Configuration](../destination-types/https/schema-structure/index.md)

- **PagerDuty**
    - [Connector Configuration](../destination-types/pagerduty/connector-config/index.md)
    - [Message Configuration](../destination-types/pagerduty/schema-structure/index.md)

- **Slack**
    - [Connector Configuration](../destination-types/slack/connector-config/index.md)
    - [Message Configuration](../destination-types/slack/schema-structure/index.md)

**Connectors**

- [Introduction](../connectors/introduction/index.md)

- [Connector Setup](../connectors/setup/index.md)

- [Managing Existing Connectors](../connectors/management/index.md)

**Presets**

- [Introduction](../presets/introduction/index.md)

- [Preset Structure for Alerts](../presets/alerts-preset-structure/index.md)

- **Custom Preset Setup**

    - [Alerts Use-Case: Structured Ouput Type](../presets/setup-for-alerts/index.md)

    - [Alerts Use-Case: Raw Output Type](../presets/setup-for-alerts-raw-output/index.md)

- [Managing Existing Presets](../presets/management/index.md)

**Routing**

- [Setup](../routing/index.md)

**Dynamic Templating**

- [Dynamic Templating for Connectors, Presets, & Routing](../dynamic-templating/index.md)

**Migration**

- [Migrating from Outbound Webhooks](../migration/index.md)

**Permissions**

- [Permissions](../permissions/index.md)