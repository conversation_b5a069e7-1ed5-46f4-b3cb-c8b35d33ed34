---
title: "PagerDuty"
date: "2024-08-07"
description: "Notification Center supports the PagerDuty destination type."
---

# PagerDuty

This guide outlines the PagerDuty destination type.

## Connector configuration
A connector is a specific integration instance within a destination type that links Coralogix to a third-party vendor. It includes a set of parameters, known as the connector configuration, that define how it connects to its destination type. This configuration varies by destination type. The connector configuration for PagerDuty can be found [here](../pagerduty/connector-config/index.md).

## Message configuration
Each destination type has a notification schema structure per output type. Schemas define the structure of the message sent to the destination type. Presets expose this message configuration in a reusable format. The message configuration for PagerDuty can be found [here](../pagerduty/schema-structure/index.md).