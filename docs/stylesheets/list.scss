.list-wrapper {
  margin-top: 18px;

  .list-item {
    position: relative;
    display: flex;
    margin-left: 16px;
    padding: 0 24px 8px 24px;
    border-left: 1px solid var(--blue-grey-color);
    color: var(--list-group-color);

    .list-item-marker {
      position: absolute;
      top: -1px;
      left: -16px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      min-width: 32px;
      height: 32px;
      border: 1px solid var(--blue-grey-color);
      border-radius: 50%;
      background-color: var(--list-marker-bg-color);
      font-size: 16px;
      color: var(--blue-grey-color);
      content: counter(list-item);
    }

    .list-item-inner {
      width: 100%;

      .list-item-header {
        display: flex;
        align-items: center;
        min-height: 32px;
        font-size: 16px;
        line-height: 24px;
        font-weight: 600;
        color: var(--list-item-header-color);

        &:empty {
          min-height: 8px;
          margin-bottom: -16px;
        }

        & > p {
          margin: 0;
        }
      }

      .list-item-content {
        margin-top: 8px;
        font-size: 14px;
        line-height: 19.6px;
      }
    }
  }
}
