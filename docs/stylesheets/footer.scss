@use 'utils/mixins';

.md-footer {
  //display: flex;
  display: none;
  justify-content: center;
  padding: 60px 32px;
  background-color: var(--grey-600-color);

  @include mixins.break-to-device(tablet portrait) {
    padding: 42px 24px 30px;
  }

  .footer-inner {
    width: 100%;
    max-width: 1552px;

    @include mixins.break-to-device(screen small) {
      max-width: 1288px;
    }

    .footer-top {
      display: grid;
      grid-template-columns: 148px 1fr;
      grid-gap: 242px;
      justify-content: space-between;

      @include mixins.break-to-device(tablet landscape) {
        display: flex;
        flex-direction: column;
        gap: 48px;
      }

      @include mixins.break-to-device(tablet portrait) {
        gap: 30px;
      }

      .footer-nav {
        display: flex;
        justify-content: space-between;
        gap: 60px;
        padding: 6px 0;

        @include mixins.break-to-device(tablet landscape) {
          gap: 34px;
        }

        @include mixins.break-to-device(tablet portrait) {
          flex-direction: column;
          padding: 0;
        }

        .footer-nav-section {
          font-size: 15px;
          font-weight: 500;
          line-height: 20.4px;
          letter-spacing: 0.02em;
          color: var(--white-color);

          @include mixins.break-to-device(mobile landscape) {
            font-size: 14px;
          }

          .section-items-list {
            margin-top: 8px;

            .section-item a {
              font-weight: 400;
              line-height: 36px;
              letter-spacing: -0.01em;
              color: var(--grey-300-color);

              &:hover {
                color: var(--white-color);
              }
            }
          }
        }
      }
    }

    .footer-bottom {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin: 80px auto 0;
      padding: 80px 0 20px;
      border-top: 1px solid var(--grey-300-color);
      font-size: 14px;
      font-weight: 400;
      line-height: 19.6px;
      color: var(--text-primary-dark-color);

      @include mixins.break-to-device(tablet portrait) {
        flex-direction: column-reverse;
        align-items: flex-start;
        gap: 24px;
        margin-top: 24px;
        padding: 24px 0 0;
      }

      .footer-copyright {
        display: flex;
        flex-direction: row;
        align-items: center;
        flex-wrap: wrap;
        gap: 20px;

        @include mixins.break-to-device(tablet portrait) {
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;
        }

        a:hover {
          color: var(--white-color);
        }
      }

      .copyright-nav-items-list {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 20px;

        @include mixins.break-to-device(tablet portrait) {
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;
        }
      }

      .footer-social {
        display: flex;
        gap: 48px;

        a {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        @include mixins.break-to-device(screen small-material) {
          gap: 36px;
        }

        @include mixins.break-to-device(tablet portrait) {
          width: 100%;
          border-bottom: 1px solid var(--grey-300-color);
          padding-bottom: 24px;
        }
      }
    }
  }
}

.footer-old {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 78px;
  padding: 0 16px;
  background-color: var(--main-green-primary-color);
  z-index: 9;

  @include mixins.break-to-device(tablet portrait) {
    flex-direction: column;
    align-items: start;
    justify-content: unset;
    gap: 16px;
    height: 115px;
    padding-top: 24px;
    padding-bottom: 24px;
  }

  .footer-logo {
    display: flex;
    align-items: center;
    gap: 8px;
    padding-top: 2px;
    font-size: 20px;
    line-height: 31px;
    font-weight: 600;
    color: var(--white-color);

    img {
      width: 131px;
      height: 31px;
      margin-bottom: 2px;
    }
  }

  .footer-copyrights {
    display: flex;
    gap: 32px;
    font-size: 14px;
    line-height: 20px;
    font-weight: 600;

    @include mixins.break-to-device(tablet portrait) {
      justify-content: space-between;
      width: 100%;
    }

    @include mixins.break-to-device(mobile portrait) {
      gap: 16px;
    }
  }
}
