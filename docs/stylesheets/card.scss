@use "utils/mixins";

@mixin cards-media-query() {
  @media screen and (max-width: 580px) {
    @content;
  }
}

.cards-wrapper {
  width: 100%;
  margin-top: 32px;

  .cards-section {
    margin-top: 20px;

    &:not(:first-of-type) {
      margin-top: 70px;
    }

    .cards-section-title {
      font-size: 32px;
      line-height: 44px;
      font-weight: 600;
      text-align: center;
      color: var(--typeset-section-header-color);

      @include cards-media-query {
        & {
          font-size: 22px;
          line-height: 32px;
        }
      }
    }

    .cards-list {
      display: grid;
      grid-template-columns: repeat(4, auto);
      gap: 24px;
      justify-content: flex-start;
      margin-top: 32px;

      @include mixins.break-to-device(tablet landscape) {
        & {
          grid-template-columns: repeat(auto-fill, 242px);
          justify-content: center;
        }
      }

      @include cards-media-query {
        & {
          grid-template-columns: repeat(auto-fill, 100%);
        }
      }

      .card {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 242px;
        height: 272px;
        border: 1px solid var(--card-border-color);
        border-radius: 8px;
        box-shadow: 0 3px 14px 0 var(--box-shadow-color);
        font-size: 18px;
        line-height: 26px;
        font-weight: 400;
        text-decoration: none;

        @include mixins.break-from-device(screen large) {
          & {
            width: 302px;
          }
        }

        @include cards-media-query {
          & {
            width: 100%;
          }
        }

        &:hover {
          border-color: var(--main-green-primary-color);
          box-shadow: 4px 5px 16px 0 var(--box-shadow-hover-color);
        }

        .card-header {
          display: flex;
          align-items: center;
          height: 96px;
          padding: 0 16px;
          color: var(--card-header-text-color);
          font-weight: 800;
          background-color: var(--card-header-bg-color);
          border-top-left-radius: 8px;
          border-top-right-radius: 8px;

          .card-header-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            min-width: 32px;
            min-height: 32px;
            margin-right: 16px;
            background-color: var(--card-icon-bg-color);
            border-radius: 4px;
          }

          .card-header-title {
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
        }

        .card-description {
          overflow: hidden;
          display: -webkit-box;
          margin-top: 24px;
          padding: 0 16px;
          color: var(--card-text-color);
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
        }

        .card-footer {
          padding: 0 16px 16px;
          font-weight: 700;
          text-align: right;

          &:hover {
            img {
              transform: translateX(4px);
              transition: transform 300ms;
            }
          }

          img {
            margin-left: 4px;
          }
        }
      }
    }
  }
}

[data-md-color-scheme='dark'] {
  .cards-wrapper .cards-section .cards-list .card {
    box-shadow: 0 0 20px 0 var(--box-shadow-light-color-o-10);

    &:hover {
      box-shadow: 0 0 30px 0 var(--box-shadow-light-hover-color-o-25);
    }
  }
}
