@use "utils/mixins";

.highlight {
  @include mixins.break-to-device(tablet landscape) {
    & {
      margin-left: 0;
      margin-right: 0;
    }
  }

  code.md-code__content:hover {
    scrollbar-color: inherit;
  }

  .linenos {
    width: 58px;
    padding: 19px 0;
    background-color: var(--code-block-lines-bg-color);
    font-size: 14px;

    .linenodiv {
      padding: 0;
      display: flex;
      justify-content: center;
      box-shadow: none;

      pre {
        line-height: 12.5px;
        color: var(--code-block-lines-color);

        .normal {
          display: block;

          a {
            text-decoration: none;
          }
        }
      }
    }
  }

  pre {
    .md-clipboard:after {
      width: 20px;
      height: 20px;
      background: transparent url('images/icons/copy-dark.svg');
      mask-image: none;
    }

    code {
      padding: 13px 16px;
      background-color: var(--code-block-bg-color);
      font-size: 14px;
      line-height: 25px;
    }
  }
}

//[data-md-color-scheme='dark'] {
//  .highlighttable .code .md-clipboard:after {
//    background: transparent url('images/icons/copy-dark.svg');
//  }
//}
