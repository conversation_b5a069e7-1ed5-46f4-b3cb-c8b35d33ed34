@use 'utils/mixins';

.md-header {
  position: fixed;
  display: flex;
  align-items: center;
  height: 64px;
  padding: 0 32px;
  background-color: var(--main-header-bg-color);
  border-bottom: 1px solid var(--main-header-border-color);
  color: var(--header-logo-text-color);
  box-shadow: none;

  @include mixins.break-to-device(tablet landscape) {
    color: var(--text-default-link-color);
    z-index: 999;
  }

  .md-header__inner {
    justify-content: space-between;
    width: 100%;
    height: 100%;
    padding: 0;

    .md-header__button {
      z-index: 0;

      &:hover {
        opacity: 1;
      }
    }

    .header-left {
      display: flex;
      align-items: center;
      height: 100%;

      .md-logo {
        display: block;
        width: 208px;
        height: 28px;
        margin: 0;
        padding: 0;
        background: url('images/main-logo.svg') no-repeat;
      }

      .md-header__title {
        height: auto;
        margin-left: 8px;
        margin-right: 0;
        padding-top: 3px;
        font-size: 20px;
        line-height: 50px;
        font-weight: 600;
      }

      .header-nav {
        height: 100%;
        margin-left: 32px;
        color: var(--main-header-nav-text-color);
        font-size: 14px;
        line-height: 19px;
        font-weight: 400;

        @include mixins.break-to-device(tablet landscape-material) {
          display: none;
        }

        .header-nav-list {
          height: 100%;
          display: flex;
          gap: 12px;

          .header-nav-item {
            display: flex;
            align-items: center;
            height: 100%;
            gap: 8px;
            padding: 0 8px;

            .header-nav-item-icon {
              width: 24px;
              height: 24px;
              filter: invert(71%) sepia(4%) saturate(635%) hue-rotate(171deg)
                brightness(107%) contrast(92%);
            }

            &.active {
              border-bottom: 1px solid var(--active-item-color);
              color: var(--active-item-color);

              .header-nav-item-icon {
                filter: invert(66%) sepia(38%) saturate(3866%)
                  hue-rotate(125deg) brightness(93%) contrast(96%);
              }
            }
          }
        }
      }
    }

    .header-right {
      display: flex;
      align-items: center;

      @include mixins.break-to-device(tablet portrait) {
        .md-search {
          border: none !important;
        }
      }

      .md-typeset {
        display: flex;
        align-items: center;

        .header-button {
          margin-left: 8px;

          @include mixins.break-to-device(tablet landscape) {
            display: none;
          }
        }
      }

      .md-header__option {
        @include mixins.break-to-device(tablet portrait) {
          display: none;
        }

        .site-mode-toggle {
          margin: 0 0 0 12px;
          padding: 11px 0;

          img {
            width: 24px;
            height: 24px;
          }
        }
      }

      .header-search-button {
        width: 28px;
        height: 28px;
        margin: 5px;
        border-radius: 2px;
        background: url('images/icons/search.svg') no-repeat center center;

        svg {
          display: none;
        }
      }

      .header-menu-button {
        width: 28px;
        height: 28px;
        margin: 5px 0 5px 5px;
        background: url('images/icons/hamburger.svg') no-repeat center center;

        @include mixins.break-from-device(screen small-material) {
          display: none;
        }

        svg {
          display: none;
        }
      }
    }
  }
}

.static {
  .md-header {
    background-color: var(--md-primary-fg-color);
    border-bottom: 1px solid var(--main-border-color);

    .md-header__inner {
      max-width: 1552px;

      @include mixins.break-to-device(screen small) {
        max-width: 1288px;
      }
    }
  }

  .md-main .md-main__inner .md-content .md-content__inner {
    margin: 0 !important;
    padding: 0;
  }

  .md-footer {
    display: flex;
  }

  .footer-old {
    display: none;
  }
}

[data-md-color-scheme='dark'] {
  .md-header {
    .md-logo {
      background: url('images/main-logo-white.svg') no-repeat !important;

      @include mixins.break-to-device(tablet landscape) {
        background-image: url('images/main-logo.svg') !important;
      }
    }

    .header-nav-item {
      display: flex;
      align-items: center;
      gap: 8px;

      &.active {
        .header-nav-item-icon {
          filter: invert(80%) sepia(27%) saturate(640%) hue-rotate(106deg)
            brightness(100%) contrast(101%) !important;
        }
      }
    }
  }
}
