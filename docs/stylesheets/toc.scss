@use "utils/mixins";

.md-sidebar[data-md-type='toc'] {
  top: 120px !important;
  right: 20px;
  height: 100%;
  width: unset;
  padding: 0 0 16px 0;

  @include mixins.break-to-device(tablet landscape-material) {
    display: none;
  }

  .md-sidebar__scrollwrap {
    height: calc(100vh - 150px) !important;
    margin: 0;

    .dropdown {
      margin-bottom: 32px;
    }

    .md-sidebar__inner {
      width: 226px;
      padding: 16px;
      background-color: transparent;
      border: 1px solid var(--main-border-color);
      border-radius: 2px;

      @include mixins.break-from-device(screen small) {
        width: 246px;
      }

      @include mixins.break-from-device(screen large) {
        width: 300px;
      }

      & > .md-nav {
        position: relative;

        .md-nav-inner {
          margin-bottom: 32px;
          padding-bottom: 28px;
          border-bottom: 1px solid var(--main-border-color);

          & > .md-nav__list .md-nav__item .md-nav {
            margin-left: 16px;
          }
        }
      }

      .toc-main {
        margin: 0;
      }

      .md-nav__title {
        position: static;
        margin-bottom: 16px;
        padding: 6px 12px;
        border: 1px solid var(--main-border-color);
        border-radius: 2px;
        background: none;
        box-shadow: none;
        font-size: 16px;
        line-height: 24px;
        font-weight: 600;
      }

      .md-nav {
        font-size: 14px;

        .md-nav__list {
          padding: 0;

          .md-nav__link {
            margin: 0;
            padding-left: 0;
          }

          .md-nav__link[for]:focus,
          .md-nav__link[for]:hover,
          .md-nav__link[href]:focus,
          .md-nav__link[href]:hover {
            &.md-nav__link--active,
            &.md-nav__link--active code {
              color: var(--active-item-color);
            }
          }

          .md-nav__link--active,
          .md-nav__link--active code {
            color: var(--active-item-color);
          }
        }
      }
    }
  }
}

.toc-mobile {
  position: fixed;
  display: none;
  top: 54px;
  left: 0;
  width: 100%;
  padding: 24px 16px 0 16px;
  background-color: var(--main-bg-color);
  margin-bottom: 24px;
  z-index: 9;

  @include mixins.break-to-device(tablet landscape-material) {
    display: block;
  }

  & > .common-overlay {
    z-index: 0;
  }

  & > .dropdown-inner {
    z-index: 1;

    & > .dropdown-menu-wrapper {
      & > .dropdown-menu {
        width: 100%;
        max-height: 425px;
        border: 1px solid var(--active-item-color);
        background-color: var(--main-bg-color);

        .general-options {
          padding: 16px 16px 24px 16px;

          .domain-selector {
            margin-top: 24px;
          }
        }

        .md-nav__list {
          list-style: none;
          margin: 0;
        }

        .dropdown-item {
          padding: 16px;
          color: inherit;
          text-decoration: none;

          &:not(:first-of-type) {
            border-top: 1px solid var(--main-border-color);
          }

          & > .md-nav {
            margin: -16px;
          }
        }

        & > li:nth-of-type(2) > a {
          border: none;
        }
      }
    }
  }
}
