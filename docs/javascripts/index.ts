import Intercom from '@intercom/messenger-js-sdk';
import { AnalyticsBrowser } from '@segment/analytics-next';
import { INTERCOM_APP_ID, SEGMENT_IO_KEY } from 'env';

import { initGlobalCategory } from '@app/global-category';
import { initHeaderAnchor } from '@app/header-anchor';
import { initDropdown } from '@app/dropdown';
import { initDomainSelector } from '@app/domain-selector';
import { initAccordion } from '@app/accordion';
import { initTooltip } from '@app/tooltip';
import { initSearch } from '@app/search';
import { initToc } from '@app/toc';
import { initFeedbackForm } from '@app/feedback';
import { initThemeSwitcherMobile } from '@app/theme-switcher-mobile';
import { initNav } from '@app/nav';
import { initButton } from '@app/button';
import { initJsonSchema } from '@app/json-schema';
import { getPageTrackingData } from '@app/utils';
import { TRACKING_EVENTS } from '@app/constants';

import '@styles/main.scss';

export const analytics = AnalyticsBrowser.load({
  writeKey: SEGMENT_IO_KEY,
});

document$.subscribe(() => {
  const homePageEl = document.querySelector('.home-page') as HTMLDivElement;
  const mainInnerEl = document.querySelector(
    '.md-main__inner'
  ) as HTMLDivElement;

  if (homePageEl) {
    mainInnerEl.classList.add('static');
    document.body.classList.add('static');
  }

  const page404El = document.querySelector('.page-404') as HTMLDivElement;

  if (page404El) {
    document.body.classList.add('page-404-wrapper');
  }

  initHeaderAnchor();
  initNav();
  initToc();
  initDomainSelector();
  initDropdown();
  initAccordion();
  initSearch();
  initTooltip();
  initFeedbackForm();
  initThemeSwitcherMobile();
  initButton();
  initJsonSchema();
  initGlobalCategory();

  Intercom({
    app_id: INTERCOM_APP_ID,
  });

  analytics.track(TRACKING_EVENTS.pageView, getPageTrackingData());
});
