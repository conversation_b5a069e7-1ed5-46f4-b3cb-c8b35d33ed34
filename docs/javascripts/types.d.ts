import { Observable, Subject } from 'rx';

type KeyboardMode = 'global' | 'search';

interface Keyboard {
  mode: KeyboardMode;
  type: string;
  claim: () => void;
}

interface ViewportOffset {
  x: number;
  y: number;
}

interface ViewportSize {
  width: number;
  height: number;
}

interface Viewport {
  offset: ViewportOffset;
  size: ViewportSize;
}

type Component<
  T extends object = object,
  U extends HTMLElement = HTMLElement,
> = T & {
  ref: U;
};

declare global {
  const document$: Observable<Document>; /* Document observable */
  const location$: Subject<URL>; /* Location subject */
  const target$: Observable<HTMLElement>; /* Location target observable */
  const keyboard$: Observable<Keyboard>; /* Keyboard observable */
  const viewport$: Observable<Viewport>; /* Viewport observable */
  const tablet$: Observable<boolean>; /* Media tablet observable */
  const screen$: Observable<boolean>; /* Media screen observable */
  const print$: Observable<boolean>; /* Media print observable */
  const alert$: Subject<string>; /* Alert subject */
  const progress$: Subject<number>; /* Progress indicator subject */
  const component$: Observable<Component>; /* Component observable */
}
