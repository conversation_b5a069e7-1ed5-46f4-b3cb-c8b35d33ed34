import AnchorJS from 'anchor-js';

export const initHeaderAnchor = () => {
  const anchors = new AnchorJS();

  anchors.options = {
    placement: 'left',
    visible: 'hover',
    icon: '',
    titleText: 'Copy to Clipboard',
  };
  anchors.add(
    '.md-content__inner h1, .md-content__inner h2, .md-content__inner h3'
  );

  setTimeout(() => {
    const anchorEls: NodeListOf<HTMLLinkElement> =
      document.querySelectorAll('.anchorjs-link ');

    anchorEls.forEach((anchorEl) => {
      anchorEl.addEventListener('click', (e) => {
        e.preventDefault();

        navigator.clipboard.writeText(anchorEl.href);

        const topOffset =
          anchorEl.getBoundingClientRect().top + window.scrollY - 80;

        window.scrollTo({ top: topOffset, behavior: 'smooth' });
        window.history.pushState({}, '', anchorEl.href);
      });
    });
  }, 2000);
};
