---
title: query_service v1
language_tabs: []
toc_footers: []
includes: []
highlight_theme: darkula
headingLevel: 2

---

<!-- Generator: Widdershins v4.0.1 -->

<h1 id="query_service">query_service v1</h1>

> Scroll down for example requests and responses.

<h1 id="query_service-default">Default</h1>

## com.coralogixapis.dataprime.v1.DataprimeQueryService.Query

<a id="opIdcom.coralogixapis.dataprime.v1.DataprimeQueryService.Query"></a>

> Code samples

`POST /api/v1/dataprime/query`

method to run dataprime text queries

> Body parameter

```json
{
  "query": "string",
  "metadata": {
    "tier": "TIER_UNSPECIFIED",
    "syntax": "QUERY_SYNTAX_UNSPECIFIED",
    "limit": 0,
    "startDate": "2019-08-24T14:15:22Z",
    "endDate": "2019-08-24T14:15:22Z",
    "defaultSource": "string",
    "strictFieldsValidation": true,
    "nowDate": "2019-08-24T14:15:22Z"
  }
}
```

<h3 id="com.coralogixapis.dataprime.v1.dataprimequeryservice.query-parameters">Parameters</h3>

|Name|In|Type|Required|Description|
|---|---|---|---|---|
|body|body|[com.coralogixapis.dataprime.v1.QueryRequest](#schemacom.coralogixapis.dataprime.v1.queryrequest)|true|dataprime text query request|

> Example responses

> 200 Response

<h3 id="com.coralogixapis.dataprime.v1.dataprimequeryservice.query-responses">Responses</h3>

|Status|Meaning|Description|Schema|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[com.coralogixapis.dataprime.v1.QueryResponse](#schemacom.coralogixapis.dataprime.v1.queryresponse)|

<aside class="success">
This operation does not require authentication
</aside>

## com.coralogixapis.dataprime.v1.DataprimeQueryService.SubmitBackgroundQuery

<a id="opIdcom.coralogixapis.dataprime.v1.DataprimeQueryService.SubmitBackgroundQuery"></a>

> Code samples

`POST /api/v1/dataprime/background-query`

> Body parameter

```json
{
  "query": "string",
  "syntax": "QUERY_SYNTAX_UNSPECIFIED",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "nowDate": "2019-08-24T14:15:22Z"
}
```

<h3 id="com.coralogixapis.dataprime.v1.dataprimequeryservice.submitbackgroundquery-parameters">Parameters</h3>

|Name|In|Type|Required|Description|
|---|---|---|---|---|
|body|body|[com.coralogixapis.dataprime.v1.SubmitBackgroundQueryRequest](#schemacom.coralogixapis.dataprime.v1.submitbackgroundqueryrequest)|true|none|

> Example responses

> 200 Response

<h3 id="com.coralogixapis.dataprime.v1.dataprimequeryservice.submitbackgroundquery-responses">Responses</h3>

|Status|Meaning|Description|Schema|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[com.coralogixapis.dataprime.v1.SubmitBackgroundQueryResponse](#schemacom.coralogixapis.dataprime.v1.submitbackgroundqueryresponse)|

<aside class="success">
This operation does not require authentication
</aside>

## com.coralogixapis.dataprime.v1.DataprimeQueryService.GetBackgroundQueryStatus

<a id="opIdcom.coralogixapis.dataprime.v1.DataprimeQueryService.GetBackgroundQueryStatus"></a>

> Code samples

`POST /api/v1/dataprime/background-query/status`

> Body parameter

```json
{
  "queryId": "string"
}
```

<h3 id="com.coralogixapis.dataprime.v1.dataprimequeryservice.getbackgroundquerystatus-parameters">Parameters</h3>

|Name|In|Type|Required|Description|
|---|---|---|---|---|
|body|body|[com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusRequest](#schemacom.coralogixapis.dataprime.v1.getbackgroundquerystatusrequest)|true|none|

> Example responses

> 200 Response

<h3 id="com.coralogixapis.dataprime.v1.dataprimequeryservice.getbackgroundquerystatus-responses">Responses</h3>

|Status|Meaning|Description|Schema|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse](#schemacom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse)|

<aside class="success">
This operation does not require authentication
</aside>

## com.coralogixapis.dataprime.v1.DataprimeQueryService.GetBackgroundQueryData

<a id="opIdcom.coralogixapis.dataprime.v1.DataprimeQueryService.GetBackgroundQueryData"></a>

> Code samples

`POST /api/v1/dataprime/background-query/data`

> Body parameter

```json
{
  "queryId": "string"
}
```

<h3 id="com.coralogixapis.dataprime.v1.dataprimequeryservice.getbackgroundquerydata-parameters">Parameters</h3>

|Name|In|Type|Required|Description|
|---|---|---|---|---|
|body|body|[com.coralogixapis.dataprime.v1.GetBackgroundQueryDataRequest](#schemacom.coralogixapis.dataprime.v1.getbackgroundquerydatarequest)|true|none|

> Example responses

> 200 Response

<h3 id="com.coralogixapis.dataprime.v1.dataprimequeryservice.getbackgroundquerydata-responses">Responses</h3>

|Status|Meaning|Description|Schema|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[com.coralogixapis.dataprime.v1.GetBackgroundQueryDataResponse](#schemacom.coralogixapis.dataprime.v1.getbackgroundquerydataresponse)|

<aside class="success">
This operation does not require authentication
</aside>

## com.coralogixapis.dataprime.v1.DataprimeQueryService.CancelBackgroundQuery

<a id="opIdcom.coralogixapis.dataprime.v1.DataprimeQueryService.CancelBackgroundQuery"></a>

> Code samples

`POST /api/v1/dataprime/background-query/cancel`

> Body parameter

```json
{
  "queryId": "string"
}
```

<h3 id="com.coralogixapis.dataprime.v1.dataprimequeryservice.cancelbackgroundquery-parameters">Parameters</h3>

|Name|In|Type|Required|Description|
|---|---|---|---|---|
|body|body|[com.coralogixapis.dataprime.v1.CancelBackgroundQueryRequest](#schemacom.coralogixapis.dataprime.v1.cancelbackgroundqueryrequest)|true|none|

> Example responses

> 200 Response

<h3 id="com.coralogixapis.dataprime.v1.dataprimequeryservice.cancelbackgroundquery-responses">Responses</h3>

|Status|Meaning|Description|Schema|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[com.coralogixapis.dataprime.v1.CancelBackgroundQueryResponse](#schemacom.coralogixapis.dataprime.v1.cancelbackgroundqueryresponse)|

<aside class="success">
This operation does not require authentication
</aside>

# Schemas

<h2 id="tocS_com.coralogixapis.dataprime.v1.QueryRequest">com.coralogixapis.dataprime.v1.QueryRequest</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.queryrequest"></a>
<a id="schema_com.coralogixapis.dataprime.v1.QueryRequest"></a>
<a id="tocScom.coralogixapis.dataprime.v1.queryrequest"></a>
<a id="tocscom.coralogixapis.dataprime.v1.queryrequest"></a>

```json
{
  "query": "string",
  "metadata": {
    "tier": "TIER_UNSPECIFIED",
    "syntax": "QUERY_SYNTAX_UNSPECIFIED",
    "limit": 0,
    "startDate": "2019-08-24T14:15:22Z",
    "endDate": "2019-08-24T14:15:22Z",
    "defaultSource": "string",
    "strictFieldsValidation": true,
    "nowDate": "2019-08-24T14:15:22Z"
  }
}

```

dataprime text query request

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|query|string|true|none|query for which you seek results|
|metadata|[com.coralogixapis.dataprime.v1.Metadata](#schemacom.coralogixapis.dataprime.v1.metadata)|false|none|configuration of query execution|

<h2 id="tocS_com.coralogixapis.dataprime.v1.Metadata">com.coralogixapis.dataprime.v1.Metadata</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.metadata"></a>
<a id="schema_com.coralogixapis.dataprime.v1.Metadata"></a>
<a id="tocScom.coralogixapis.dataprime.v1.metadata"></a>
<a id="tocscom.coralogixapis.dataprime.v1.metadata"></a>

```json
{
  "tier": "TIER_UNSPECIFIED",
  "syntax": "QUERY_SYNTAX_UNSPECIFIED",
  "limit": 0,
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "defaultSource": "string",
  "strictFieldsValidation": true,
  "nowDate": "2019-08-24T14:15:22Z"
}

```

configuration of query execution

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|tier|[com.coralogixapis.dataprime.v1.Metadata.Tier](#schemacom.coralogixapis.dataprime.v1.metadata.tier)|false|none|tier on which query runs, default: TIER_FREQUENT_SEARCH|
|syntax|[com.coralogixapis.dataprime.v1.QuerySyntax](#schemacom.coralogixapis.dataprime.v1.querysyntax)|false|none|syntax of the query, default: QUERY_SYNTAX_DATAPRIME|
|limit|integer(int32)|false|none|limit number of results, default: 2000; max for TIER_FREQUENT_SEARCH: 12000;  max for TIER_ARCHIVE: 50000|
|startDate|string(date-time)|false|none|beginning of the time range for the query, default: end - 15 min or current time - 15 min if end is not defined, example 2023-11-05T13:15:30Z|
|endDate|string(date-time)|false|none|end of the time range for the query, default: start + 15 min or current time if start is not defined, example 2023-11-05T13:15:30Z|
|defaultSource|string|false|none|default value for source to be used when source is omitted in a query|
|strictFieldsValidation|boolean|false|none|prohibit using unknown fields, ones which were not detected in the ingested data, default = false|
|nowDate|string(date-time)|false|none|contextual `now` for the query, default: current time, example 2023-11-05T13:15:30Z|

<h2 id="tocS_com.coralogixapis.dataprime.v1.QuerySyntax">com.coralogixapis.dataprime.v1.QuerySyntax</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.querysyntax"></a>
<a id="schema_com.coralogixapis.dataprime.v1.QuerySyntax"></a>
<a id="tocScom.coralogixapis.dataprime.v1.querysyntax"></a>
<a id="tocscom.coralogixapis.dataprime.v1.querysyntax"></a>

```json
"QUERY_SYNTAX_UNSPECIFIED"

```

syntax of the query, default: QUERY_SYNTAX_DATAPRIME

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|*anonymous*|string|false|none|syntax of the query, default: QUERY_SYNTAX_DATAPRIME|

#### Enumerated Values

|Property|Value|
|---|---|
|*anonymous*|QUERY_SYNTAX_UNSPECIFIED|
|*anonymous*|QUERY_SYNTAX_LUCENE|
|*anonymous*|QUERY_SYNTAX_DATAPRIME|
|*anonymous*|QUERY_SYNTAX_LUCENE_UTF8_BASE64|
|*anonymous*|QUERY_SYNTAX_DATAPRIME_UTF8_BASE64|

<h2 id="tocS_com.coralogixapis.dataprime.v1.QueryResponse">com.coralogixapis.dataprime.v1.QueryResponse</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.queryresponse"></a>
<a id="schema_com.coralogixapis.dataprime.v1.QueryResponse"></a>
<a id="tocScom.coralogixapis.dataprime.v1.queryresponse"></a>
<a id="tocscom.coralogixapis.dataprime.v1.queryresponse"></a>

```json
{
  "error": {
    "message": "string",
    "code": {
      "rateLimitReached": {}
    }
  },
  "result": {
    "results": [
      {
        "metadata": [
          {
            "key": "string",
            "value": "string"
          }
        ],
        "labels": [
          {
            "key": "string",
            "value": "string"
          }
        ],
        "userData": "string"
      }
    ]
  },
  "warning": {
    "compileWarning": {
      "warningMessage": "string"
    },
    "timeRangeWarning": {
      "warningMessage": "string",
      "startDate": "2019-08-24T14:15:22Z",
      "endDate": "2019-08-24T14:15:22Z"
    },
    "numberOfResultsLimitWarning": {
      "numberOfResultsLimit": 0
    },
    "bytesScannedLimitWarning": {},
    "deprecationWarning": {
      "warningMessage": "string"
    },
    "blocksLimitWarning": {},
    "aggregationBucketsLimitWarning": {
      "aggregationBucketsLimit": 0
    },
    "archiveWarning": {
      "noMetastoreData": {},
      "bucketAccessDenied": {},
      "bucketReadFailed": {},
      "missingData": {}
    },
    "scrollTimeoutWarning": {},
    "fieldCountLimitWarning": {},
    "shuffleFileSizeLimitReachedWarning": {},
    "filesReadLimitWarning": {}
  },
  "queryId": {
    "queryId": "string"
  }
}

```

dataprime response for text query, at most one of the fields can be defined in this object

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|error|[com.coralogixapis.dataprime.v1.DataprimeError](#schemacom.coralogixapis.dataprime.v1.dataprimeerror)|false|none|wrapper for dataprime error|
|result|[com.coralogixapis.dataprime.v1.DataprimeResult](#schemacom.coralogixapis.dataprime.v1.dataprimeresult)|false|none|batch of results|
|warning|[com.coralogixapis.dataprime.v1.DataprimeWarning](#schemacom.coralogixapis.dataprime.v1.dataprimewarning)|false|none|warning message, at most one of the fields can be defined in this object|
|queryId|[com.coralogixapis.dataprime.v1.QueryId](#schemacom.coralogixapis.dataprime.v1.queryid)|false|none|internal identifier of the query. Can be used to simplify investigation of issues|

<h2 id="tocS_com.coralogixapis.dataprime.v1.DataprimeWarning">com.coralogixapis.dataprime.v1.DataprimeWarning</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.dataprimewarning"></a>
<a id="schema_com.coralogixapis.dataprime.v1.DataprimeWarning"></a>
<a id="tocScom.coralogixapis.dataprime.v1.dataprimewarning"></a>
<a id="tocscom.coralogixapis.dataprime.v1.dataprimewarning"></a>

```json
{
  "compileWarning": {
    "warningMessage": "string"
  },
  "timeRangeWarning": {
    "warningMessage": "string",
    "startDate": "2019-08-24T14:15:22Z",
    "endDate": "2019-08-24T14:15:22Z"
  },
  "numberOfResultsLimitWarning": {
    "numberOfResultsLimit": 0
  },
  "bytesScannedLimitWarning": {},
  "deprecationWarning": {
    "warningMessage": "string"
  },
  "blocksLimitWarning": {},
  "aggregationBucketsLimitWarning": {
    "aggregationBucketsLimit": 0
  },
  "archiveWarning": {
    "noMetastoreData": {},
    "bucketAccessDenied": {},
    "bucketReadFailed": {},
    "missingData": {}
  },
  "scrollTimeoutWarning": {},
  "fieldCountLimitWarning": {},
  "shuffleFileSizeLimitReachedWarning": {},
  "filesReadLimitWarning": {}
}

```

warning message, at most one of the fields can be defined in this object

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|compileWarning|[com.coralogixapis.dataprime.v1.CompileWarning](#schemacom.coralogixapis.dataprime.v1.compilewarning)|false|none|warning from Dataprime compilation|
|timeRangeWarning|[com.coralogixapis.dataprime.v1.TimeRangeWarning](#schemacom.coralogixapis.dataprime.v1.timerangewarning)|false|none|warning from applying time range to query|
|numberOfResultsLimitWarning|[com.coralogixapis.dataprime.v1.NumberOfResultsLimitWarning](#schemacom.coralogixapis.dataprime.v1.numberofresultslimitwarning)|false|none|warning from applying limit on number of results|
|bytesScannedLimitWarning|[com.coralogixapis.dataprime.v1.BytesScannedLimitWarning](#schemacom.coralogixapis.dataprime.v1.bytesscannedlimitwarning)|false|none|warning for reaching bytes scanned limit|
|deprecationWarning|[com.coralogixapis.dataprime.v1.DeprecationWarning](#schemacom.coralogixapis.dataprime.v1.deprecationwarning)|false|none|warning about deprecated elements|
|blocksLimitWarning|[com.coralogixapis.dataprime.v1.BlocksLimitWarning](#schemacom.coralogixapis.dataprime.v1.blockslimitwarning)|false|none|Warning for when query has reached maximum number of parquet blocks|
|aggregationBucketsLimitWarning|[com.coralogixapis.dataprime.v1.AggregationBucketsLimitWarning](#schemacom.coralogixapis.dataprime.v1.aggregationbucketslimitwarning)|false|none|warning for reaching aggregation buckets limit|
|archiveWarning|[com.coralogixapis.dataprime.v1.ArchiveWarning](#schemacom.coralogixapis.dataprime.v1.archivewarning)|false|none|wrapper for archive related warnings, at most one of the fields can be defined in this object|
|scrollTimeoutWarning|[com.coralogixapis.dataprime.v1.ScrollTimeoutWarning](#schemacom.coralogixapis.dataprime.v1.scrolltimeoutwarning)|false|none|warning for when OpenSearch scroll timeout is reached|
|fieldCountLimitWarning|[com.coralogixapis.dataprime.v1.FieldCountLimitWarning](#schemacom.coralogixapis.dataprime.v1.fieldcountlimitwarning)|false|none|warning for when result contain entries where number of fields is truncated|
|shuffleFileSizeLimitReachedWarning|[com.coralogixapis.dataprime.v1.ShuffleFileSizeLimitReachedWarning](#schemacom.coralogixapis.dataprime.v1.shufflefilesizelimitreachedwarning)|false|none|warning for when shuffle file size limit is reached - e.g. during a join with a large right side|
|filesReadLimitWarning|[com.coralogixapis.dataprime.v1.FilesReadLimitWarning](#schemacom.coralogixapis.dataprime.v1.filesreadlimitwarning)|false|none|warning for when query has reached maximum number of parquet files|

<h2 id="tocS_com.coralogixapis.dataprime.v1.CompileWarning">com.coralogixapis.dataprime.v1.CompileWarning</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.compilewarning"></a>
<a id="schema_com.coralogixapis.dataprime.v1.CompileWarning"></a>
<a id="tocScom.coralogixapis.dataprime.v1.compilewarning"></a>
<a id="tocscom.coralogixapis.dataprime.v1.compilewarning"></a>

```json
{
  "warningMessage": "string"
}

```

warning from Dataprime compilation

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|warningMessage|string|true|none|none|

<h2 id="tocS_com.coralogixapis.dataprime.v1.TimeRangeWarning">com.coralogixapis.dataprime.v1.TimeRangeWarning</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.timerangewarning"></a>
<a id="schema_com.coralogixapis.dataprime.v1.TimeRangeWarning"></a>
<a id="tocScom.coralogixapis.dataprime.v1.timerangewarning"></a>
<a id="tocscom.coralogixapis.dataprime.v1.timerangewarning"></a>

```json
{
  "warningMessage": "string",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z"
}

```

warning from applying time range to query

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|warningMessage|string|true|none|none|
|startDate|string(date-time)|false|none|example 2023-11-05T13:15:30Z|
|endDate|string(date-time)|false|none|example 2023-11-05T13:15:30Z|

<h2 id="tocS_com.coralogixapis.dataprime.v1.NumberOfResultsLimitWarning">com.coralogixapis.dataprime.v1.NumberOfResultsLimitWarning</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.numberofresultslimitwarning"></a>
<a id="schema_com.coralogixapis.dataprime.v1.NumberOfResultsLimitWarning"></a>
<a id="tocScom.coralogixapis.dataprime.v1.numberofresultslimitwarning"></a>
<a id="tocscom.coralogixapis.dataprime.v1.numberofresultslimitwarning"></a>

```json
{
  "numberOfResultsLimit": 0
}

```

warning from applying limit on number of results

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|numberOfResultsLimit|integer(int32)|true|none|none|

<h2 id="tocS_com.coralogixapis.dataprime.v1.BytesScannedLimitWarning">com.coralogixapis.dataprime.v1.BytesScannedLimitWarning</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.bytesscannedlimitwarning"></a>
<a id="schema_com.coralogixapis.dataprime.v1.BytesScannedLimitWarning"></a>
<a id="tocScom.coralogixapis.dataprime.v1.bytesscannedlimitwarning"></a>
<a id="tocscom.coralogixapis.dataprime.v1.bytesscannedlimitwarning"></a>

```json
{}

```

warning for reaching bytes scanned limit

### Properties

*None*

<h2 id="tocS_com.coralogixapis.dataprime.v1.AggregationBucketsLimitWarning">com.coralogixapis.dataprime.v1.AggregationBucketsLimitWarning</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.aggregationbucketslimitwarning"></a>
<a id="schema_com.coralogixapis.dataprime.v1.AggregationBucketsLimitWarning"></a>
<a id="tocScom.coralogixapis.dataprime.v1.aggregationbucketslimitwarning"></a>
<a id="tocscom.coralogixapis.dataprime.v1.aggregationbucketslimitwarning"></a>

```json
{
  "aggregationBucketsLimit": 0
}

```

warning for reaching aggregation buckets limit

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|aggregationBucketsLimit|integer(int32)|true|none|none|

<h2 id="tocS_com.coralogixapis.dataprime.v1.DeprecationWarning">com.coralogixapis.dataprime.v1.DeprecationWarning</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.deprecationwarning"></a>
<a id="schema_com.coralogixapis.dataprime.v1.DeprecationWarning"></a>
<a id="tocScom.coralogixapis.dataprime.v1.deprecationwarning"></a>
<a id="tocscom.coralogixapis.dataprime.v1.deprecationwarning"></a>

```json
{
  "warningMessage": "string"
}

```

warning about deprecated elements

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|warningMessage|string|true|none|none|

<h2 id="tocS_com.coralogixapis.dataprime.v1.BlocksLimitWarning">com.coralogixapis.dataprime.v1.BlocksLimitWarning</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.blockslimitwarning"></a>
<a id="schema_com.coralogixapis.dataprime.v1.BlocksLimitWarning"></a>
<a id="tocScom.coralogixapis.dataprime.v1.blockslimitwarning"></a>
<a id="tocscom.coralogixapis.dataprime.v1.blockslimitwarning"></a>

```json
{}

```

Warning for when query has reached maximum number of parquet blocks

### Properties

*None*

<h2 id="tocS_com.coralogixapis.dataprime.v1.FilesReadLimitWarning">com.coralogixapis.dataprime.v1.FilesReadLimitWarning</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.filesreadlimitwarning"></a>
<a id="schema_com.coralogixapis.dataprime.v1.FilesReadLimitWarning"></a>
<a id="tocScom.coralogixapis.dataprime.v1.filesreadlimitwarning"></a>
<a id="tocscom.coralogixapis.dataprime.v1.filesreadlimitwarning"></a>

```json
{}

```

warning for when query has reached maximum number of parquet files

### Properties

*None*

<h2 id="tocS_com.coralogixapis.dataprime.v1.DataprimeResult">com.coralogixapis.dataprime.v1.DataprimeResult</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.dataprimeresult"></a>
<a id="schema_com.coralogixapis.dataprime.v1.DataprimeResult"></a>
<a id="tocScom.coralogixapis.dataprime.v1.dataprimeresult"></a>
<a id="tocscom.coralogixapis.dataprime.v1.dataprimeresult"></a>

```json
{
  "results": [
    {
      "metadata": [
        {
          "key": "string",
          "value": "string"
        }
      ],
      "labels": [
        {
          "key": "string",
          "value": "string"
        }
      ],
      "userData": "string"
    }
  ]
}

```

batch of results

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|results|[[com.coralogixapis.dataprime.v1.DataprimeResults](#schemacom.coralogixapis.dataprime.v1.dataprimeresults)]|true|none|[wrapper for dataprime results]|

<h2 id="tocS_com.coralogixapis.dataprime.v1.DataprimeResults">com.coralogixapis.dataprime.v1.DataprimeResults</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.dataprimeresults"></a>
<a id="schema_com.coralogixapis.dataprime.v1.DataprimeResults"></a>
<a id="tocScom.coralogixapis.dataprime.v1.dataprimeresults"></a>
<a id="tocscom.coralogixapis.dataprime.v1.dataprimeresults"></a>

```json
{
  "metadata": [
    {
      "key": "string",
      "value": "string"
    }
  ],
  "labels": [
    {
      "key": "string",
      "value": "string"
    }
  ],
  "userData": "string"
}

```

wrapper for dataprime results

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|metadata|[[com.coralogixapis.dataprime.v1.DataprimeResults.KeyValue](#schemacom.coralogixapis.dataprime.v1.dataprimeresults.keyvalue)]|true|none|none|
|labels|[[com.coralogixapis.dataprime.v1.DataprimeResults.KeyValue](#schemacom.coralogixapis.dataprime.v1.dataprimeresults.keyvalue)]|true|none|none|
|userData|string|true|none|none|

<h2 id="tocS_com.coralogixapis.dataprime.v1.DataprimeError">com.coralogixapis.dataprime.v1.DataprimeError</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.dataprimeerror"></a>
<a id="schema_com.coralogixapis.dataprime.v1.DataprimeError"></a>
<a id="tocScom.coralogixapis.dataprime.v1.dataprimeerror"></a>
<a id="tocscom.coralogixapis.dataprime.v1.dataprimeerror"></a>

```json
{
  "message": "string",
  "code": {
    "rateLimitReached": {}
  }
}

```

wrapper for dataprime error

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|message|string|true|none|none|
|code|[com.coralogixapis.dataprime.v1.DataprimeError.Code](#schemacom.coralogixapis.dataprime.v1.dataprimeerror.code)|false|none|none|

<h2 id="tocS_com.coralogixapis.dataprime.v1.QueryId">com.coralogixapis.dataprime.v1.QueryId</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.queryid"></a>
<a id="schema_com.coralogixapis.dataprime.v1.QueryId"></a>
<a id="tocScom.coralogixapis.dataprime.v1.queryid"></a>
<a id="tocscom.coralogixapis.dataprime.v1.queryid"></a>

```json
{
  "queryId": "string"
}

```

internal identifier of the query. Can be used to simplify investigation of issues

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|queryId|string|true|none|none|

<h2 id="tocS_com.coralogixapis.dataprime.v1.ArchiveWarning">com.coralogixapis.dataprime.v1.ArchiveWarning</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.archivewarning"></a>
<a id="schema_com.coralogixapis.dataprime.v1.ArchiveWarning"></a>
<a id="tocScom.coralogixapis.dataprime.v1.archivewarning"></a>
<a id="tocscom.coralogixapis.dataprime.v1.archivewarning"></a>

```json
{
  "noMetastoreData": {},
  "bucketAccessDenied": {},
  "bucketReadFailed": {},
  "missingData": {}
}

```

wrapper for archive related warnings, at most one of the fields can be defined in this object

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|noMetastoreData|[com.coralogixapis.dataprime.v1.ArchiveWarning.NoMetastoreData](#schemacom.coralogixapis.dataprime.v1.archivewarning.nometastoredata)|false|none|none|
|bucketAccessDenied|[com.coralogixapis.dataprime.v1.ArchiveWarning.BucketAccessDenied](#schemacom.coralogixapis.dataprime.v1.archivewarning.bucketaccessdenied)|false|none|none|
|bucketReadFailed|[com.coralogixapis.dataprime.v1.ArchiveWarning.BucketReadFailed](#schemacom.coralogixapis.dataprime.v1.archivewarning.bucketreadfailed)|false|none|none|
|missingData|[com.coralogixapis.dataprime.v1.ArchiveWarning.MissingData](#schemacom.coralogixapis.dataprime.v1.archivewarning.missingdata)|false|none|none|

<h2 id="tocS_com.coralogixapis.dataprime.v1.ScrollTimeoutWarning">com.coralogixapis.dataprime.v1.ScrollTimeoutWarning</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.scrolltimeoutwarning"></a>
<a id="schema_com.coralogixapis.dataprime.v1.ScrollTimeoutWarning"></a>
<a id="tocScom.coralogixapis.dataprime.v1.scrolltimeoutwarning"></a>
<a id="tocscom.coralogixapis.dataprime.v1.scrolltimeoutwarning"></a>

```json
{}

```

warning for when OpenSearch scroll timeout is reached

### Properties

*None*

<h2 id="tocS_com.coralogixapis.dataprime.v1.FieldCountLimitWarning">com.coralogixapis.dataprime.v1.FieldCountLimitWarning</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.fieldcountlimitwarning"></a>
<a id="schema_com.coralogixapis.dataprime.v1.FieldCountLimitWarning"></a>
<a id="tocScom.coralogixapis.dataprime.v1.fieldcountlimitwarning"></a>
<a id="tocscom.coralogixapis.dataprime.v1.fieldcountlimitwarning"></a>

```json
{}

```

warning for when result contain entries where number of fields is truncated

### Properties

*None*

<h2 id="tocS_com.coralogixapis.dataprime.v1.ShuffleFileSizeLimitReachedWarning">com.coralogixapis.dataprime.v1.ShuffleFileSizeLimitReachedWarning</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.shufflefilesizelimitreachedwarning"></a>
<a id="schema_com.coralogixapis.dataprime.v1.ShuffleFileSizeLimitReachedWarning"></a>
<a id="tocScom.coralogixapis.dataprime.v1.shufflefilesizelimitreachedwarning"></a>
<a id="tocscom.coralogixapis.dataprime.v1.shufflefilesizelimitreachedwarning"></a>

```json
{}

```

warning for when shuffle file size limit is reached - e.g. during a join with a large right side

### Properties

*None*

<h2 id="tocS_com.coralogixapis.dataprime.v1.ResponseMetadata">com.coralogixapis.dataprime.v1.ResponseMetadata</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.responsemetadata"></a>
<a id="schema_com.coralogixapis.dataprime.v1.ResponseMetadata"></a>
<a id="tocScom.coralogixapis.dataprime.v1.responsemetadata"></a>
<a id="tocscom.coralogixapis.dataprime.v1.responsemetadata"></a>

```json
{
  "statistics": {
    "bytesScanned": "string"
  }
}

```

this is only used in BG queries but will be also used as part of QueryResponse

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|statistics|[com.coralogixapis.dataprime.v1.ResponseMetadata.Statistics](#schemacom.coralogixapis.dataprime.v1.responsemetadata.statistics)|false|none|none|

<h2 id="tocS_com.coralogixapis.dataprime.v1.SubmitBackgroundQueryRequest">com.coralogixapis.dataprime.v1.SubmitBackgroundQueryRequest</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.submitbackgroundqueryrequest"></a>
<a id="schema_com.coralogixapis.dataprime.v1.SubmitBackgroundQueryRequest"></a>
<a id="tocScom.coralogixapis.dataprime.v1.submitbackgroundqueryrequest"></a>
<a id="tocscom.coralogixapis.dataprime.v1.submitbackgroundqueryrequest"></a>

```json
{
  "query": "string",
  "syntax": "QUERY_SYNTAX_UNSPECIFIED",
  "startDate": "2019-08-24T14:15:22Z",
  "endDate": "2019-08-24T14:15:22Z",
  "nowDate": "2019-08-24T14:15:22Z"
}

```

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|query|string|true|none|none|
|syntax|[com.coralogixapis.dataprime.v1.QuerySyntax](#schemacom.coralogixapis.dataprime.v1.querysyntax)|true|none|syntax of the query, default: QUERY_SYNTAX_DATAPRIME|
|startDate|string(date-time)|false|none|example 2023-11-05T13:15:30Z|
|endDate|string(date-time)|false|none|example 2023-11-05T13:15:30Z|
|nowDate|string(date-time)|false|none|example 2023-11-05T13:15:30Z|

<h2 id="tocS_com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusRequest">com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusRequest</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.getbackgroundquerystatusrequest"></a>
<a id="schema_com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusRequest"></a>
<a id="tocScom.coralogixapis.dataprime.v1.getbackgroundquerystatusrequest"></a>
<a id="tocscom.coralogixapis.dataprime.v1.getbackgroundquerystatusrequest"></a>

```json
{
  "queryId": "string"
}

```

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|queryId|string|true|none|none|

<h2 id="tocS_com.coralogixapis.dataprime.v1.GetBackgroundQueryDataRequest">com.coralogixapis.dataprime.v1.GetBackgroundQueryDataRequest</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.getbackgroundquerydatarequest"></a>
<a id="schema_com.coralogixapis.dataprime.v1.GetBackgroundQueryDataRequest"></a>
<a id="tocScom.coralogixapis.dataprime.v1.getbackgroundquerydatarequest"></a>
<a id="tocscom.coralogixapis.dataprime.v1.getbackgroundquerydatarequest"></a>

```json
{
  "queryId": "string"
}

```

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|queryId|string|true|none|none|

<h2 id="tocS_com.coralogixapis.dataprime.v1.CancelBackgroundQueryRequest">com.coralogixapis.dataprime.v1.CancelBackgroundQueryRequest</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.cancelbackgroundqueryrequest"></a>
<a id="schema_com.coralogixapis.dataprime.v1.CancelBackgroundQueryRequest"></a>
<a id="tocScom.coralogixapis.dataprime.v1.cancelbackgroundqueryrequest"></a>
<a id="tocscom.coralogixapis.dataprime.v1.cancelbackgroundqueryrequest"></a>

```json
{
  "queryId": "string"
}

```

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|queryId|string|true|none|none|

<h2 id="tocS_com.coralogixapis.dataprime.v1.SubmitBackgroundQueryResponse">com.coralogixapis.dataprime.v1.SubmitBackgroundQueryResponse</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.submitbackgroundqueryresponse"></a>
<a id="schema_com.coralogixapis.dataprime.v1.SubmitBackgroundQueryResponse"></a>
<a id="tocScom.coralogixapis.dataprime.v1.submitbackgroundqueryresponse"></a>
<a id="tocscom.coralogixapis.dataprime.v1.submitbackgroundqueryresponse"></a>

```json
{
  "warnings": [
    {
      "compileWarning": {
        "warningMessage": "string"
      },
      "timeRangeWarning": {
        "warningMessage": "string",
        "startDate": "2019-08-24T14:15:22Z",
        "endDate": "2019-08-24T14:15:22Z"
      },
      "numberOfResultsLimitWarning": {
        "numberOfResultsLimit": 0
      },
      "bytesScannedLimitWarning": {},
      "deprecationWarning": {
        "warningMessage": "string"
      },
      "blocksLimitWarning": {},
      "aggregationBucketsLimitWarning": {
        "aggregationBucketsLimit": 0
      },
      "archiveWarning": {
        "noMetastoreData": {},
        "bucketAccessDenied": {},
        "bucketReadFailed": {},
        "missingData": {}
      },
      "scrollTimeoutWarning": {},
      "fieldCountLimitWarning": {},
      "shuffleFileSizeLimitReachedWarning": {},
      "filesReadLimitWarning": {}
    }
  ],
  "queryId": "string"
}

```

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|warnings|[[com.coralogixapis.dataprime.v1.DataprimeWarning](#schemacom.coralogixapis.dataprime.v1.dataprimewarning)]|true|none|all warnings that can apprear during query submission|
|queryId|string|true|none|none|

<h2 id="tocS_com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse">com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse"></a>
<a id="schema_com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse"></a>
<a id="tocScom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse"></a>
<a id="tocscom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse"></a>

```json
{
  "running": {
    "runningSince": "2019-08-24T14:15:22Z"
  },
  "terminated": {
    "success": {},
    "error": {
      "cancelled": {},
      "failed": {
        "reason": "string"
      },
      "timedOut": {}
    },
    "cancelled": {},
    "runningSince": "2019-08-24T14:15:22Z",
    "terminatedAt": "2019-08-24T14:15:22Z"
  },
  "metadata": [
    {
      "statistics": {
        "bytesScanned": "string"
      }
    }
  ],
  "warnings": [
    {
      "compileWarning": {
        "warningMessage": "string"
      },
      "timeRangeWarning": {
        "warningMessage": "string",
        "startDate": "2019-08-24T14:15:22Z",
        "endDate": "2019-08-24T14:15:22Z"
      },
      "numberOfResultsLimitWarning": {
        "numberOfResultsLimit": 0
      },
      "bytesScannedLimitWarning": {},
      "deprecationWarning": {
        "warningMessage": "string"
      },
      "blocksLimitWarning": {},
      "aggregationBucketsLimitWarning": {
        "aggregationBucketsLimit": 0
      },
      "archiveWarning": {
        "noMetastoreData": {},
        "bucketAccessDenied": {},
        "bucketReadFailed": {},
        "missingData": {}
      },
      "scrollTimeoutWarning": {},
      "fieldCountLimitWarning": {},
      "shuffleFileSizeLimitReachedWarning": {},
      "filesReadLimitWarning": {}
    }
  ],
  "waitingForExecution": {},
  "submittedAt": "2019-08-24T14:15:22Z"
}

```

at most one of the following fields can be defined in this object: running,terminated,waiting_for_execution

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|running|[com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Running](#schemacom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.running)|false|none|none|
|terminated|[com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated](#schemacom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated)|false|none|at most one of the following fields can be defined in this object: success,error,cancelled|
|metadata|[[com.coralogixapis.dataprime.v1.ResponseMetadata](#schemacom.coralogixapis.dataprime.v1.responsemetadata)]|true|none|does not contain duplicate messages|
|warnings|[[com.coralogixapis.dataprime.v1.DataprimeWarning](#schemacom.coralogixapis.dataprime.v1.dataprimewarning)]|true|none|includes all warnings (e.g.: warnings that can apprear during query submission + runtime warnings)|
|waitingForExecution|[com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.WaitingForExecution](#schemacom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.waitingforexecution)|false|none|none|
|submittedAt|string(date-time)|true|none|example 2023-11-05T13:15:30Z|

<h2 id="tocS_com.coralogixapis.dataprime.v1.GetBackgroundQueryDataResponse">com.coralogixapis.dataprime.v1.GetBackgroundQueryDataResponse</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.getbackgroundquerydataresponse"></a>
<a id="schema_com.coralogixapis.dataprime.v1.GetBackgroundQueryDataResponse"></a>
<a id="tocScom.coralogixapis.dataprime.v1.getbackgroundquerydataresponse"></a>
<a id="tocscom.coralogixapis.dataprime.v1.getbackgroundquerydataresponse"></a>

```json
{
  "response": {
    "results": {
      "results": [
        {
          "metadata": [
            {
              "key": "string",
              "value": "string"
            }
          ],
          "labels": [
            {
              "key": "string",
              "value": "string"
            }
          ],
          "userData": "string"
        }
      ]
    }
  }
}

```

using extensible structure for future-proofing

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|response|[com.coralogixapis.dataprime.v1.GetDataResponse](#schemacom.coralogixapis.dataprime.v1.getdataresponse)|false|none|none|

<h2 id="tocS_com.coralogixapis.dataprime.v1.GetDataResponse">com.coralogixapis.dataprime.v1.GetDataResponse</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.getdataresponse"></a>
<a id="schema_com.coralogixapis.dataprime.v1.GetDataResponse"></a>
<a id="tocScom.coralogixapis.dataprime.v1.getdataresponse"></a>
<a id="tocscom.coralogixapis.dataprime.v1.getdataresponse"></a>

```json
{
  "results": {
    "results": [
      {
        "metadata": [
          {
            "key": "string",
            "value": "string"
          }
        ],
        "labels": [
          {
            "key": "string",
            "value": "string"
          }
        ],
        "userData": "string"
      }
    ]
  }
}

```

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|results|[com.coralogixapis.dataprime.v1.DataprimeResult](#schemacom.coralogixapis.dataprime.v1.dataprimeresult)|true|none|batch of results|

<h2 id="tocS_com.coralogixapis.dataprime.v1.CancelBackgroundQueryResponse">com.coralogixapis.dataprime.v1.CancelBackgroundQueryResponse</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.cancelbackgroundqueryresponse"></a>
<a id="schema_com.coralogixapis.dataprime.v1.CancelBackgroundQueryResponse"></a>
<a id="tocScom.coralogixapis.dataprime.v1.cancelbackgroundqueryresponse"></a>
<a id="tocscom.coralogixapis.dataprime.v1.cancelbackgroundqueryresponse"></a>

```json
{}

```

### Properties

*None*

<h2 id="tocS_com.coralogixapis.dataprime.v1.Metadata.Tier">com.coralogixapis.dataprime.v1.Metadata.Tier</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.metadata.tier"></a>
<a id="schema_com.coralogixapis.dataprime.v1.Metadata.Tier"></a>
<a id="tocScom.coralogixapis.dataprime.v1.metadata.tier"></a>
<a id="tocscom.coralogixapis.dataprime.v1.metadata.tier"></a>

```json
"TIER_UNSPECIFIED"

```

tier on which query runs, default: TIER_FREQUENT_SEARCH

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|*anonymous*|string|false|none|tier on which query runs, default: TIER_FREQUENT_SEARCH|

#### Enumerated Values

|Property|Value|
|---|---|
|*anonymous*|TIER_UNSPECIFIED|
|*anonymous*|TIER_ARCHIVE|
|*anonymous*|TIER_FREQUENT_SEARCH|

<h2 id="tocS_com.coralogixapis.dataprime.v1.DataprimeResults.KeyValue">com.coralogixapis.dataprime.v1.DataprimeResults.KeyValue</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.dataprimeresults.keyvalue"></a>
<a id="schema_com.coralogixapis.dataprime.v1.DataprimeResults.KeyValue"></a>
<a id="tocScom.coralogixapis.dataprime.v1.dataprimeresults.keyvalue"></a>
<a id="tocscom.coralogixapis.dataprime.v1.dataprimeresults.keyvalue"></a>

```json
{
  "key": "string",
  "value": "string"
}

```

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|key|string|true|none|none|
|value|string|true|none|none|

<h2 id="tocS_com.coralogixapis.dataprime.v1.DataprimeError.RateLimitReached">com.coralogixapis.dataprime.v1.DataprimeError.RateLimitReached</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.dataprimeerror.ratelimitreached"></a>
<a id="schema_com.coralogixapis.dataprime.v1.DataprimeError.RateLimitReached"></a>
<a id="tocScom.coralogixapis.dataprime.v1.dataprimeerror.ratelimitreached"></a>
<a id="tocscom.coralogixapis.dataprime.v1.dataprimeerror.ratelimitreached"></a>

```json
{}

```

### Properties

*None*

<h2 id="tocS_com.coralogixapis.dataprime.v1.DataprimeError.Code">com.coralogixapis.dataprime.v1.DataprimeError.Code</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.dataprimeerror.code"></a>
<a id="schema_com.coralogixapis.dataprime.v1.DataprimeError.Code"></a>
<a id="tocScom.coralogixapis.dataprime.v1.dataprimeerror.code"></a>
<a id="tocscom.coralogixapis.dataprime.v1.dataprimeerror.code"></a>

```json
{
  "rateLimitReached": {}
}

```

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|rateLimitReached|[com.coralogixapis.dataprime.v1.DataprimeError.RateLimitReached](#schemacom.coralogixapis.dataprime.v1.dataprimeerror.ratelimitreached)|false|none|none|

<h2 id="tocS_com.coralogixapis.dataprime.v1.ArchiveWarning.NoMetastoreData">com.coralogixapis.dataprime.v1.ArchiveWarning.NoMetastoreData</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.archivewarning.nometastoredata"></a>
<a id="schema_com.coralogixapis.dataprime.v1.ArchiveWarning.NoMetastoreData"></a>
<a id="tocScom.coralogixapis.dataprime.v1.archivewarning.nometastoredata"></a>
<a id="tocscom.coralogixapis.dataprime.v1.archivewarning.nometastoredata"></a>

```json
{}

```

### Properties

*None*

<h2 id="tocS_com.coralogixapis.dataprime.v1.ArchiveWarning.BucketAccessDenied">com.coralogixapis.dataprime.v1.ArchiveWarning.BucketAccessDenied</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.archivewarning.bucketaccessdenied"></a>
<a id="schema_com.coralogixapis.dataprime.v1.ArchiveWarning.BucketAccessDenied"></a>
<a id="tocScom.coralogixapis.dataprime.v1.archivewarning.bucketaccessdenied"></a>
<a id="tocscom.coralogixapis.dataprime.v1.archivewarning.bucketaccessdenied"></a>

```json
{}

```

### Properties

*None*

<h2 id="tocS_com.coralogixapis.dataprime.v1.ArchiveWarning.BucketReadFailed">com.coralogixapis.dataprime.v1.ArchiveWarning.BucketReadFailed</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.archivewarning.bucketreadfailed"></a>
<a id="schema_com.coralogixapis.dataprime.v1.ArchiveWarning.BucketReadFailed"></a>
<a id="tocScom.coralogixapis.dataprime.v1.archivewarning.bucketreadfailed"></a>
<a id="tocscom.coralogixapis.dataprime.v1.archivewarning.bucketreadfailed"></a>

```json
{}

```

### Properties

*None*

<h2 id="tocS_com.coralogixapis.dataprime.v1.ArchiveWarning.MissingData">com.coralogixapis.dataprime.v1.ArchiveWarning.MissingData</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.archivewarning.missingdata"></a>
<a id="schema_com.coralogixapis.dataprime.v1.ArchiveWarning.MissingData"></a>
<a id="tocScom.coralogixapis.dataprime.v1.archivewarning.missingdata"></a>
<a id="tocscom.coralogixapis.dataprime.v1.archivewarning.missingdata"></a>

```json
{}

```

### Properties

*None*

<h2 id="tocS_com.coralogixapis.dataprime.v1.ResponseMetadata.Statistics">com.coralogixapis.dataprime.v1.ResponseMetadata.Statistics</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.responsemetadata.statistics"></a>
<a id="schema_com.coralogixapis.dataprime.v1.ResponseMetadata.Statistics"></a>
<a id="tocScom.coralogixapis.dataprime.v1.responsemetadata.statistics"></a>
<a id="tocscom.coralogixapis.dataprime.v1.responsemetadata.statistics"></a>

```json
{
  "bytesScanned": "string"
}

```

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|bytesScanned|string(int64)|true|none|none|

<h2 id="tocS_com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.WaitingForExecution">com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.WaitingForExecution</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.waitingforexecution"></a>
<a id="schema_com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.WaitingForExecution"></a>
<a id="tocScom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.waitingforexecution"></a>
<a id="tocscom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.waitingforexecution"></a>

```json
{}

```

### Properties

*None*

<h2 id="tocS_com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Running">com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Running</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.running"></a>
<a id="schema_com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Running"></a>
<a id="tocScom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.running"></a>
<a id="tocscom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.running"></a>

```json
{
  "runningSince": "2019-08-24T14:15:22Z"
}

```

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|runningSince|string(date-time)|true|none|example 2023-11-05T13:15:30Z|

<h2 id="tocS_com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated">com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated"></a>
<a id="schema_com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated"></a>
<a id="tocScom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated"></a>
<a id="tocscom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated"></a>

```json
{
  "success": {},
  "error": {
    "cancelled": {},
    "failed": {
      "reason": "string"
    },
    "timedOut": {}
  },
  "cancelled": {},
  "runningSince": "2019-08-24T14:15:22Z",
  "terminatedAt": "2019-08-24T14:15:22Z"
}

```

at most one of the following fields can be defined in this object: success,error,cancelled

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|success|[com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Success](#schemacom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.success)|false|none|none|
|error|[com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Error](#schemacom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.error)|false|none|at most one of the fields can be defined in this object|
|cancelled|[com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Cancelled](#schemacom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.cancelled)|false|none|none|
|runningSince|string(date-time)|true|none|example 2023-11-05T13:15:30Z|
|terminatedAt|string(date-time)|true|none|example 2023-11-05T13:15:30Z|

<h2 id="tocS_com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Success">com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Success</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.success"></a>
<a id="schema_com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Success"></a>
<a id="tocScom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.success"></a>
<a id="tocscom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.success"></a>

```json
{}

```

### Properties

*None*

<h2 id="tocS_com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Cancelled">com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Cancelled</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.cancelled"></a>
<a id="schema_com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Cancelled"></a>
<a id="tocScom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.cancelled"></a>
<a id="tocscom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.cancelled"></a>

```json
{}

```

### Properties

*None*

<h2 id="tocS_com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Error">com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Error</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.error"></a>
<a id="schema_com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Error"></a>
<a id="tocScom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.error"></a>
<a id="tocscom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.error"></a>

```json
{
  "cancelled": {},
  "failed": {
    "reason": "string"
  },
  "timedOut": {}
}

```

at most one of the fields can be defined in this object

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|cancelled|[com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Cancelled](#schemacom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.cancelled)|false|none|none|
|failed|[com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Error.Failed](#schemacom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.error.failed)|false|none|none|
|timedOut|[com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Error.TimedOut](#schemacom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.error.timedout)|false|none|none|

<h2 id="tocS_com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Error.TimedOut">com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Error.TimedOut</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.error.timedout"></a>
<a id="schema_com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Error.TimedOut"></a>
<a id="tocScom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.error.timedout"></a>
<a id="tocscom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.error.timedout"></a>

```json
{}

```

### Properties

*None*

<h2 id="tocS_com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Error.Cancelled">com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Error.Cancelled</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.error.cancelled"></a>
<a id="schema_com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Error.Cancelled"></a>
<a id="tocScom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.error.cancelled"></a>
<a id="tocscom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.error.cancelled"></a>

```json
{}

```

### Properties

*None*

<h2 id="tocS_com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Error.Failed">com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Error.Failed</h2>
<!-- backwards compatibility -->
<a id="schemacom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.error.failed"></a>
<a id="schema_com.coralogixapis.dataprime.v1.GetBackgroundQueryStatusResponse.Terminated.Error.Failed"></a>
<a id="tocScom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.error.failed"></a>
<a id="tocscom.coralogixapis.dataprime.v1.getbackgroundquerystatusresponse.terminated.error.failed"></a>

```json
{
  "reason": "string"
}

```

### Properties

|Name|Type|Required|Restrictions|Description|
|---|---|---|---|---|
|reason|string|true|none|none|

