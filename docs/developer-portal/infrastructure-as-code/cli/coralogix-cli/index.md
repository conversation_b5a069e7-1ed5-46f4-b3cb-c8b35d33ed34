---
title: "Coralogix CLI"
date: "2021-08-30"
coverImage: "Direct-Query-CLI-S3.png"
description: "Coralogix is on a mission to provide a CLI that will enable you to do all your Coralogix operations without the web interface involved"
---

Coralogix is on a mission to provide a CLI that will enable you to do all your Coralogix operations without the web interface involved. Currently, we support:

1. [Quota management](https://coralogix.com/tutorials/quota-management/) to help you manage quota across your teams.

3. [SAML management](https://coralogix.com/tutorials/saml-management-via-cli/) allows the management of SAML SSO configuration by admin users.

5. [Team management](https://coralogix.com/tutorials/team-management-via-cli/) allows admins to create teams and invite users.

7. LiveTail (a live log stream from all your services). You can filter your logs by Coralogix metadata fields such as application name, subsystem name, and severity and also by querying the data itself. Live tail is much like how the stern command in K8s streams logs in real-time. 

## Install the Coralogix CLI for MacOS/Linux

### Requirements
To use this API you need to [create](../../../../user-guides/account-management/api-keys/api-keys/index.md) a personal or team API key. It’s recommended to use permission presets, as they are automatically updated with all relevant permissions. Alternatively, you can manually add individual permissions.

| Preset | Action | Description |
| --- | --- | --- |
| DataQuerying | `LEGACY-ARCHIVE-QUERIES:EXECUTE` <br> `LOGS.DATA-API#HIGH:READDATA` <br> `LOGS.DATA-API#LOW:READDATA` <br> `METRICS.DATA-API#HIGH:READDATA` <br> `SPANS.DATA-API#HIGH:READDATA` <br> `SPANS.DATA-API#LOW:READDATA`| Query Data from the Archive <br> Query Frequent Search Logs <br> Query Monitoring & Compliance Logs <br> Query Metrics <br> Query Frequent Search Spans <br> Query Monitoring & Compliance Spans | 
| LiveTail | `LIVETAIL:READ` | View LiveTail Data |

### Download

``` bash
curl -O https://coralogix-public.s3-eu-west-1.amazonaws.com/cxctl/latest/cxctl-macOS.gz
```

Or

``` bash
curl -O https://coralogix-public.s3-eu-west-1.amazonaws.com/cxctl/latest/cxctl-Linux.gz
```

### Unzip

``` bash
gunzip -N cxctl-*.gz
```

### Make executable

``` bash
chmod +x cxctl
```

## Set environment variables:

``` bash
export CORALOGIX_API_KEY=<cx_api_key>
```

## Scan option

|   |   |
|---|---|
|S3 bucket| `\--bucket <bucket name>, -b <bucket name>` |
| Time range - Start  | `--range-start <start time>, -s <start time>` |
| Time range - End | `--range-end <end time>, -e <end time>` |
| Application | `--application <app1,app2>` |
| Subsystem | `--subsystem <sub1,sub2>` |     
| Severity | `--severity <severity>` |     
| Limit the number of logs | `-c <number>` | 
| Query | `" <Your Query> "` |                         

## Scan examples

| Query           | Explanation                 | 
|---------------------------|-----------------------------------|
| `./cxctl scan --bucket coralogix-tutorial -s "2020-08-18T16:30:00Z" -e "2020-08-18T17:00:00Z" -c 5`             | Retrieves the first 5 rows within the time range        |
| `./cxctl scan --bucket coralogix-tutorial --range-start 2020-08-18T00:01:01Z --range-end 2020-08-18T00:09:05Z -c 5 "NOT s3_bucket:amir-blog-logs"`   | Retrieves the top 5 results of the query within the time range |
| `./cxctl scan -b coralogix-tutorial -s 2020-08-18T00:01:01Z -e 2020-08-18T16:59:05Z --application-name Prod --subsystem Coralogix --severity info`                       | Retrieves all the logs from Prod application and Coralogix subsystem with info severity within the time range               | 

## Histogram

Prints a time-based log count histogram.

|   |   |
|---|---|
|S3 bucket| `\--bucket <bucket name>, -b <bucket name>` |
| Time range - Start  | `--range-start <start time>, -s <start time>` |
| Time range - End | `--range-end <end time>, -e <end time>` |
| Application | `--application <app1,app2>` |
| Subsystem | `--subsystem <sub1,sub2>` |     
| Severity | `--severity <severity>` |     
| Limit the number of logs | `-c <number>` | 
| Query | `" <your_query> "` |

## Log streaming

In order to stream the logs in real-time (as they are being archived) use the following command:

``` bash
./cxctl livetail --region "region-name" --api-key <cx_api_key> --format pretty --application "app-name" --subsystem "subsystem-name"
```

A Coralogix API key can be passed to the scanner through the \`--api-key\` argument or as an environment variable

Streaming supports the following regions:

| Region | US1 | US2 | EU1 | EU1 | AP1 | AP2 | 
| --- | --- | --- | --- | --- | --- | --- |
| region-name | us1 | us2 | eu | se | in | sg |

By default, the logs use the \`pretty\` format. A \`raw\` format is also available which prints out the logs as JSON objects

The scanner will tail the logs infinitely by default but can stop after a specified number of log lines using --count

## LiveTail option

|   |   |
|---|---|
|Coralogix API Key| `--api-key <cx_api_key>, -k <cx_api_key>` |
| Application name to filter on  | `--application <app1>,<app2>` |
| Add a custom field to the colored fields when pretty-printing | `--color-by <field path>` |
| How many lines to read | `--count <count>, -c <count>` |
| How to render the logs (raw/pretty) | `--format <format>` |     
| Print logs without any colors | `--monochrome, -m` |     
| Coralogix account region (eu/in/us) | `--region <region>, -r <region>` | 
| Subsystem to filter on | `--subsystem <sub1>,<sub2>` |
| Severity to filter on (case-sensitive - use DEBUG/INFO/etc.) | `--severity <severity>` |