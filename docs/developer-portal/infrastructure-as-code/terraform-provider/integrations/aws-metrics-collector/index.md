# AWS Metrics from CloudWatch

## Overview

AWS Metrics from CloudWatch integration will poll all your metrics from specified AWS account every 10 minutes, with one exception so far which is S3 metrics that are avaliable on CloudWatch side daily. Tags and metric definitions are pulled every 5 minutes. As a prerequisite, you need to first prepare an AWS IAM policy and role with this <a target='_blank' href='https://github.com/coralogix/cloudformation-coralogix-aws/tree/master/coralogix-policies/coralogix-metrics-integration-policy'>CloudFormation template</a>

For more information about this integration, [click here](https://coralogix.com/docs/aws-metrics-via-cloudwatch/).

## How to use with Terraform

To deploy this integration using Terraform, you can use the [`coralogix_integration`](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/resources/integration) resource provided by the Coralogix Terraform provider. Below is an example configuration:

```hcl
resource "coralogix_integration" "aws-metrics-collector" {
  integration_key = "aws-metrics-collector"
  version         = "0.5.0"

  parameters = {
    IntegrationName = "cx-aws-cw-metrics"
    AwsRoleArn = "arn:aws:iam::012345678901:role/access-cloudwatch-metrics"
    AwsRegion = "eu-west-1"
    ApplicationName = "<ApplicationName>"
    SubsystemName = "<SubsystemName>"
    MetricNamespaces = [ "value1", "value2" ]
    WithAggregations = false
    EnrichWithTags = true
  }
}
```

Replace the placeholder values with your actual configuration parameters. Refer to the parameter table below for required fields and their descriptions.
## Revisions

???+ note "0.5.0"

    | Name | Type | Required | Description |
    |------|------|----------|-------------|
    | ApplicationName | String | Yes | The application name that the integration will be used with. It is added as label `cx_application_name` to each metric. |
    | AwsRegion | String | Yes | Aws Region from which you want to pull the metrics |
    | AwsRoleArn | String | Yes | The ARN of the role that trusts the Coralogix AWS account. |
    | EnrichWithTags | Boolean | Yes | Add resource tags as labels to the metrics on that resource. |
    | IntegrationName | String | Yes |  |
    | MetricNamespaces | List | Yes | Specify the Metric Namespaces of the metrics that you want to pull into Coralogix or leave empty to pull all the metrics. |
    | SubsystemName | String | Yes | The subsystem name that the integration will be used with. It is added as label `cx_subsystem_name` to each metric. |
    | WithAggregations | Boolean | Yes | Add convenience only metrics with suffixes `_avg`, `_min` and `_max`, this will result in increased data usage, where you can achieve same data with more complex queries. |
    | CustomMetricNamespaces | String | No | Specify the Metric Namespaces of the metrics that you want to pull into Coralogix. |
    | EcsEnhancedMonitoringEnabled | Boolean | No | Enable ECS Enhanced Monitoring, this will result in increased data usage as it produces more metrics and requires permissions for `ecs::Describe*` and `ecs::List*`. It will also bring in ECS services and container_instances tags. All the specific permissions without * can be found in changelog. |
    | ElastiCacheEnhancedMonitoringEnabled | Boolean | No | Enable ElastiCache Enhanced Monitoring, this will result in increased data usage as it produces more metrics and requires permissions for `elasticache:DescribeCacheClusters`, `elasticache:ListTagsForResource` and `ec2:DescribeInstanceTypes`. |
    | ExtendedStatisticsToCollect | TextArea | No | Enter a JSON-formatted string containing additional statistics, they will be collected only for metrics matching selected Metric Namespaces. If you do not want any additional statistics put `[]` as a value. Example: `[{\"metric_selector\":{\"namespace\": \"AWS/EC2\", \"metric_names\": [\"CPUUtilization\"]}, \"extended_statistics\": [\"p99\"]}]`<br> |
    | ExternalId | String | No | Optional \"sts:ExternalId\", if you defined your role to depend on it, it has to be specified, otherwise we would not be able to get data from your AWS account. |
    | PollIntervalMinutes | Number | No | Poll interval in minutes, can be set to 10 and more. |
    | RdsEnhancedMonitoringEnabled | Boolean | No | Enable RDS Enhanced Monitoring, this will result in increased data usage as it produces more metrics and requires permissions for `rds::Describe*`, `rds::List*` and `ec2:DescribeInstanceTypes`. It will also bring in RDS provisioned and reserved instance tags. All the specific permissions without * can be found in changelog. |
    | ResourceTagsFilter | String | No | Enter a comma-separated list of tag keys or key-value pairs to use as filter. Metrics on resources not matching the filter will be skipped. Note that not all metrics are linked to resources, this filter will have no effect on them. If no filter is defined, ALL metrics will be collected. |
    | StaticLabels | List | No | List of key-value pairs that will be added as labels to every metric in the integration. |
    | TagsKeyFilter | List | No | List of tag keys. Only tags matching these keys will be added as labels to the metrics. If empty all the matching tags will be added. |

??? note "0.4.0"

    | Name | Type | Required | Description |
    |------|------|----------|-------------|
    | ApplicationName | String | Yes | The application name that the integration will be used with. It is added as label `cx_application_name` to each metric. |
    | AwsRegion | String | Yes | Aws Region from which you want to pull the metrics |
    | AwsRoleArn | String | Yes | The ARN of the role that trusts the Coralogix AWS account. |
    | EnrichWithTags | Boolean | Yes | Add resource tags as labels to the metrics on that resource. |
    | IntegrationName | String | Yes |  |
    | MetricNamespaces | List | Yes | Specify the Metric Namespaces of the metrics that you want to pull into Coralogix or leave empty to pull all the metrics. |
    | SubsystemName | String | Yes | The subsystem name that the integration will be used with. It is added as label `cx_subsystem_name` to each metric. |
    | WithAggregations | Boolean | Yes | Add convenience only metrics with suffixes `_avg`, `_min` and `_max`, this will result in increased data usage, where you can achieve same data with more complex queries. |
    | CustomMetricNamespaces | String | No | Specify the Metric Namespaces of the metrics that you want to pull into Coralogix. |
    | EcsEnhancedMonitoringEnabled | Boolean | No | Enable ECS Enhanced Monitoring, this will result in increased data usage as it produces more metrics and requires permissions for `ecs::Describe*` and `ecs::List*`. It will also bring in ECS services and container_instances tags. All the specific permissions without * can be found in changelog. |
    | ExtendedStatisticsToCollect | TextArea | No | Enter a JSON-formatted string containing additional statistics, they will be collected only for metrics matching selected Metric Namespaces. If you do not want any additional statistics put `[]` as a value. Example: `[{\"metric_selector\":{\"namespace\": \"AWS/EC2\", \"metric_names\": [\"CPUUtilization\"]}, \"extended_statistics\": [\"p99\"]}]`<br> |
    | ExternalId | String | No | Optional \"sts:ExternalId\", if you defined your role to depend on it, it has to be specified, otherwise we would not be able to get data from your AWS account. |
    | PollIntervalMinutes | Number | No | Poll interval in minutes, can be set to 10 and more. |
    | RdsEnhancedMonitoringEnabled | Boolean | No | Enable RDS Enhanced Monitoring, this will result in increased data usage as it produces more metrics and requires permissions for `rds::Describe*`, `rds::List*` and `ec2:DescribeInstanceTypes`. It will also bring in RDS provisioned and reserved instance tags. All the specific permissions without * can be found in changelog. |
    | ResourceTagsFilter | String | No | Enter a comma-separated list of tag keys or key-value pairs to use as filter. Metrics on resources not matching the filter will be skipped. Note that not all metrics are linked to resources, this filter will have no effect on them. If no filter is defined, ALL metrics will be collected. |
    | StaticLabels | List | No | List of key-value pairs that will be added as labels to every metric in the integration. |
    | TagsKeyFilter | List | No | List of tag keys. Only tags matching these keys will be added as labels to the metrics. If empty all the matching tags will be added. |

??? note "0.3.0"

    | Name | Type | Required | Description |
    |------|------|----------|-------------|
    | ApplicationName | String | Yes | The application name that the integration will be used with. It is added as label `cx_application_name` to each metric. |
    | AwsRegion | String | Yes | Aws Region from which you want to pull the metrics |
    | AwsRoleArn | String | Yes | The ARN of the role that trusts the Coralogix AWS account. |
    | EnrichWithTags | Boolean | Yes | Add resource tags as labels to the metrics on that resource. |
    | IntegrationName | String | Yes |  |
    | MetricNamespaces | List | Yes | Specify the Metric Namespaces of the metrics that you want to pull into Coralogix or leave empty to pull all the metrics. |
    | SubsystemName | String | Yes | The subsystem name that the integration will be used with. It is added as label `cx_subsystem_name` to each metric. |
    | WithAggregations | Boolean | Yes | Add convenience only metrics with suffixes `_avg`, `_min` and `_max`, this will result in increased data usage, where you can achieve same data with more complex queries. |
    | CustomMetricNamespaces | String | No | Specify the Metric Namespaces of the metrics that you want to pull into Coralogix. |
    | EcsEnhancedMonitoringEnabled | Boolean | No | Enable ECS Enhanced Monitoring, this will result in increased data usage as it produces more metrics and requires permissions for `ecs::Describe*` and `ecs::List*`. It will also bring in ECS services and container_instances tags. All the specific permissions without * can be found in changelog. |
    | ExtendedStatisticsToCollect | TextArea | No | Enter a JSON-formatted string containing additional statistics, they will be collected only for metrics matching selected Metric Namespaces. If you do not want any additional statistics put `[]` as a value. Example: `[{\"metric_selector\":{\"namespace\": \"AWS/EC2\", \"metric_names\": [\"CPUUtilization\"]}, \"extended_statistics\": [\"p99\"]}]`<br> |
    | ExternalId | String | No | Optional \"sts:ExternalId\", if you defined your role to depend on it, it has to be specified, otherwise we would not be able to get data from your AWS account. |
    | PollIntervalMinutes | Number | No | Poll interval in minutes, can be set to 10 and more. |
    | RdsEnhancedMonitoringEnabled | Boolean | No | Enable RDS Enhanced Monitoring, this will result in increased data usage as it produces more metrics and requires permissions for `rds::Describe*`, `rds::List*` and `ec2:DescribeInstanceTypes`. It will also bring in RDS provisioned and reserved instance tags. All the specific permissions without * can be found in changelog. |
    | ResourceTagsFilter | String | No | Enter a comma-separated list of tag keys or key-value pairs to use as filter. Metrics on resources not matching the filter will be skipped. Note that not all metrics are linked to resources, this filter will have no effect on them. If no filter is defined, ALL metrics will be collected. |
    | StaticLabels | List | No | List of key-value pairs that will be added as labels to every metric in the integration. |
    | TagsKeyFilter | List | No | List of tag keys. Only tags matching these keys will be added as labels to the metrics. If empty all the matching tags will be added. |

??? note "0.2.0"

    | Name | Type | Required | Description |
    |------|------|----------|-------------|
    | ApplicationName | String | Yes | The application name that the integration will be used with. It is added as label `cx_application_name` to each metric. |
    | AwsRegion | String | Yes | Aws Region from which you want to pull the metrics |
    | AwsRoleArn | String | Yes | The ARN of the role that trusts the Coralogix AWS account. |
    | EnrichWithTags | Boolean | Yes | Add resource tags as labels to the metrics on that resource. |
    | IntegrationName | String | Yes |  |
    | MetricNamespaces | List | Yes | Specify the Metric Namespaces of the metrics that you want to pull into Coralogix or leave empty to pull all the metrics. |
    | SubsystemName | String | Yes | The subsystem name that the integration will be used with. It is added as label `cx_subsystem_name` to each metric. |
    | WithAggregations | Boolean | Yes | Add convenience only metrics with suffixes `_avg`, `_min` and `_max`, this will result in increased data usage, where you can achieve same data with more complex queries. |
    | CustomMetricNamespaces | String | No | Specify the Metric Namespaces of the metrics that you want to pull into Coralogix. |
    | EcsEnhancedMonitoringEnabled | Boolean | No | Enable ECS Enhanced Monitoring, this will result in increased data usage as it produces more metrics and requires permissions for `ecs::Describe*` and `ecs::List*`. It will also bring in ECS services and container_instances tags. All the specific permissions without * can be found in changelog. |
    | ExtendedStatisticsToCollect | TextArea | No | Enter a JSON-formatted string containing additional statistics, they will be collected only for metrics matching selected Metric Namespaces. If you do not want any additional statistics put `[]` as a value. Example: `[{\"metric_selector\":{\"namespace\": \"AWS/EC2\", \"metric_names\": [\"CPUUtilization\"]}, \"extended_statistics\": [\"p99\"]}]`<br> |
    | ExternalId | String | No | Optional \"sts:ExternalId\", if you defined your role to depend on it, it has to be specified, otherwise we would not be able to get data from your AWS account. |
    | PollIntervalMinutes | Number | No | Poll interval in minutes, can be set to 10 and more. |
    | RdsEnhancedMonitoringEnabled | Boolean | No | Enable RDS Enhanced Monitoring, this will result in increased data usage as it produces more metrics and requires permissions for `rds::Describe*`, `rds::List*` and `ec2:DescribeInstanceTypes`. It will also bring in RDS provisioned and reserved instance tags. All the specific permissions without * can be found in changelog. |
    | ResourceTagsFilter | String | No | Enter a comma-separated list of tag keys or key-value pairs to use as filter. Metrics on resources not matching the filter will be skipped. Note that not all metrics are linked to resources, this filter will have no effect on them. If no filter is defined, ALL metrics will be collected. |
    | StaticLabels | List | No | List of key-value pairs that will be added as labels to every metric in the integration. |
    | TagsKeyFilter | List | No | List of tag keys. Only tags matching these keys will be added as labels to the metrics. If empty all the matching tags will be added. |

??? note "0.1.0"

    | Name | Type | Required | Description |
    |------|------|----------|-------------|
    | ApplicationName | String | Yes | The application name that the integration will be used with. It is added as label `cx_application_name` to each metric. |
    | AwsRegion | String | Yes | Aws Region from which you want to pull the metrics |
    | AwsRoleArn | String | Yes | The ARN of the role that trusts the Coralogix AWS account. |
    | EnrichWithTags | Boolean | Yes | Add resource tags as labels to the metrics on that resource. |
    | IntegrationName | String | Yes |  |
    | MetricNamespaces | List | Yes | Specify the Metric Namespaces of the metrics that you want to pull into Coralogix or leave empty to pull all the metrics. |
    | SubsystemName | String | Yes | The subsystem name that the integration will be used with. It is added as label `cx_subsystem_name` to each metric. |
    | WithAggregations | Boolean | Yes | Add convenience only metrics with suffixes `_avg`, `_min` and `_max`, this will result in increased data usage, where you can achieve same data with more complex queries. |
    | CustomMetricNamespaces | String | No | Specify the Metric Namespaces of the metrics that you want to pull into Coralogix. |
    | ExtendedStatisticsToCollect | TextArea | No | Enter a JSON-formatted string containing additional statistics, they will be collected only for metrics matching selected Metric Namespaces. If you do not want any additional statistics put `[]` as a value. Example: `[{\"metric_selector\":{\"namespace\": \"AWS/EC2\", \"metric_names\": [\"CPUUtilization\"]}, \"extended_statistics\": [\"p99\"]}]`<br> |
    | ExternalId | String | No | Optional \"sts:ExternalId\", if you defined your role to depend on it, it has to be specified, otherwise we would not be able to get data from your AWS account. |
    | PollIntervalMinutes | Number | No | Poll interval in minutes, can be set to 10 and more. |
    | ResourceTagsFilter | String | No | Enter a comma-separated list of tag keys or key-value pairs to use as filter. Metrics on resources not matching the filter will be skipped. Note that not all metrics are linked to resources, this filter will have no effect on them. If no filter is defined, ALL metrics will be collected. |
    | StaticLabels | List | No | List of key-value pairs that will be added as labels to every metric in the integration. |
    | TagsKeyFilter | List | No | List of tag keys. Only tags matching these keys will be added as labels to the metrics. If empty all the matching tags will be added. |

