# GCP Logs

## Overview

Google Cloud Platform provides built-in monitoring and observability tools that allow users to collect and analyze logs, metrics, and traces from their GCP resources. Send Google Cloud <a target='_blank' href='https://cloud.google.com/logging/docs'>logs</a> seamlessly to Coralogix. Search, analyze, and visualize your data, gaining insights into application behavior, identifying errors, and troubleshooting problems.

For more information about this integration, [click here](https://coralogix.com/docs/gcp-logs/).

## How to use with Terraform

To deploy this integration using Terraform, you can use the [`coralogix_integration`](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/resources/integration) resource provided by the Coralogix Terraform provider. Below is an example configuration:

```hcl
resource "coralogix_integration" "gcp-logs-collector" {
  integration_key = "gcp-logs-collector"
  version         = "0.1.0"

  parameters = {
    IntegrationName = "<IntegrationName>"
    SubscriptionName = "<SubscriptionName>"
    ApplicationNameTemplates = [ "value1", "value2" ]
    SubsystemNameTemplates = [ "value1", "value2" ]
    ServiceAccountKey = "<ServiceAccountKey>"
  }
}
```

Replace the placeholder values with your actual configuration parameters. Refer to the parameter table below for required fields and their descriptions.
## Revisions

???+ note "0.1.0"

    | Name | Type | Required | Description |
    |------|------|----------|-------------|
    | ApplicationNameTemplates | List | Yes | ApplicationName templates, supported values are `const`, `${variable}` and their combinations. Supported values for variable are `projectId`, `organizationId`, `billingAccountId`, `folderId`, `logId` and `resourceType`. First template that has all variables available will be used as a logs application name. At least one `const` template is required. |
    | IntegrationName | String | Yes |  |
    | ServiceAccountKey | String | Yes |  |
    | SubscriptionName | String | Yes | The name of the GCP Pub/Sub subscription to pull logs from. |
    | SubsystemNameTemplates | List | Yes | List of SubsystemName templates, supported values are `const`, `${variable}` and their combinations. Supported values for variable are `projectId`, `organizationId`, `billingAccountId`, `folderId`, `logId` and `resourceType`. First template that has all variables available will be used as a logs subsystem name. At least one `const` template is required. |

