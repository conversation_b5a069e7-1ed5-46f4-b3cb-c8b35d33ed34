---
title: "Coralogix Terraform Provider"
date: "2018-03-02"
coverImage: "<EMAIL>"
---

The [Coralogix terraform provider](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs#example-usage) is used to interact with the resources supported by Coralogix. The provider needs to be configured with the proper credentials before it can be used. It **requires** terraform v1.3.0 or later.

View the [latest version](https://registry.terraform.io/providers/coralogix/coralogix/latest) of our comprehensive [usage guide in the terraform registry](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs) to learn more and view examples of how to use the terraform provider to manage Coralogix resources and APIs.

You can also view our open source code in its official GitHub [repository](https://github.com/coralogix/terraform-provider-coralogix/tree/master/examples).

## Prerequisites

- [Sign up](https://signup.coralogix.com/#/) for a Coralogix account. Set up your account on the Coralogix [domain](../../../../user-guides/account-management/account-settings/coralogix-domain/index.md) corresponding to the region within which you would like your data stored.

- To use this API you need to [create](../../../../user-guides/account-management/api-keys/api-keys/index.md#overview) a personal or team API key. It’s recommended to use permission presets, as they are automatically updated with all relevant permissions. Alternatively, you can manually add individual permissions. Assign permissions to the API key based on the specific Terraform functionality you intend to use.

- [Install](https://developer.hashicorp.com/terraform/tutorials/aws-get-started/install-cli) Terraform.

## Configuration

### **Example usage**

``` tf
terraform {
  required_providers {
    coralogix = {
      version = "<your desired version>"
      source  = "coralogix/coralogix"
    }
  }
}
provider "coralogix" {
  api_key = "<add your api key here or add a CORALOGIX_API_KEY env variable>"
  env = "<add the environment where you want to work or add a CORALOGIX_ENV env variable>"
}

resource "coralogix_rules_group" "my_first_rules_group" {
  name = "my first rules_group"
}
```

### Authentication

The API key and desired environment for using the Coralogix provider can be set in two ways, either explicitly **or** implicitly through environment variables.

- explicitly:

``` tf
provider "coralogix" {
api_key = "<cx_api_key>"
env = "<add the environment where you want to work>"
}

```

- implicitly:

``` bash
export CORALOGIX_API_KEY="<cx_api_key>"

```

``` bash
export CORALOGIX_ENV="<add the environment where you want to work>"

```

### Argument reference

- `api_key` (string, sensitive). An API key appropriate for the defined environment and functionality.

- `env` (string). The Coralogix API environment should be the region (EU1, EU2, US1, US2, AP1, AP2, AP3) associated with your Coralogix [domain](../../../../user-guides/account-management/account-settings/coralogix-domain/index.md).

## Resources

Set up parsing rules and groups in your account as follows:

[coralogix_action](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/resources/action)

[coralogix_alert](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/resources/alert)

[coralogix_dashboard](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/resources/dashboard)

[coralogix_data_set](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/resources/data_set)

[coralogix_enrichment](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/resources/enrichment)

[coralogix_hosted_dashboard](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/resources/hosted_dashboard)

[coralogix_events2metric](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/resources/events2metric)` (previously coralogix_logs2metric)

[coralogix_recording_rules_group](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/resources/recording_rules_group)

[coralogix_rules_group](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/resources/rules_group)`

[coralogix_slo](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/resources/slo)

[coralogix_tco_policy_logs](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/resources/tco_policy_logs)

[coralogix_tco_policy_override](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/resources/tco_policy_override)

[coralogix_tco_policy_traces](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/resources/tco_policy_traces)

[coralogix_webhook](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/resources/webhook)

## Data sources

Set up alerts in your account as follows:

[coralogix_action](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/data-sources/action)

[coralogix_alert](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/data-sources/alert)

[coralogix_dashboard](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/data-sources/dashboard)

[coralogix_data_set](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/data-sources/data_set)

[coralogix_enrichment](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/data-sources/enrichment)

[coralogix_hosted_dashboard](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/data-sources/hosted_dashboard)

[coralogix_events2metric](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/data-sources/events2metric) (previously coralogix_logs2metric)

[coralogix_recording_rules_group](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/data-sources/recording_rules_group)

[coralogix_rules_group](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/data-sources/rules_group)

[coralogix_slo](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/data-sources/slo)

[coralogix_tco_policy_logs](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/data-sources/tco_policy_logs)

[coralogix_tco_policy_override](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/data-sources/tco_policy_override)

[coralogix_tco_policy_traces](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/data-sources/tco_policy_traces)

[coralogix_webhook](https://registry.terraform.io/providers/coralogix/coralogix/latest/docs/data-sources/webhook)

## Report issues

We welcome your contribution to our open source code! Share your ideas and report any bugs [here](https://github.com/coralogix/terraform-provider-coralogix/issues).

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
