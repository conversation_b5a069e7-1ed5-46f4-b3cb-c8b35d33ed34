---
title: "GitLab Version Tags"
date: "2021-06-10"
coverImage: "GitLab-Version-Tags-1000X1000.png"
description: "Coralogix supports integration with GitLab webhooks, use webhooks to inform Coralogix when a new build is issued."
---

This tutorial demonstrates how to integrate Coralogix with your [GitLab](../../../../integrations/contextual-data/gitlab-data-ingestion/index.md) deployment pipelines.

## Configuration

**Use GitLab webhooks to inform Coralogix when a new build is issued**.

**STEP 1**. Log in to GitLab using your credentials and navigate to your project page.

**STEP 2.** Click **Settings** > Webhooks.

**STEP 3**. Add the following URL:

https://ng-api-http.**DOMAIN**/api/v1/external/gitlab?application=**APPLICATION_NAME**&subsystem=**SUBSYSTEM_NAME**&name=**TAG_NAME**

Insert the following parameters:

**DOMAIN**: Coralogix [domain](../../../../user-guides/account-management/account-settings/coralogix-domain/index.md) associated with your account

**APPLICATION_NAME**: [Application name](../../../../user-guides/account-management/account-settings/application-and-subsystem-names/index.md) as it appears in your Coralogix UI

**SUBSYSTEM_NAME**: [Subsystem name](../../../../user-guides/account-management/account-settings/application-and-subsystem-names/index.md) as it appears in your Coralogix UI. Add one or more subsystem names separated by a comma.

**TAG_NAME** - Tag name. If this parameter is not inserted, values are taken from GitLab payload fields _project.name_ and _object_attributes.ref_, as explained [here](https://docs.gitlab.com/ee/user/project/integrations/webhook_events.html#pipeline-events).

**STEP 4**. To use this API you need to [create](../../../../user-guides/account-management/api-keys/api-keys/index.md) a personal or team API key. It’s recommended to use permission presets, as they are automatically updated with all relevant permissions. Alternatively, you can manually add individual permissions.

| Preset | Action | Description |
| --- | --- | --- |
| CICDIntegration | `VERSION-BENCHMARK-TAGS:READ` <br> `VERSION-BENCHMARKS-REPORTS:READ` <br> `VERSION-BENCHMARK-TAGS:UPDATE` | View Version Benchmark Tags <br> View Version Benchmark Reports <br> Modify Version Benchmark Tags | 


**Notes**:

- The **Secret Token** is obligatory.

- Input the **Secret Token** in the Secret Token field below the URL in GitLab.

**STEP 5**. Select **Pipeline events** as a trigger and click save changes.

![coralogix integration to Gitlab](images/**********************-at-22.07.28-1024x825.png)

## Additional Resources
| | |
| --- | --- |
| Documentation | [GitLab Data Ingestion](../../../../integrations/contextual-data/gitlab-data-ingestion/index.md) |

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email at [<EMAIL>](mailto:<EMAIL>).
