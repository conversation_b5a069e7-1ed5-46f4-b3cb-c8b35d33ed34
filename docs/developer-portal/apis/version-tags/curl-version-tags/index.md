---
title: "cURL Version Tags"
date: "2021-06-10"
coverImage: "Version-Tags-with-cURL-1000X1000.png"
description: "You can add version tags in Coralogix per Application and Subsystem using cURL"
show_blocks:
  - domain_selector
---

Set up cURL Version Tags with Coralogix.

## Setup

You can add version tags per application and subsystem using cURL:

**URL:** Please use the [API endpoint](../../../../integrations/coralogix-endpoints.md#curl-version-tags) associated with your Coralogix [domain](../../../../user-guides/account-management/account-settings/coralogix-domain/index.md).

**API Key:** To use this API you need to [create](../../../../user-guides/account-management/api-keys/api-keys/index.md) a personal or team API key. It’s recommended to use permission presets, as they are automatically updated with all relevant permissions. Alternatively, you can manually add individual permissions.

  | Preset | Action | Description |
  | --- | --- | --- |
  | CICDIntegration | `VERSION-BENCHMARK-TAGS:READ` <br> `VERSION-BENCHMARKS-REPORTS:READ` <br> `VERSION-BENCHMARK-TAGS:UPDATE` | View Version Benchmark Tags <br> View Version Benchmark Reports <br> Modify Version Benchmark Tags | 

**Method:** POST

**Body Schema:**

```json
{"iconUrl": string,"name": string,"timestamp": date type (JavaScript date string or epoch milliseconds)"application": string[]"subsystem": string[]}
```

**Application:** Your Application name.

**Subsystem:** Your Subsystem(s) name(s). You can input more than one subsystem name, use comma delimiter ‘,’ between Subsystem names.

**Name:** Your Version Tag name.

**Timestamp:** Tag Timestamp. (This is **OPTIONAL**: If a timestamp is not supplied, the current timestamp will be applied).

**IconUrl:** Tag's Picture. (This is **OPTIONAL**: Use an URL _(URL encoded)_ to a valid image file uploaded to a public repository)  
  
**Supported Image Formats:** png, jpeg, SVG.  
  
**Maximum File Size:** 50 Kilobytes.  
  
**Note:** If the URL to the image contains spaces, please use _%20_.  
  
For example:

https://www.myimagesite.com/Path%20To%20The%20Image.png.

**Example (GET):**

``` bash
curl --location --request GET '{{ endpoints.curl_version_tags }}/add?key=&application=Application_Name,Application_Name2&subsystem=Subsystem1,Subsystem2,Subsystem3&name=MyFirstTag&timestamp=2020-06-23:09:00:00&iconUrl=your_icon_url'
```

**Example (POST):**

``` bash
curl --location --request POST '{{ endpoints.curl_version_tags }}' \
--header 'Authorization: Bearer <cx_api_key>' \
--header 'Content-Type: application/json' \
--data-raw '{
"timestamp": 1617793927675,
"name": "test tag",
"application": ["prd", "dev"],
"subsystem": ["app", "mobile"],
"iconUrl": "my-avatar.png"
}'
```

**Note:** application and subsystem should always be contained between square brackets, even if it is an array of one element, for example:  
  
`"application": ["prd"]`,  
`"subsystem": ["app"]`  

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email to [<EMAIL>](mailto:<EMAIL>).
