---
title: "Microsoft Azure DevOps Server Version Tags"
date: "2021-06-10"
coverImage: "Azure-DevOps-Server-Version-Tags-1000X1000.png"
description: "Coralogix supports integration with Azure DevOps Server webhooks, use webhooks to inform Coralogix when a new build is issued."
---

### Integrate Coralogix with your Azure DevOps Server deployment pipelines

Coralogix supports integration with `Azure DevOps Server` webhooks, use webhooks to inform Coralogix when a new build is issued.

**STEP 1**. Sign in to Azure DevOps Server using your user credentials.

**STEP 2**. Select your team project.

**STEP 3**. Click **Settings**.

**STEP 4**. Navigate to **Service hooks**.

**STEP 5**. Click **Add new service hooks**.

**STEP 6**. Select **Web Hooks**.

**STEP 7**. Select **Build Completed** as the trigger.

**STEP 8**. Select Build Status **Succeeded** and click **Next.**

**STEP 9**. Finalize your setup.

- Choose any filter as you desire.

- Add the [version tag endpoint](../../../../integrations/coralogix-endpoints.md#microsoft-azure-devops-server-version-tags) associated with your Coralogix [domain](../../../../user-guides/account-management/account-settings/coralogix-domain/index.md).
    - `application` (required): This must be identical to your logs application name.
    
    - `subsystem`: This must be identical to your logs subsystem name. You may add more than one subsystem name separated by a comma.

- To use this API you need to [create](../../../../user-guides/account-management/api-keys/api-keys/index.md) a personal or team API key. It’s recommended to use permission presets, as they are automatically updated with all relevant permissions. Alternatively, you can manually add individual permissions.

    | Preset | Action | Description |
    | --- | --- | --- |
    | CICDIntegration | `VERSION-BENCHMARK-TAGS:READ` <br> `VERSION-BENCHMARKS-REPORTS:READ` <br> `VERSION-BENCHMARK-TAGS:UPDATE` | View Version Benchmark Tags <br> View Version Benchmark Reports <br> Modify Version Benchmark Tags | 

    Include headers with the following properties using your API key:  
    Authorization: Bearer `<cx_api_key>`

- Click **Finish**.

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by sending us an email to [<EMAIL>](mailto:<EMAIL>).
