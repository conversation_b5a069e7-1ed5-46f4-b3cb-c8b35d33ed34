---
title: "Alerts API v3"
date: "2024-09-03"
description: "Use our Alerts API v3 to define, query, and manage Coralogix Alerts."
show_blocks:
  - domain_selector
---

# Alerts API v3

Use our **Alerts API v3** to define, query, and manage [Coralogix Alerts](../../../../../user-guides/alerting/introduction-to-alerts/index.md). View [this tutorial](../alerts-api-v1-v2/index.md) for **v1/v2**.

## Overview

This tutorial outlines Coralogix's v3 Alerts gRPC API. It is designed to be simpler and more user-friendly. Using protobuf, it supports clients with autocomplete features, such as Kreya's gRPC.

## Authentication

### Authenticate requests

To authenticate requests, create a [customized Team API key](../../../../../user-guides/account-management/api-keys/api-keys/index.md) with the **Alerts** role preset assigned to it.  Alternatively, selectively add any of the following permissions to your key:

| **Method** | **Permission** |
| --- | --- |
| ListAlertDefs | alerts:ReadConfig OR logs.alerts:ReadConfig OR metrics.alerts:ReadConfig OR spans.alerts:ReadConfig |
| GetAlertDef | alerts:ReadConfig OR logs.alerts:ReadConfig OR metrics.alerts:ReadConfig OR spans.alerts:ReadConfig |
| CreateAlertDef | alerts:UpdateConfig OR logs.alerts:UpdateConfig OR metrics.alerts:UpdateConfig OR spans.alerts:UpdateConfig |
| ReplaceAlertDef | alerts:UpdateConfig OR logs.alerts:UpdateConfig OR metrics.alerts:UpdateConfig OR spans.alerts:UpdateConfig |
| DeleteAlertDef | alerts:UpdateConfig OR logs.alerts:UpdateConfig OR metrics.alerts:UpdateConfig OR spans.alerts:UpdateConfig |
| SetActive | alerts:UpdateConfig OR logs.alerts:UpdateConfig OR metrics.alerts:UpdateConfig OR spans.alerts:UpdateConfig |

### Example

Use your customized API key in the Authorization request header to successfully connect.

```
grpcurl -H "Authorization: Bearer API_KEY_HERE"
```

Then, use one of our designated [Management endpoints](../../../../../integrations/coralogix-endpoints.md#management) to structure your header.  

Select the endpoint associated with your Coralogix [domain](../../../../../user-guides/account-management/account-settings/coralogix-domain/index.md).

```
-d @ ng-api-grpc.coralogix.com:443
```

For the AlertDefs Service API, the service name is `AlertDefsService`.

```
com.coralogixapis.alerts.v3.AlertDefsService
```

The complete request header should look like this:

```
grpcurl -H "Authorization: Bearer API_KEY_HERE" -d @ ng-api-grpc.coralogix.com:443 com.coralogixapis.alerts.v3.AlertDefsService/
```

## API functions

Find details on API functions [here](https://github.com/coralogix/cx-api-alerts/blob/master/docs/alert-api-v3.md#alertdefsservice).

## Sample requests

The following section presents an array of sample requests.

## Create a Standard Alert with immediate notifications

Create a [Standard Alert](../../../../../user-guides/alerting/create-an-alert/logs/threshold-alerts/index.md) with the "notify immediately" condition.

### Request

```json
grpcurl -H "Authorization: Bearer API_KEY_HERE" -d @ ng-api-grpc.coralogix.com:443 com.coralogixapis.alerts.v3.AlertDefsService/CreateAlertDef <<EOF
{
    "alert_def_properties": {
        "name": "logs-immediate",
        "description":"Example of logs-immediate alert",
        "enabled":  true,
        "priority": "ALERT_DEF_PRIORITY_P3",
        "type": "ALERT_DEF_TYPE_LOGS_IMMEDIATE_OR_UNSPECIFIED",
        "entity_labels": {
            "label_key":"label_value"
        },
        "incidents_settings": {
            "notify_on": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
            "minutes": 10
        },
        "notification_group": {
            "group_by_keys": [],
            "webhooks": [{
               "minutes":10,
               "notify_on":"NOTIFY_ON_TRIGGERED_AND_RESOLVED",
                "integration": {
                    "recipients": {"emails": ["<EMAIL>"]}
                }
            }]
        },
        "logs_immediate": {
            "logs_filter": {
                "simple_filter": {
                    "label_filters": {
                        "application_name": [
                            {
                                "operation": "LOG_FILTER_OPERATION_TYPE_ENDS_WITH",
                                "value": "endsWithThis"
                            }
                        ],
                        "severities": [
                            "LOG_SEVERITY_ERROR",
                            "LOG_SEVERITY_CRITICAL"
                        ]
                    },
                    "lucene_query": "QueryThisLog"
                }
            }
        }
    }
}
EOF
```

### Response

```json
{
  "alertDef": {
    "alertDefProperties": {
      "name": "logs-immediate",
      "description": "Example of logs-immediate alert",
      "enabled": true,
      "priority": "ALERT_DEF_PRIORITY_P3",
      "incidentsSettings": {
        "notifyOn": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
        "minutes": 10
      },
      "notificationGroup": {
        "webhooks": [
          {
            "notifyOn": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
            "integration": {
              "recipients": {
                "emails": [
                  "<EMAIL>"
                ]
              }
            },
            "minutes": 10
          }
        ]
      },
      "entityLabels": {
        "label_key": "label_value"
      },
      "phantomMode": false,
      "deleted": false,
      "logsImmediate": {
        "logsFilter": {
          "simpleFilter": {
            "luceneQuery": "QueryThisLog",
            "labelFilters": {
              "applicationName": [
                {
                  "value": "endsWithThis",
                  "operation": "LOG_FILTER_OPERATION_TYPE_ENDS_WITH"
                }
              ],
              "severities": [
                "LOG_SEVERITY_ERROR",
                "LOG_SEVERITY_CRITICAL"
              ]
            }
          }
        }
      }
    },
    "id": "737d508f-8961-45bc-941c-356aa112e9bc",
    "createdTime": "2024-11-06T08:02:20Z",
    "updatedTime": "2024-11-06T08:02:20Z",
    "alertVersionId": "737d508f-8961-45bc-941c-356aa112e9bc"
  }
}
```

## Create a Standard Alert with a threshold

Create a [Standard Alert](../../../../../user-guides/alerting/create-an-alert/logs/threshold-alerts/index.md) with the "more than" or "less than" condition.

### Request

```json
grpcurl -H "Authorization: Bearer API_KEY_HERE" -d @ ng-api-grpc.coralogix.com:443 com.coralogixapis.alerts.v3.AlertDefsService/CreateAlertDef <<EOF
{
    "alert_def_properties": {
        "name": "logs threshold",
        "description": "example of logs threshold",
        "priority": "ALERT_DEF_PRIORITY_P2",
        "enabled":  true,
        "incidents_settings": {
            "notify_on": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
            "minutes": 10
        },
        "entity_labels": {
            "key": "value"
        },
        "type": "ALERT_DEF_TYPE_LOGS_THRESHOLD",
        "logs_threshold": {
            "logs_filter": {
                "simple_filter": {
                    "lucene_query": "_exists_:field",
                    "label_filters": {
                        "application_name": [],
                        "severities": [],
                        "subsystem_name": []
                    }
                }
            },
            "notification_payload_filter": [],
            "rules": [
                {
                    "condition": {
                        "condition_type": "LOGS_THRESHOLD_CONDITION_TYPE_MORE_THAN_OR_UNSPECIFIED",
                        "threshold": 1000,
                        "time_window": {
                            "logs_time_window_specific_value": "LOGS_TIME_WINDOW_VALUE_HOURS_2"
                        }
                    },
                    "override": {
                        "priority": "ALERT_DEF_PRIORITY_P2"
                    }
                }
            ]
        }
    }
}
EOF
```

### Response

```json
{
  "alertDef": {
    "alertDefProperties": {
      "name": "logs threshold",
      "description": "example of logs threshold",
      "enabled": true,
      "priority": "ALERT_DEF_PRIORITY_P2",
      "type": "ALERT_DEF_TYPE_LOGS_THRESHOLD",
      "incidentsSettings": {
        "notifyOn": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
        "minutes": 10
      },
      "notificationGroup": {
      },
      "entityLabels": {
        "key": "value"
      },
      "phantomMode": false,
      "deleted": false,
      "logsThreshold": {
        "logsFilter": {
          "simpleFilter": {
            "luceneQuery": "_exists_:field",
            "labelFilters": {
            }
          }
        },
        "rules": [
          {
            "condition": {
              "threshold": 1000,
              "timeWindow": {
                "logsTimeWindowSpecificValue": "LOGS_TIME_WINDOW_VALUE_HOURS_2"
              }
            },
            "override": {
              "priority": "ALERT_DEF_PRIORITY_P2"
            }
          }
        ]
      }
    },
    "id": "cb7e34ab-26a5-426e-b069-272eebc7b2bf",
    "createdTime": "2024-11-06T08:15:01Z",
    "updatedTime": "2024-11-06T08:15:01Z",
    "alertVersionId": "cb7e34ab-26a5-426e-b069-272eebc7b2bf"
  }
}
```

## Create a Ratio Alert

Create a [Ratio Alert](../../../../../user-guides/alerting/create-an-alert/logs/ratio-alerts/index.md).

### Request

```json
grpcurl -H "Authorization: Bearer API_KEY_HERE" -d @ ng-api-grpc.coralogix.com:443 com.coralogixapis.alerts.v3.AlertDefsService/CreateAlertDef <<EOF
{
    "alert_def_properties": {
        "name": "logs ratio threshold",
        "description": "example of logs ratio threshold",
        "priority": "ALERT_DEF_PRIORITY_P2",
        "enabled": true,
        "incidents_settings": {
            "notify_on": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
            "minutes":  10
        },
        "group_by_keys": ["application_name"],
        "type": "ALERT_DEF_TYPE_LOGS_RATIO_THRESHOLD",
        "logs_ratio_threshold": {
            "numerator": {
                "simple_filter": {
                    "label_filters": {"application_name": [],"severities": [],"subsystem_name": []},
                    "lucene_query":  "_exists_:field"
                }
            },
            "denominator": {
                "simple_filter": {
                    "label_filters": {"application_name": [],"severities": [],"subsystem_name": []},
                    "lucene_query": "_exists_:field2"
                }
            },
            "numerator_alias":"First Query",
            "denominator_alias":"Second Query",
            "notification_payload_filter": [],
            "ignore_infinity": false,
            "group_by_for": "LOGS_RATIO_GROUP_BY_FOR_NUMERATOR_ONLY",
            "rules": [
                {
                    "condition": {
                        "condition_type": "LOGS_RATIO_CONDITION_TYPE_MORE_THAN_OR_UNSPECIFIED",
                        "threshold": 1000,
                        "time_window": {
                            "logs_ratio_time_window_specific_value": "LOGS_RATIO_TIME_WINDOW_VALUE_HOURS_2"
                        }
                    },
                    "override": {
                        "priority": "ALERT_DEF_PRIORITY_P2"
                    }
                }
            ]
        }
    }
}
EOF
```

### Response

```json
{
  "alertDef": {
    "alertDefProperties": {
      "name": "logs ratio threshold",
      "description": "example of logs ratio threshold",
      "enabled": true,
      "priority": "ALERT_DEF_PRIORITY_P2",
      "type": "ALERT_DEF_TYPE_LOGS_RATIO_THRESHOLD",
      "groupByKeys": [
        "application_name"
      ],
      "incidentsSettings": {
        "notifyOn": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
        "minutes": 10
      },
      "notificationGroup": {

      },
      "phantomMode": false,
      "deleted": false,
      "logsRatioThreshold": {
        "numerator": {
          "simpleFilter": {
            "luceneQuery": "_exists_:field",
            "labelFilters": {

            }
          }
        },
        "numeratorAlias": "First Query",
        "denominator": {
          "simpleFilter": {
            "luceneQuery": "_exists_:field2",
            "labelFilters": {

            }
          }
        },
        "denominatorAlias": "Second Query",
        "rules": [
          {
            "condition": {
              "threshold": 1000,
              "timeWindow": {
                "logsRatioTimeWindowSpecificValue": "LOGS_RATIO_TIME_WINDOW_VALUE_HOURS_2"
              }
            },
            "override": {
              "priority": "ALERT_DEF_PRIORITY_P2"
            }
          }
        ],
        "groupByFor": "LOGS_RATIO_GROUP_BY_FOR_NUMERATOR_ONLY",
        "ignoreInfinity": false
      }
    },
    "id": "071aeed5-66cd-4218-874d-41eae5ef4140",
    "createdTime": "2024-11-06T08:20:52Z",
    "updatedTime": "2024-11-06T08:20:52Z",
    "alertVersionId": "071aeed5-66cd-4218-874d-41eae5ef4140"
  }
}
```

## Create a Time Relative Alert

Create a [Time Relative Alert](../../../../../user-guides/alerting/create-an-alert/logs/time-relative-alerts/index.md).

### Request

```json
grpcurl -H "Authorization: Bearer API_KEY_HERE" -d @ ng-api-grpc.coralogix.com:443 com.coralogixapis.alerts.v3.AlertDefsService/CreateAlertDef <<EOF
{
    "alert_def_properties": {
        "name": "logs time relative threshold",
        "description": "example of logs time relative threshold",
        "priority": "ALERT_DEF_PRIORITY_P2",
        "enabled": true,
        "incidents_settings": {
            "notify_on": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
            "minutes":  10
        },
        "type": "ALERT_DEF_TYPE_LOGS_TIME_RELATIVE_THRESHOLD",
        "logs_time_relative_threshold": {
            "logs_filter": {
                "simple_filter": {
                    "label_filters": {"application_name": [],"severities": [],"subsystem_name": []},
                    "lucene_query": "_exists_:field"
                }
            },
            "notification_payload_filter": [],
            "ignore_infinity": true,
            "rules": [
                {
                    "condition": {
                        "condition_type": "LOGS_TIME_RELATIVE_CONDITION_TYPE_LESS_THAN",
                       "compared_to": "LOGS_TIME_RELATIVE_COMPARED_TO_SAME_DAY_LAST_WEEK",
                       "threshold": 5
                    },
                    "override": {
                        "priority": "ALERT_DEF_PRIORITY_P2"
                    }
                }
            ]
        }
    }
}
EOF
```

### Response

```json
{
  "alertDef": {
    "alertDefProperties": {
      "name": "logs time relative threshold",
      "description": "example of logs time relative threshold",
      "enabled": true,
      "priority": "ALERT_DEF_PRIORITY_P2",
      "type": "ALERT_DEF_TYPE_LOGS_TIME_RELATIVE_THRESHOLD",
      "incidentsSettings": {
        "notifyOn": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
        "minutes": 10
      },
      "notificationGroup": {

      },
      "phantomMode": false,
      "deleted": false,
      "logsTimeRelativeThreshold": {
        "logsFilter": {
          "simpleFilter": {
            "luceneQuery": "_exists_:field",
            "labelFilters": {

            }
          }
        },
        "rules": [
          {
            "condition": {
              "threshold": 5,
              "comparedTo": "LOGS_TIME_RELATIVE_COMPARED_TO_SAME_DAY_LAST_WEEK",
              "conditionType": "LOGS_TIME_RELATIVE_CONDITION_TYPE_LESS_THAN"
            },
            "override": {
              "priority": "ALERT_DEF_PRIORITY_P2"
            }
          }
        ],
        "ignoreInfinity": true,
        "undetectedValuesManagement": {
          "triggerUndetectedValues": false,
          "autoRetireTimeframe": "AUTO_RETIRE_TIMEFRAME_NEVER_OR_UNSPECIFIED"
        }
      }
    },
    "id": "a5de559e-b940-4283-8ad6-2ddc77701049",
    "createdTime": "2024-11-06T08:22:52Z",
    "updatedTime": "2024-11-06T08:22:52Z",
    "alertVersionId": "a5de559e-b940-4283-8ad6-2ddc77701049"
  }
}
```

## Create a log-based Anomaly Alert

Create a log-based Anomaly [Alert](../../../../../user-guides/alerting/create-an-alert/logs/anomaly-detection-alerts/index.md) to measure [more-than-usual anomalies](../../../../../user-guides/alerting/create-an-alert/logs/anomaly-detection-alerts/index.md).

### Request

```json
grpcurl -H "Authorization: Bearer API_KEY_HERE" -d @ ng-api-grpc.coralogix.com:443 com.coralogixapis.alerts.v3.AlertDefsService/CreateAlertDef <<EOF
{
    "alert_def_properties": {
        "name": "logs-anomaly-alert",
        "description": "Example of logs-anomaly alert",
        "enabled": true,
        "priority": "ALERT_DEF_PRIORITY_P3",
        "type": "ALERT_DEF_TYPE_LOGS_ANOMALY",
        "incidents_settings": {
            "notify_on": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
            "minutes":  60
        },
        "notification_group": {
            "group_by_keys": [],
            "webhooks": [
                {
                    "notify_on": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
                    "integration": {
                        "recipients": {"emails": ["<EMAIL>"]}
                    },
                    "minutes": 60
                }
            ]
        },
        "logs_anomaly": {
            "rules": [
                {
                    "condition": {
                        "minimum_threshold":  1000,
                        "time_window": {
                            "logs_time_window_specific_value": "LOGS_TIME_WINDOW_VALUE_HOURS_24"
                        },
                        "condition_type": "LOGS_ANOMALY_CONDITION_TYPE_MORE_THAN_USUAL_OR_UNSPECIFIED"
                    }
                }
            ],
            "logs_filter": {
                "simple_filter": {
                    "label_filters": {
                        "subsystem_name": [
                            {
                                "operation": "LOG_FILTER_OPERATION_TYPE_INCLUDES",
                                "value": "includes"
                            }
                        ]
                    },
                    "lucene_query": "QueryThisLog && This Log"
                }
            }
        }
    }
}
EOF
```

### Response

```json
{
  "alertDef": {
    "alertDefProperties": {
      "name": "logs-anomaly-alert",
      "description": "Example of logs-anomaly alert",
      "enabled": true,
      "priority": "ALERT_DEF_PRIORITY_P3",
      "type": "ALERT_DEF_TYPE_LOGS_ANOMALY",
      "incidentsSettings": {
        "notifyOn": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
        "minutes": 60
      },
      "notificationGroup": {
        "webhooks": [
          {
            "notifyOn": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
            "integration": {
              "recipients": {
                "emails": [
                  "<EMAIL>"
                ]
              }
            },
            "minutes": 60
          }
        ]
      },
      "phantomMode": false,
      "deleted": false,
      "logsAnomaly": {
        "logsFilter": {
          "simpleFilter": {
            "luceneQuery": "QueryThisLog \u0026\u0026 This Log",
            "labelFilters": {
              "subsystemName": [
                {
                  "value": "includes",
                  "operation": "LOG_FILTER_OPERATION_TYPE_INCLUDES"
                }
              ]
            }
          }
        },
        "rules": [
          {
            "condition": {
              "minimumThreshold": 1000,
              "timeWindow": {
                "logsTimeWindowSpecificValue": "LOGS_TIME_WINDOW_VALUE_HOURS_24"
              }
            }
          }
        ]
      }
    },
    "id": "5bc8712b-ae0c-4d49-9cd2-1cc374eddfb7",
    "createdTime": "2024-11-06T08:46:10Z",
    "updatedTime": "2024-11-06T08:46:10Z",
    "alertVersionId": "5bc8712b-ae0c-4d49-9cd2-1cc374eddfb7"
  }
}
```

## Create a New Value Alert

Create a [New Value Alert](../../../../../user-guides/alerting/create-an-alert/logs/new-value-alerts/index.md).

### Request

```json
grpcurl -H "Authorization: Bearer API_KEY_HERE" -d @ ng-api-grpc.coralogix.com:443 com.coralogixapis.alerts.v3.AlertDefsService/CreateAlertDef <<EOF
{
    "alert_def_properties": {
        "name": "logs new value",
        "description": "example of logs new value",
        "priority": "ALERT_DEF_PRIORITY_P3",
        "enabled":  true,
        "incidents_settings": {
            "notify_on": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
            "minutes": 10
        },
        "entity_labels": {
            "my_key": "my_label"
        },
        "type": "ALERT_DEF_TYPE_LOGS_NEW_VALUE",
        "logs_new_value": {
            "logs_filter": {
                "simple_filter": {
                    "label_filters": {
                        "application_name": [],
                        "severities": [],
                        "subsystem_name": []
                    },
                    "lucene_query":  "_exists_:field"
                }
            },
            "notification_payload_filter": [],
            "rules": [
                {
                    "condition": {
                        "keypath_to_track": "keypath",
                        "time_window": {
                            "logs_new_value_time_window_specific_value": "LOGS_NEW_VALUE_TIME_WINDOW_VALUE_HOURS_24"
                        }
                    }
                }
            ]
        }
    }
}
EOF
```

### Response

```json
{
  "alertDef": {
    "alertDefProperties": {
      "name": "logs new value",
      "description": "example of logs new value",
      "enabled": true,
      "priority": "ALERT_DEF_PRIORITY_P3",
      "type": "ALERT_DEF_TYPE_LOGS_NEW_VALUE",
      "incidentsSettings": {
        "notifyOn": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
        "minutes": 10
      },
      "notificationGroup": {

      },
      "entityLabels": {
        "my_key": "my_label"
      },
      "phantomMode": false,
      "deleted": false,
      "logsNewValue": {
        "logsFilter": {
          "simpleFilter": {
            "luceneQuery": "_exists_:field",
            "labelFilters": {

            }
          }
        },
        "rules": [
          {
            "condition": {
              "keypathToTrack": "keypath",
              "timeWindow": {
                "logsNewValueTimeWindowSpecificValue": "LOGS_NEW_VALUE_TIME_WINDOW_VALUE_HOURS_24"
              }
            }
          }
        ]
      }
    },
    "id": "5722397b-0251-4832-83b7-e8e16dfd7c92",
    "createdTime": "2024-11-06T08:48:07Z",
    "updatedTime": "2024-11-06T08:48:07Z",
    "alertVersionId": "5722397b-0251-4832-83b7-e8e16dfd7c92"
  }
}
```

## Create a Unique Count Alert

Create a [Unique Count Alert](../../../../../user-guides/alerting/create-an-alert/logs/unique-count-alerts/index.md).

### Request

```json
grpcurl -H "Authorization: Bearer API_KEY_HERE" -d @ ng-api-grpc.coralogix.com:443 com.coralogixapis.alerts.v3.AlertDefsService/CreateAlertDef <<EOF
{
    "alert_def_properties": {
        "name": "logs unique count",
        "description": "example of logs unique count",
        "priority": "ALERT_DEF_PRIORITY_P4",
        "enabled": true,
        "group_by_keys": ["logobj.path"],
        "incidents_settings": {
            "notify_on": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
            "minutes": 10
        },
        "type": "ALERT_DEF_TYPE_LOGS_UNIQUE_COUNT",
        "logs_unique_count": {
            "logs_filter": {
                "simple_filter": {
                    "label_filters": {
                        "application_name": [],
                        "severities": [],
                        "subsystem_name": []
                    },
                    "lucene_query": "_exists_:field"
                }
            },
            "notification_payload_filter": [],
            "unique_count_keypath": "keypath",
            "max_unique_count_per_group_by_key": "100",
            "rules": [
                {
                    "condition": {
                        "max_unique_count": "100",
                        "time_window": {
                            "logs_unique_value_time_window_specific_value": "LOGS_UNIQUE_VALUE_TIME_WINDOW_VALUE_HOURS_12"
                        }
                    }
                }
            ]
        }
    }
}
EOF
```

### Response

```json
{
  "alertDef": {
    "alertDefProperties": {
      "name": "logs unique count",
      "description": "example of logs unique count",
      "enabled": true,
      "priority": "ALERT_DEF_PRIORITY_P4",
      "type": "ALERT_DEF_TYPE_LOGS_UNIQUE_COUNT",
      "groupByKeys": [
        "logobj.path"
      ],
      "incidentsSettings": {
        "notifyOn": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
        "minutes": 10
      },
      "notificationGroup": {

      },
      "phantomMode": false,
      "deleted": false,
      "logsUniqueCount": {
        "logsFilter": {
          "simpleFilter": {
            "luceneQuery": "_exists_:field",
            "labelFilters": {

            }
          }
        },
        "rules": [
          {
            "condition": {
              "maxUniqueCount": "100",
              "timeWindow": {
                "logsUniqueValueTimeWindowSpecificValue": "LOGS_UNIQUE_VALUE_TIME_WINDOW_VALUE_HOURS_12"
              }
            }
          }
        ],
        "maxUniqueCountPerGroupByKey": "100",
        "uniqueCountKeypath": "keypath"
      }
    },
    "id": "1ee21858-a338-4c22-a42c-205c3e76544b",
    "createdTime": "2024-11-06T08:50:04Z",
    "updatedTime": "2024-11-06T08:50:04Z",
    "alertVersionId": "1ee21858-a338-4c22-a42c-205c3e76544b"
  }
}
```

## Create a Metric Alert with a threshold

Create a [Metric Alert](../../../../../user-guides/alerting/create-an-alert/metrics/threshold-alerts/index.md) with a "less than or equals" condition.

### Request

```json
grpcurl -H "Authorization: Bearer API_KEY_HERE" -d @ ng-api-grpc.coralogix.com:443 com.coralogixapis.alerts.v3.AlertDefsService/CreateAlertDef <<EOF
{
    "alert_def_properties": {
        "name": "metrics threshold",
        "description": "example of metrics threshold",
        "priority": "ALERT_DEF_PRIORITY_P2",
        "enabled": true,
        "incidents_settings": {
            "notify_on": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
            "minutes": 10
        },
        "type": "ALERT_DEF_TYPE_METRIC_THRESHOLD",
        "metric_threshold": {
            "metric_filter": {
                "promql": "sum(http_requests_total{job=\"api-server\"}) by (job)"
            },
            "missing_values": {"replace_with_zero": true},
            "rules": [{
                "condition": {
                    "condition_type": "METRIC_THRESHOLD_CONDITION_TYPE_LESS_THAN_OR_EQUALS",
                    "for_over_pct": 80,
                    "of_the_last": {"metric_time_window_specific_value": "METRIC_TIME_WINDOW_VALUE_HOUR_1"},
                    "threshold": 5000
                },
                "override": {
                    "priority": "ALERT_DEF_PRIORITY_P2"
                }
            }],
            "undetected_values_management": {
                "trigger_undetected_values": true,
                "auto_retire_timeframe": "AUTO_RETIRE_TIMEFRAME_HOURS_24"
            }
        }
    }
}
EOF
```

### Response

```json
{
  "alertDef": {
    "alertDefProperties": {
      "name": "metrics threshold",
      "description": "example of metrics threshold",
      "enabled": true,
      "priority": "ALERT_DEF_PRIORITY_P2",
      "type": "ALERT_DEF_TYPE_METRIC_THRESHOLD",
      "incidentsSettings": {
        "notifyOn": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
        "minutes": 10
      },
      "notificationGroup": {

      },
      "phantomMode": false,
      "deleted": false,
      "metricThreshold": {
        "metricFilter": {
          "promql": "sum(http_requests_total{job=\"api-server\"}) by (job)"
        },
        "rules": [
          {
            "condition": {
              "threshold": 5000,
              "forOverPct": 80,
              "ofTheLast": {
                "metricTimeWindowSpecificValue": "METRIC_TIME_WINDOW_VALUE_HOUR_1"
              },
              "conditionType": "METRIC_THRESHOLD_CONDITION_TYPE_LESS_THAN_OR_EQUALS"
            },
            "override": {
              "priority": "ALERT_DEF_PRIORITY_P2"
            }
          }
        ],
        "undetectedValuesManagement": {
          "triggerUndetectedValues": true,
          "autoRetireTimeframe": "AUTO_RETIRE_TIMEFRAME_HOURS_24"
        },
        "missingValues": {
          "replaceWithZero": true
        }
      }
    },
    "id": "bb835aa2-7462-4d39-903b-9eab7dbaf1ca",
    "createdTime": "2024-11-06T09:01:12Z",
    "updatedTime": "2024-11-06T09:01:12Z",
    "alertVersionId": "bb835aa2-7462-4d39-903b-9eab7dbaf1ca"
  }
}
```

## Create a metric-based Anomaly Alert

Create a metric-based Anomaly [Alert](../../../../../user-guides/alerting/create-an-alert/metrics/anomaly-detection-alerts/index.md) to measure [more-than-usual anomalies](../../../../../user-guides/alerting/create-an-alert/metrics/anomaly-detection-alerts/index.md).

### Request

```json
grpcurl -H "Authorization: Bearer API_KEY_HERE" -d @ ng-api-grpc.coralogix.com:443 com.coralogixapis.alerts.v3.AlertDefsService/CreateAlertDef <<EOF
{
    "alert_def_properties": {
        "name": "metrics-anomaly alert",
        "description": "Example of metrics-anomaly alert",
        "enabled": true,
        "priority": "ALERT_DEF_PRIORITY_P3",
        "type": "ALERT_DEF_TYPE_METRIC_ANOMALY",
        "incidents_settings": {
            "notify_on": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
            "minutes":  5
        },
        "notification_group": {
            "group_by_keys": [],
            "webhooks": [
                {
                    "notify_on": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
                    "integration": {
                        "recipients": {
                            "emails": [ "<EMAIL>"]
                        }
                    },
                    "minutes": 5
                }
            ]
        },
        "metric_anomaly": {
            "rules": [
                {
                    "condition": {
                        "for_over_pct": 5,
                        "of_the_last": {
                            "metric_time_window_specific_value": "METRIC_TIME_WINDOW_VALUE_HOURS_24"
                        },
                        "threshold": 100,
                        "min_non_null_values_pct": 50,
                        "condition_type": "METRIC_ANOMALY_CONDITION_TYPE_MORE_THAN_USUAL_OR_UNSPECIFIED"
                    }
                }
            ],
            "metric_filter": {
                "promql": "sum(cpu) by (pod)"
            }
        }
    }
}
EOF
```

### Response

```json
{
  "alertDef": {
    "alertDefProperties": {
      "name": "metrics-anomaly alert",
      "description": "Example of metrics-anomaly alert",
      "enabled": true,
      "priority": "ALERT_DEF_PRIORITY_P3",
      "type": "ALERT_DEF_TYPE_METRIC_ANOMALY",
      "incidentsSettings": {
        "notifyOn": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
        "minutes": 5
      },
      "notificationGroup": {
        "webhooks": [
          {
            "notifyOn": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
            "integration": {
              "recipients": {
                "emails": [
                  "<EMAIL>"
                ]
              }
            },
            "minutes": 5
          }
        ]
      },
      "phantomMode": false,
      "deleted": false,
      "metricAnomaly": {
        "metricFilter": {
          "promql": "sum(cpu) by (pod)"
        },
        "rules": [
          {
            "condition": {
              "threshold": 100,
              "forOverPct": 5,
              "ofTheLast": {
                "metricTimeWindowSpecificValue": "METRIC_TIME_WINDOW_VALUE_HOURS_24"
              },
              "minNonNullValuesPct": 50
            }
          }
        ]
      }
    },
    "id": "42391e48-7996-4f7c-b7ca-8b359a012d7f",
    "createdTime": "2024-11-06T09:04:43Z",
    "updatedTime": "2024-11-06T09:04:43Z",
    "alertVersionId": "42391e48-7996-4f7c-b7ca-8b359a012d7f"
  }
}
```

## Create a Tracing Alert with immediate notifications

Create a [Tracing Alert](../../../../../user-guides/alerting/create-an-alert/traces/tracing-alerts/index.md) with the “notify immediately” condition.

### Request

```json
grpcurl -H "Authorization: Bearer API_KEY_HERE" -d @ ng-api-grpc.coralogix.com:443 com.coralogixapis.alerts.v3.AlertDefsService/CreateAlertDef <<EOF
{
    "alert_def_properties": {
        "name":"tracing immediate alert",
        "description": "Example of tracing immediate alert",
        "enabled": true,
        "priority": "ALERT_DEF_PRIORITY_P4",
        "type": "ALERT_DEF_TYPE_TRACING_IMMEDIATE",
        "incidents_settings": {
            "notify_on": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
            "minutes": 10
        },
        "tracing_immediate": {
            "tracing_filter": {
                "simple_filter": {
                    "latency_threshold_ms": 3000,
                    "tracing_label_filters": {
                        "application_name": [{
                            "operation": "TRACING_FILTER_OPERATION_TYPE_INCLUDES",
                            "values": ["test"]
                        }]
                    }
                }
            }
        }
    }
}
EOF
```

### Response

```json
{
  "alertDef": {
    "alertDefProperties": {
      "name": "tracing immediate alert",
      "description": "Example of tracing immediate alert",
      "enabled": true,
      "priority": "ALERT_DEF_PRIORITY_P4",
      "type": "ALERT_DEF_TYPE_TRACING_IMMEDIATE",
      "incidentsSettings": {
        "notifyOn": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
        "minutes": 10
      },
      "notificationGroup": {

      },
      "phantomMode": false,
      "deleted": false,
      "tracingImmediate": {
        "tracingFilter": {
          "simpleFilter": {
            "tracingLabelFilters": {
              "applicationName": [
                {
                  "values": [
                    "test"
                  ],
                  "operation": "TRACING_FILTER_OPERATION_TYPE_INCLUDES"
                }
              ]
            },
            "latencyThresholdMs": 3000
          }
        }
      }
    },
    "id": "c71915ba-0854-4b8f-8522-885063b500d5",
    "createdTime": "2024-11-06T09:06:34Z",
    "updatedTime": "2024-11-06T09:06:34Z",
    "alertVersionId": "c71915ba-0854-4b8f-8522-885063b500d5"
  }
}
```

## Create a Tracing Alert with a threshold

Create a [Tracing Alert](../../../../../user-guides/alerting/create-an-alert/traces/tracing-alerts/index.md) with a “more than” condition.

### Request

```json
grpcurl -H "Authorization: Bearer API_KEY_HERE" -d @ ng-api-grpc.coralogix.com:443 com.coralogixapis.alerts.v3.AlertDefsService/CreateAlertDef <<EOF
{
    "alert_def_properties": {
        "name":  "tracing threshold alert",
        "description": "Example of tracing threshold alert",
        "enabled":  true,
        "priority": "ALERT_DEF_PRIORITY_P4",
        "type": "ALERT_DEF_TYPE_TRACING_THRESHOLD",
        "incidents_settings": {
            "notify_on": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
            "minutes": 10
        },
        "entity_labels": {
            "key1": "value1"
        },
        "tracing_threshold": {
            "rules": [{
                "condition": {
                    "condition_type": "TRACING_THRESHOLD_CONDITION_TYPE_MORE_THAN_OR_UNSPECIFIED",
                    "span_amount": 20,
                    "time_window": {"tracing_time_window_value": "TRACING_TIME_WINDOW_VALUE_HOUR_1"}
                }
            }],
            "tracing_filter": {
                "simple_filter": {
                    "latency_threshold_ms": 3000,
                    "tracing_label_filters": {
                        "application_name": [{
                            "operation": "TRACING_FILTER_OPERATION_TYPE_INCLUDES",
                            "values": ["test"]
                        }]
                    }
                }
            }
        }
    }
}
EOF
```

### Response

```json
{
  "alertDef": {
    "alertDefProperties": {
      "name": "tracing threshold alert",
      "description": "Example of tracing threshold alert",
      "enabled": true,
      "priority": "ALERT_DEF_PRIORITY_P4",
      "type": "ALERT_DEF_TYPE_TRACING_THRESHOLD",
      "incidentsSettings": {
        "notifyOn": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
        "minutes": 10
      },
      "notificationGroup": {

      },
      "entityLabels": {
        "key1": "value1"
      },
      "phantomMode": false,
      "deleted": false,
      "tracingThreshold": {
        "tracingFilter": {
          "simpleFilter": {
            "tracingLabelFilters": {
              "applicationName": [
                {
                  "values": [
                    "test"
                  ],
                  "operation": "TRACING_FILTER_OPERATION_TYPE_INCLUDES"
                }
              ]
            },
            "latencyThresholdMs": 3000
          }
        },
        "rules": [
          {
            "condition": {
              "spanAmount": 20,
              "timeWindow": {
                "tracingTimeWindowValue": "TRACING_TIME_WINDOW_VALUE_HOUR_1"
              }
            }
          }
        ]
      }
    },
    "id": "321b4997-e07d-4132-8da2-82e320f03ad6",
    "createdTime": "2024-11-06T09:09:23Z",
    "updatedTime": "2024-11-06T09:09:23Z",
    "alertVersionId": "321b4997-e07d-4132-8da2-82e320f03ad6"
  }
}
```

## Create a Flow Alert

Create a [Flow Alert](../../../../../user-guides/alerting/create-an-alert/flow-alerts/index.md).

### Request

```json
grpcurl -H "Authorization: Bearer API_KEY_HERE" -d @ ng-api-grpc.coralogix.com:443 com.coralogixapis.alerts.v3.AlertDefsService/CreateAlertDef <<EOF
{
    "alert_def_properties": {
        "name": "flow alert",
        "description": "Example of flow alert",
        "enabled":  true,
        "priority": "ALERT_DEF_PRIORITY_P4",
        "type": "ALERT_DEF_TYPE_FLOW",
        "incidents_settings": {
            "notify_on": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
            "minutes": 10
        },
        "flow": {
            "stages": [
                {
                    "timeframe_ms": "3000",
                    "timeframe_type": "TIMEFRAME_TYPE_UP_TO",
                    "flow_stages_groups": {
                        "groups": [
                            {
                                "alert_defs": [
                                    {
                                        "id":  "id_of_alert"
                                    }
                                ],
                                "alerts_op": "ALERTS_OP_OR",
                                "next_op": "NEXT_OP_AND_OR_UNSPECIFIED"
                            }
                        ]
                    }
                }
            ]
        }
    }
}
EOF
```

### Response

```json
{
  "alertDef": {
    "alertDefProperties": {
      "name": "flow alert",
      "description": "Example of flow alert",
      "enabled": true,
      "priority": "ALERT_DEF_PRIORITY_P4",
      "type": "ALERT_DEF_TYPE_FLOW",
      "incidentsSettings": {
        "notifyOn": "NOTIFY_ON_TRIGGERED_AND_RESOLVED",
        "minutes": 10
      },
      "notificationGroup": {

      },
      "phantomMode": false,
      "deleted": false,
      "flow": {
        "stages": [
          {
            "timeframeMs": "3000",
            "timeframeType": "TIMEFRAME_TYPE_UP_TO",
            "flowStagesGroups": {
              "groups": [
                {
                  "alertDefs": [
                    {
                      "id": "id_of_alert",
                      "not": false
                    }
                  ],
                  "alertsOp": "ALERTS_OP_OR"
                }
              ]
            }
          }
        ],
        "enforceSuppression": false
      }
    },
    "id": "b2e54052-7614-4354-83ca-9b33f6b3e208",
    "createdTime": "2024-11-06T09:10:33Z",
    "updatedTime": "2024-11-06T09:10:33Z",
    "alertVersionId": "b2e54052-7614-4354-83ca-9b33f6b3e208"
  }
}
```

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Contact us **via our in-app chat** or by emailing [<EMAIL>](mailto:<EMAIL>).
