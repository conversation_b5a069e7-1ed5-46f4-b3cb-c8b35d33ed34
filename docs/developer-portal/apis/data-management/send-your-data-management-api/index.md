---
title: "Send-Your-Data Management API"
date: "2023-03-30"
description: "Coralogix provides an API that allows you to manage your 'Send Your Data' API keys."
---

Coralogix provides an API that allows you to manage
your [Send-Your-Data API keys](../../../../user-guides/account-management/api-keys/send-your-data-api-key/index.md).

## Prerequisites

- Api Key with `ApiKeys` preset. Access this in your navigation pane by clicking **Data Flow** > API Keys.

![](images/keys.png)

- Select a [Management API Endpoint](../../../../integrations/coralogix-endpoints.md#management).

## Create a New 'Send Your Data' API Key

```json
grpcurl -H "Authorization: Bearer <YOUR-API-KEY>" -d @ <CORALOGIX-DOMAIN> com.coralogixapis.aaa.apikeys.v3.ApiKeysService/CreateApiKey <<EOF
{
  "name": "my-api-key",
  "key_permissions": {
  	"presets": [    
            "SendData"    
        ]
  },  
  "owner": {
    "team_id": 000000
  },
  "hashed": false
}
EOF
```

### Request Args

| Field           | Description                                                                                                                                                                                                                                                                                                            |
|-----------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| key_name        | Name of the key                                                                                                                                                                                                                                                                                                        |
| key_permissions | Specifies the permissions assigned to this key. For 'Send Your Data' keys, the only value that should be used is the SendData permissions preset. (Note: A preset is a predefined group of permissions tailored to a specific role. A key can be assigned either individual permissions directly or through a preset.) |
| owner           | Defines who can be the owner of the key. The only available value at present is the team_id.                                                                                                                                                                                                                           |
| hashed          | Whether the key should be stored securely. If hashed, key value will be disclosed only once in create response.                                                                                                                                                                                                        |

### Response

The response will be a message with information about the created key.

```json
{
  "key_id": "c8d84762-9aff-4f90-93ef-1762757e0c2c",
  "name": "my-api-key",
  "value": "<key_value>",
  }
}
```

| Field  | Description                                                                           |
|--------|---------------------------------------------------------------------------------------|
| key_id | ID of the key                                                                         |
| name   | Assigned name to the key                                                              |
| Value  | Value of the key. For securely stored keys, this is the only time it will be visible. |

## Update 'Send Your Data' API Key

```json
grpcurl -H "Authorization: Bearer Bearer <YOUR-API-KEY>" -d @ <CORALOGIX-DOMAIN> com.coralogixapis.aaa.apikeys.v3.ApiKeysService/UpdateApiKey <<EOF
{
  "key_id": "252d3765-069a-4f32-b79a-18e60f4c7b61",
  "is_active": false,
  "new_name": "Some new name"
}
EOF
```

### Request args

| Field     | Description                                                |
|-----------|------------------------------------------------------------|
| key_id    | ID of the key                                              |
| is_active | If provided, defines if key should be active or not        |
| new_name  | If provided, new name that will be correlated with the key |

### Response

The response will be an empty message.

## Get a 'Send Your Data' API Key By Id

```json
grpcurl -H "Authorization: Bearer <YOUR-API-KEY>" -d @ <CORALOGIX-DOMAIN> com.coralogixapis.aaa.apikeys.v3.ApiKeysService/GetApiKey <<EOF
{
  "key_id": "252d3765-069a-4f32-b79a-18e60f4c7b61"
}
EOF

```

### Request args

| Field  | Description                                 |
|--------|---------------------------------------------|
| key_id | Id of they key send data key to be returned |

### Response

The response will be an message with API key information:

```json
{
  "key_info": {
    "id": "252d3765-069a-4f32-b79a-18e60f4c7b61",
    "name": "Some key name",
    "owner": {
      "team_id": 0000
    },
    "active": false,
    "hashed": false,
    "key_permissions": {
      "presets": [
        {
          "permissions": [
            "cloud-metadata-ingress:SendData",
            "logs.data-ingress:SendData",
            "metrics.data-ingress:SendData",
            "spans.data-ingress:SendData",
            "contextual-data:SendData"
          ],
          "name": "SendData"
        }
      ],
      "permissions": []
    },
    "value": "<key_value>"
  }
}
```

| Field             | Description                                                                                              |
|-------------------|----------------------------------------------------------------------------------------------------------|
| id                | ID of the key                                                                                            |
| name              | Assigned name to the key                                                                                 |
| owner             | Who owns the key                                                                                         
| active Key Active | If key is in active state                                                                                |
| hashed            | Whether the key is stored securely. If hashed, key value is only disclosed  once in create response      |
| key_permissions   | Permissions assigned to this key come either from presets (predefined groups of permissions) or directly |
| value             | Optional value of the key. If key is stored securely, this field will not be sent                        |

## Get all 'Send Your Data' API Keys Owned by caller of the endpoint

The caller can be either a user or a team, determined by the authentication context in which the call is performed

```json
grpcurl -H "Authorization: Bearer <YOUR-API-KEY>" -d @ <CORALOGIX-DOMAIN> com.coralogixapis.aaa.apikeys.v3.ApiKeysService/GetSendDataApiKeys <<EOF
{
}
EOF
```

### Request args

No args

### Response

The response will be a message with list of all 'Send Your Data' API keys owned by caller:

```json
{
  "keys": [
    {
      "key_info": {
        "id": "252d3765-069a-4f32-b79a-18e60f4c7b61",
        "name": "Some key name",
        "owner": {
          "team_id": 0000
        },
        "active": false,
        "hashed": false,
        "key_permissions": {
          "presets": [
            {
              "permissions": [
                "cloud-metadata-ingress:SendData",
                "logs.data-ingress:SendData",
                "metrics.data-ingress:SendData",
                "spans.data-ingress:SendData",
                "contextual-data:SendData"
              ],
              "name": "SendData"
            }
          ],
          "permissions": []
        },
        "value": "<key_value>"
      }
    }
  ]
}

```
List of

| Field             | Description                                                                                              |
|-------------------|----------------------------------------------------------------------------------------------------------|
| id                | ID of the key                                                                                            |
| name              | Assigned name to the key                                                                                 |
| owner             | Who owns the key                                                                                         
| active Key Active | If key is in active state                                                                                |
| hashed            | Whether the key is stored securely. If hashed, key value is only disclosed  once in create response      |
| key_permissions   | Permissions assigned to this key come either from presets (predefined groups of permissions) or directly |
| value             | Optional value of the key. If key is stored securely, this field will not be sent                        |

## **Support**

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may
come up.

Feel free to reach out to us**via our in-app chat**or by sending us an email at*
*[<EMAIL>](mailto:<EMAIL>)**.
