---
title: "Insights API"
date: "2020-07-19"
coverImage: "Insights-API-1000X1000.png"
---

This tutorial describes how you can retrieve "New", "Suspected", and "Top" errors using the Coralogix Insight API calls.

"New", "Suspected" and "Top" errors appear in tags, volume, and flow anomalies, as well as in the Coralogix dashboard. These insights help you proactively focus on problematic areas in your applications.

**Suspected Errors** – Error log types that comply with the query parameters and occur more than normal during the query time.

**New errors** - Error logs that comply with the query parameters and were not classified prior to the query time

**Top errors** - Most common error types that comply with the query parameters

Here is a description of the API calls' structure (make sure to select the correct Route address for your Account region):

\[table id=33 /\]

\[table id=34 /\]

**Notes**:

1. startDate and endDate have to be defined together.
2. User cannot send tagName together with dates as the dates associated with the tag will be used
3. The difference between endDate and startDate cannot be more than 7 Days

In the following example, the request is for the top five errors in descending order:

```json
{
  "startDate": *************,
  "endDate":*************,
  "type": "top",
  "order":"desc"
}
```

The result is a structure that starts with `totalCount` and continues with an array that includes the top 5 templates. For each template there will be count, ratio, metadata that was used for classification and a log example with place holder values:

```json
{
"totalCount": 47042,
... 
"topFiveTemplates": [
   {
     "count": 6780,
     "ratio": "14.41%",
     "metadata": {
         "applicationName": "w3wp",
         "category": "Error",
         "className": "Staging",
         "methodName": "",
         "severity": "ERROR",
         "subsystemName": "LPAPI-Imperial Palace"
        },
    "logExample": {
        "IP": "*************",
        "postal_code": "20149",
        "transaction": "123456789",
        "user":"AR23456",
        "duration":25967
    }
}
```

In a similar fashion, "new" and "suspected" type transaction will return a count and an array of appropriate logs. 

Here is an example of using curl command to call the API. This example retrieves newly introduced errors associated with the tag 'test":

``` bash
curl -H 'Authorization: Bearer 'your-private-key' -H "Content-type: application/json" -d '{"tagName": "test","type": "new"}' https://api.coralogix.com/api/v1/external/insights
```
