---
title: "Custom Enrichment API"
date: "2024-06-24"
description: "This tutorial demonstrates how to set up Custom Enrichment using the Coralogix API."
show_blocks:
  - domain_selector
---

This tutorial demonstrates how to set up [Custom Enrichment](../../../../user-guides/data-transformation/enrichments/custom-enrichment/index.md) using the Coralogix API.

## API endpoint

Select the [API endpoint](../../../../integrations/coralogix-endpoints.md#custom-enrichment) associated with your Coralogix [domain](../../../../user-guides/account-management/account-settings/coralogix-domain/index.md).

To use this API you need to [create](../../../../user-guides/account-management/api-keys/api-keys/index.md) a personal or team API key. It’s recommended to use permission presets, as they are automatically updated with all relevant permissions. Alternatively, you can manually add individual permissions.

| Preset | Action | Description |
| --- | --- | --- |
| Enrichments | `CLOUD-METADATA-ENRICHMENT:READCONFIG` <br> `CLOUD-METADATA-ENRICHMENT:UPDATECONFIG` <br> `GEO-ENRICHMENT:READCONFIG` <br> `GEO-ENRICHMENT:UPDATECONFIG` <br> `SECURITY-ENRICHMENT:READCONFIG` <br> `SECURITY-ENRICHMENT:UPDATECONFIG` <br> `TEAM-CUSTOM-ENRICHMENT:READCONFIG` <br> `TEAM-CUSTOM-ENRICHMENT:READDATA` <br> `TEAM-CUSTOM-ENRICHMENT:UPDATECONFIG` <br> `TEAM-CUSTOM-ENRICHMENT:UPDATEDATA` | View AWS Enrichment Configuration <br> Manage AWS Enrichment Configuration <br> View Geo Enrichment Configuration <br> Manage Geo Enrichment Configuration <br> View Unified Threat Intelligence Enrichment Configuration <br> Manage Unified Threat Intelligence Enrichment Configuration <br> View Custom Enrichment Configuration <br> View Custom Enrichment Data <br> Manage Custom Enrichment Configuration <br> Manage Data for Custom Enrichment Ingestion |

## Supported API calls

The following Custom Enrichments API calls are supported:

- Create

- Update

- Delete

- List

### Create

Create a new Custom Enrichment. Copy the curl commands below and customize them for your environment before sending them to the API. Please remember to use the correct endpoint for your deployment.

**Request:**

``` bash
curl --location --request POST '{{ endpoints.enrichment }}' \
--header 'Authorization: Bearer <cx_api_key>' \
--form 'name="\"YOUR_ENRICHMENT_NAME\""' \
--form 'description="\"YOUR_ENRICHMENT_DESCRIPTION\""' \
--form 'file=@"PATH_TO_YOUR_ENRICHMENT.CSV_FILE"'
```

!!! Note
    An example of the PATH_TO_YOUR_ENRICHMENT.CSV_FILE variable: "/Users/<USER>/CustomEnrichment.csv".

**Response:**

```json
{
    "message": "accepted new enrichment request with id 14",
    "customEnrichmentId": 14
}
```

**Status Codes: 202, 406, 502.**

### Update

[](https://github.com/coralogix/custom-enrichments-loader#request-2)**Request:**

``` bash
curl --location --request PUT '{{ endpoints.enrichment }}/14' \
--header 'Authorization: Bearer <cx_api_key>' \
--form 'name="\"YOUR_ENRICHMENT_NAME_V2\""' \
--form 'description="\"YOUR_ENRICHMENT_DESCRIPTION\""' \
--form 'file=@"PATH_TO_YOUR_ENRICHMENT.CSV_FILE"'
```

!!! Note

    -  The **customEnrichmentID** used in the Custom Enrichment PUT endpoint (**14** in this example) is taken from the initial Custom Enrichment creation POST request.

    - An example of the PATH_TO_YOUR_ENRICHMENT.CSV_FILE variable: "/Users/<USER>/CustomEnrichment_V2.csv".

**Response:**

```json
{
    "message": "accepted update to enrichment request with id 14",
    "customEnrichmentId": 14
}
```

**Status Codes: 202, 502.**

### Delete

**Request:**

``` bash
curl --location --request DELETE '{{ endpoints.enrichment }}/14' \
--header 'Authorization: Bearer <cx_api_key>' \
--data-raw ''
```

!!! Note

    The **customEnrichmentID** used in the Custom Enrichment DELETE endpoint (**14** in this example) is taken from the initial Custom Enrichment creation POST request.

[](https://github.com/coralogix/custom-enrichments-loader#response-2)**Response:**

```json
{
    "message": "deleted custom enrichment 14",
    "customEnrichmentId": 14
}
```

**Status Codes: 200, 409, 502.**

### List

**Request:**

``` bash
curl --location --request GET '{{ endpoints.enrichment }}/' \
--header 'Authorization: Bearer <cx_api_key>'
```

[](https://github.com/coralogix/custom-enrichments-loader#response-2)**Response:**

```json
[
  {
    "id": 13,
    "name": "Enrichment Test",
    "description": "First Coralogix API Custom Enrichment Test",
    "version": 1
  },
  {
    "id": 14,
    "name": "customer's UUID to customer name V2",
    "description": "This enrichment is for mapping UUID to name",
    "version": 2
  }
]
```

**Status Codes: 200, 500.**

## Additional resources
| | |
| --- | --- |
| Documentation | [Custom Enrichment](../../../../user-guides/data-transformation/enrichments/custom-enrichment/index.md) |

## Support

**Need help?**

Our world-class customer success team is available 24/7 to walk you through your setup and answer any questions that may come up.

Feel free to reach out to us **via our in-app chat** or by emailing [<EMAIL>](mailto:<EMAIL>).
