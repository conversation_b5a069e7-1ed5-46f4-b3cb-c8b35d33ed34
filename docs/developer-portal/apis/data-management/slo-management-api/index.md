---
title: "SLO Management API"
date: "2024-01-23"
description: "We are introducing the SLO API to enable teams, particularly those not utilizing a UI, to efficiently manage their SLOs programmatically. This API will let you retrieve, read, create, update and delete your SLOs."
show_blocks:
  - domain_selector
---

## Overview

We are introducing the SLO API to enable teams, particularly those not utilizing a UI, to efficiently manage their SLOs programmatically. This API will let you retrieve, read, create, update and delete your SLOs.

### Prerequisites

- [API Key for Alerts, Rules & Tags to successfully authenticate.](../../../../user-guides/account-management/api-keys/api-keys/index.md)

- [Management API Endpoint that corresponds with your Coralogix subscription.](../../../../integrations/coralogix-endpoints.md#management)

- Administrator permissions to manage your SLOs.

## API Endpoints

This reference document lists example requests and responses using [gRPCurl](https://github.com/fullstorydev/grpcurl). The following calls accept arguments as <PERSON><PERSON><PERSON> in the request body and return results as <PERSON>SO<PERSON> in the response body. A complete [list of Management Endpoints](../../../../integrations/coralogix-endpoints.md#management) is available here.

### Authentication

Coralogix API uses API keys to authenticate requests. You can view and [manage your API keys](../../../../user-guides/account-management/api-keys/api-keys/index.md) from the Data Flow tab in Coralogix. You need to use an API key in the Authorization request header to successfully connect.

Example:

```
grpcurl -H "Authorization: Bearer API_KEY_HERE"

```

Then, use one of our designated **[Management Endpoints](../../../../integrations/coralogix-endpoints.md#management)** to structure your header.

```
-d @ {{ endpoints.management }}

```

For SLO API, the service name will be `ServiceSloService`.

```
com.coralogixapis.apm.services.v1.ServiceSloService/

```

The complete request header should look like this:

```
grpcurl -H "Authorization: Bearer API_KEY_HERE" -d @ {{ endpoints.management }} com.coralogixapis.apm.services.v1.ServiceSloService/

```

### ListServiceSlos

Lists all available SLOs from the entire service catalog. In this example, there are two available SLOs, `List_bigger_than_10ms` from `productcatalogservice` and `Latency_bigger_then_1ms` from the `frontend` service. Here you will find the `id` field associated with each SLO.

Example request:

``` bash
grpcurl -H "Authorization: Bearer API_KEY_HERE" -d @ {{ endpoints.management }} com.coralogixapis.apm.services.v1.ServiceSloService/ListServiceSlos <<EOF
{
}
EOF

```

Example response:

```json
{
  "slos": [
    {
      "id": "049b684b-4ef0-449e-8fad-33e5f90775b4",
      "name": "List_bigger_than_10ms",
      "serviceName": "productcatalogservice",
      "description": "Check if the operation of list products is bigger than 10ms for 50% of the spans",
      "targetPercentage": 50,
      "createdAt": "1970-01-20T11:38:56.476Z",
      "remainingErrorBudgetPercentage": 100,
      "latencySli": {
        "thresholdMicroseconds": "10000",
        "thresholdSymbol": "THRESHOLD_SYMBOL_GREATER_OR_EQUAL"
      },
      "filters": [
        {
          "field": "operationname",
          "compareType": "COMPARE_TYPE_IS",
          "fieldValues": [
            "hipstershop.ProductCatalogService/ListProducts"
          ]
        }
      ],
      "period": "SLO_PERIOD_7_DAYS"
    },
    {
      "id": "089d1831-7c40-4a31-bab8-0f64116c18b2",
      "name": "Latency_bigger_then_1ms",
      "serviceName": "frontend",
      "description": "Check if latency is bigger then 1ms ",
      "targetPercentage": 1,
      "createdAt": "1970-01-20T11:40:32.323Z",
      "remainingErrorBudgetPercentage": 100,
      "latencySli": {
        "thresholdMicroseconds": "1000",
        "thresholdSymbol": "THRESHOLD_SYMBOL_GREATER_OR_EQUAL"
      },
      "period": "SLO_PERIOD_7_DAYS"
    }
  ]
}
```

### GetServiceSlo

Retrieves specific SLO information for a given service. In this example, you are retrieving all SLOs for the `frontend`service. You need to provide the SLO `id`.

Example request:

```json
grpcurl -H "Authorization: Bearer API_KEY_HERE" -d @ {{ endpoints.management }} com.coralogixapis.apm.services.v1.ServiceSloService/GetServiceSlo <<EOF
{
    "id": "049b684b-4ef0-449e-8fad-33e5f90775b4"
}
EOF

```

Example response:

```json
{
  "slo": {
    "id": "049b684b-4ef0-449e-8fad-33e5f90775b4",
    "name": "List_bigger_than_10ms",
    "serviceName": "productcatalogservice",
    "description": "Check if the operation of list products is bigger than 10ms for 50% of the spans",
    "targetPercentage": 50,
    "createdAt": "1970-01-20T11:38:56.476Z",
    "remainingErrorBudgetPercentage": 100,
    "latencySli": {
      "thresholdMicroseconds": "10000",
      "thresholdSymbol": "THRESHOLD_SYMBOL_GREATER_OR_EQUAL"
    },
    "filters": [
      {
        "field": "operationname",
        "compareType": "COMPARE_TYPE_IS",
        "fieldValues": [
          "hipstershop.ProductCatalogService/ListProducts"
        ]
      }
    ],
    "period": "SLO_PERIOD_7_DAYS"
  }
}

```

### BatchGetServiceSlos

Retrieves specified SLOs from the list. You need to provide the `id` for each required SLO. In this example, we are retrieving `List_bigger_than_10ms` and `Latency_bigger_than_1ms`.

Example request:

```json
grpcurl -H "Authorization: Bearer API_KEY_HERE" -d @ {{ endpoints.management }} com.coralogixapis.apm.services.v1.ServiceSloService/BatchGetServiceSlos <<EOF
{
  "ids": ["049b684b-4ef0-449e-8fad-33e5f90775b4", "089d1831-7c40-4a31-bab8-0f64116c18b2"]
}
EOF
```

Example response:

```json
{
  "slos": {
    "049b684b-4ef0-449e-8fad-33e5f90775b4": {
      "id": "049b684b-4ef0-449e-8fad-33e5f90775b4",
      "name": "List_bigger_than_10ms",
      "serviceName": "productcatalogservice",
      "description": "Check if the operation of list products is bigger than 10ms for 50% of the spans",
      "targetPercentage": 50,
      "createdAt": "1970-01-20T11:38:56.476Z",
      "remainingErrorBudgetPercentage": 100,
      "latencySli": {
        "thresholdMicroseconds": "10000",
        "thresholdSymbol": "THRESHOLD_SYMBOL_GREATER_OR_EQUAL"
      },
      "filters": [
        {
          "field": "operationname",
          "compareType": "COMPARE_TYPE_IS",
          "fieldValues": [
            "hipstershop.ProductCatalogService/ListProducts"
          ]
        }
      ],
      "period": "SLO_PERIOD_7_DAYS"
    },
    "089d1831-7c40-4a31-bab8-0f64116c18b2": {
      "id": "089d1831-7c40-4a31-bab8-0f64116c18b2",
      "name": "Latency_bigger_then_1ms",
      "serviceName": "frontend",
      "description": "Check if latency is bigger then 1ms ",
      "targetPercentage": 1,
      "createdAt": "1970-01-20T11:40:32.323Z",
      "remainingErrorBudgetPercentage": 100,
      "latencySli": {
        "thresholdMicroseconds": "1000",
        "thresholdSymbol": "THRESHOLD_SYMBOL_GREATER_OR_EQUAL"
      },
      "period": "SLO_PERIOD_7_DAYS"
    }
  }
}
```

### CreateServiceSlo

Creates a new SLO with given configuration for a chosen service. In this example, you are creating the `frontendtest`SLO for a `frontend` service.

Example request:

```json
grpcurl -H "Authorization: Bearer API_KEY_HERE" -d @ {{ endpoints.management }} com.coralogixapis.apm.services.v1.ServiceSloService/CreateServiceSlo <<EOF
{
  "slo": {
    "name": "frontendtest",
    "serviceName": "frontend",
    "description": "Sample description for your SLO",
    "targetPercentage": 95,
    "errorSli": {
    },
    "period": "SLO_PERIOD_7_DAYS"
  }
}
EOF

```

Example response:

```json
{
  "slo": {
    "id": "0416396c-a1ad-46d8-a10c-a05ea15dc3af",
    "name": "frontendtest",
    "serviceName": "frontend",
    "description": "Sample description for your SLO",
    "targetPercentage": 95,
    "createdAt": "1970-01-20T17:43:33.485Z",
    "remainingErrorBudgetPercentage": 100,
    "errorSli": {},
    "period": "SLO_PERIOD_7_DAYS"
  }
}
```

### ReplaceServiceSlo

Updates an SLO with new field entries for a chosen service. In this example, you are replacing the `description` content for a frontend service SLO. The SLO is supposed to check for errors in the frontend.

Example request:

```json
grpcurl -H "Authorization: Bearer API_KEY_HERE" -d @ {{ endpoints.management }} com.coralogixapis.apm.services.v1.ServiceSloService/ReplaceServiceSlo <<EOF
{
  "slo": {
    "id": "0416396c-a1ad-46d8-a10c-a05ea15dc3af",
    "name": "frontendtest",
    "serviceName": "frontend",
    "description": "Checks for frontend errors.",
    "targetPercentage": 95,
    "errorSli": {},
    "period": "SLO_PERIOD_7_DAYS"
  }
}
EOF
```

Example response:

```json
"slo": {
    "id": "0416396c-a1ad-46d8-a10c-a05ea15dc3af",
    "name": "frontendtest",
    "serviceName": "frontend",
    "description": "Checks for frontend errors.",
    "targetPercentage": 95,
    "createdAt": "1970-01-20T17:43:33.485Z",
    "remainingErrorBudgetPercentage": 100,
    "errorSli": {},
    "period": "SLO_PERIOD_7_DAYS"
  }
}

```

### DeleteServiceSlo

Deletes a specified SLO for a given service. In this example, you are deleting the `frontendtest` SLO from the `frontend`service.

Example request:

```json
grpcurl -H "Authorization: Bearer API_KEY_HERE" -d @ {{ endpoints.management }} com.coralogixapis.apm.services.v1.ServiceSloService/DeleteServiceSlo <<EOF
{
    "id": "0416396c-a1ad-46d8-a10c-a05ea15dc3af"
}
EOF
```

Example response:

```json
{}
```

## Body Parameters

| Field | Type | Definition |
| --- | --- | --- |
| id | int32 | Unique identifier for the Service Level Objective (SLO). |
| ids | array | List of service SLO IDs to retrieve. |
| slos | array | List of Service Level Objectives (SLOs) with their respective details. |
| name | string | Name of the SLO. |
| serviceName | string | Service name associated with the SLO. |
| description | string | Description of the SLO. |
| targetPercentage | integer | Target percentage for the SLO. |
| createdAt | timestamp | Timestamp indicating when the SLO was created. |
| remainingErrorBudgetPercentage | integer | Remaining error budget percentage. |
| errorSli | boolean | Indicates the presence of an error SLI. |
| latencySli | array | Defines SLI threshold and relationship. |
| latencySli.thresholdMicroseconds | string | Threshold in microseconds for latency. |
| latencySli.thresholdSymbol | enum | Symbol indicating the threshold relationship (e.g.GREATER_OR_EQUAL). |
| filters.field | string | Field for the filter. |
| filters.compareType | enum | Type of comparison (e.g., IS, START_WITH). |
| filters.fieldValues | string | List of field values for the filter. |
| period | enum | Period for the SLO (e.g., 7 days, 14 days). |

**CompareType**

| Name | Number | Description |
| --- | --- | --- |
| COMPARE_TYPE_UNSPECIFIED | 0 | Filter entry is unspecified |
| COMPARE_TYPE_IS | 1 | Filters for a specific entry. |
| COMPARE_TYPE_START_WITH | 2 | Filters for results that start with the entry. |
| COMPARE_TYPE_ENDS_WITH | 3 | Filters for results that end with the entry. |
| COMPARE_TYPE_INCLUDES | 4 | Filters for a result that includes the entry. |

ThresholdSymbol

| Name | Number | Description |
| --- | --- | --- |
| THRESHOLD_SYMBOL_UNSPECIFIED | 0 | Threshold criterion is not given. |
| THRESHOLD_SYMBOL_GREATER | 1 | Threshold criterion is greater than the specified number. |
| THRESHOLD_SYMBOL_GREATER_OR_EQUAL | 2 | Threshold criterion is greater than or equal to the specified number. |
| THRESHOLD_SYMBOL_LESS | 3 | Threshold criterion is less than the specified number. |
| THRESHOLD_SYMBOL_LESS_OR_EQUAL | 4 | Threshold criterion is less than or equal to the specified number. |
| THRESHOLD_SYMBOL_EQUAL | 5 | Threshold criterion is equal to the specified number. |
| THRESHOLD_SYMBOL_NOT_EQUAL | 6 | Threshold criterion is not equal to the specified number. |

SloPeriod

| Name | Number | Description |
| --- | --- | --- |
| SLO_PERIOD_UNSPECIFIED | 0 | Time period for which the SLO measures results is unspecified. |
| SLO_PERIOD_7_DAYS | 1 | Time period for which the SLO measures results is 7 days. |
| SLO_PERIOD_14_DAYS | 2 | Time period for which the SLO measures results is 14 days. |
| SLO_PERIOD_30_DAYS | 3 | Time period for which the SLO measures results is 30 days. |
