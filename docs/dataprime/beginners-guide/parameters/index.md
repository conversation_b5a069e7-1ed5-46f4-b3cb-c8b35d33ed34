---
title: "Query parameters ($p)"
date: "2024-10-13"
description: "The DataPrime Beginner’s Guide presents all of the DataPrime language basics, including query structure, data sources and types, commands and functions, and core language constructs."
---
# Query parameters ($p)

## Overview

`$p` enables users to incorporate variables in their queries within **Custom Dashboards** and time-related parameters within **Explore**.

This feature externalizes variables as if they were written directly in a query, enhancing flexibility for filtering, calculations, and visualizations by allowing user-defined inputs and contextual data handling.

## How `$p` works

- `$p` represents query parameters that can be used in widgets, queries, and external filters, such as a dashboard time picker.

- It enables dynamic filtering and other actions without requiring manual query modifications.

- Users can pass and manipulate these parameters through dashboard UI components.

## Defining query parameters

### Custom Dashboards

Once a [variable is created](../../../user-guides/custom-dashboards/tutorials/create-and-manage-variables/index.md) in [Custom Dashboards](../../../user-guides/custom-dashboards/tutorials/create-and-manage-variables/index.md), the variable values chosen by the user are reflected as query parameters in a DataPrime query using `$p.<variable_name>`.

!!! note

    Variables in Custom Dashboards are treated as string or multi-string values, meaning numeric variables cannot be defined directly. Work around this using, for example,`filter duration > $p.minDuration`.

### Explore

In [Explore](../../../user-guides/monitoring-and-insights/logs-screen/logs-in-explore-screen/index.md), users can perform time calculations in their DataPrime query with `$p.timeRange.*`, with `$p` allowing the user to source data from the time window determined by the UI time picker.


## Usage examples

The following section provides usage examples for `$p`.

## Working with a single-value variable

The following query example incorporates a single-value variable, as denoted by the use of `==`.

```dataprime
source logs
| filter cluster_name == $p.cluster
```

- This filters logs where `cluster_name` matches the user-defined, single-value variable  `$p.cluster`.

- Users can dynamically modify this value via the Custom Dashboards UI.

## Working with multi-selection variables

Multi-selection variables allow users to pass multiple potential values into a single parameter. 

### Handling "any value" selection

When a user selects "any value," the system treats this as `null`, meaning the filter is effectively ignored.

```dataprime
source logs
| filter $p.clusters == null || $p.clusters.arrayContains($d.clusterId)
```

- If the user selects "any value" for `$p.clusters`, it is treated as `null`, effectively removing the filter.

- Otherwise, the filter ensures that `$d.clusterId` exists within the selected values of `$p.clusters`.

### Numeric parameter example

Create a `minDurationSeconds` variable with a static list of values (e.g. 1,5,10,30,60). These values are strings (which are currently exclusively supported), but can be used as a number within the query as follows:

```dataprime
source logs
| filter duration ≥ $p.minDuration:number
```

## Defining a dynamic `groupby` field through a variable

Using dynamic variables `groupby` allows queries to adapt based on user-defined criteria, enabling more flexible data aggregation and analysis. The following query groups data based on the field specified by the $p.myGroupByVar variable, then aggregates the results by counting the number of occurrences in each group.

```dataprime
groupby $d[$p.myGroupByVar] agg count()
```

Note that this differs from `groupby $p.my_group_by_variable agg count()`. Since the variable is a string, running this query would be effectively identical to `groupby 'my_value' agg count()`, which means that it will perform a `groupby` on a static string, aggregating all results into a single group called `my_value`.

## Time-related parameters

Time-related parameters, automatically created, allow the user to source data from a time window determined by the UI time picker using `timeRange` in [Custom Dashboards](../../../user-guides/custom-dashboards/tutorials/create-and-manage-variables/index.md) and [Explore](../../../user-guides/monitoring-and-insights/logs-screen/logs-in-explore-screen/index.md).  

They include the following: 

- `$p.timeRange.startTime`: The start time of the UI time picker (e.g., 2024-03-06T08:00:00Z)

- `$p.timeRange.endTime`: The end time of the UI time picker (e.g., 2024-03-06T12:00:00Z)

### Filter logs within a specific time range

```dataprime
source logs between $p.timeRange.startTime and $p.timeRange.endTime
| filter duration >= $p.minDuration
```

This filters logs within the specified time range while ensuring the `duration` field is greater than or equal to `$p.minDuration`.

### Rounding to the nearest hour

```dataprime
source logs between $p.timeRange.startTime/1h and $p.timeRange.endTime/1h+1h
| filter duration >= $p.minDuration
```

This example adjusts the time range to the nearest full hour before filtering logs based on duration.  Any time calculation can be done using `$p.timeRange.*`.

### Joining last week’s data for the same timeframe

Using the [join](../../language-reference/commands-reference/join.md) command, the following query joins and compares the number of logs per subsystem from today to those exactly one week prior.  

```dataprime
source logs 
| groupby $l.subsystemname agg count() as cnt
| join (source logs timeshifted -7d
        | groupby $l.subsystemname agg count() as cnt) using subsystemname into a_week_befor
```

This verbose example provides a way to gain more control over the time range, if needed.

```dataprime
source logs 
| groupby $l.subsystemname agg count() as cnt
| join (source logs between $p.timeRange.startTime-7d and $p.timeRange.endTime-7d
        | groupby $l.subsystemname agg count() as cnt) using subsystemname into a_week_before
```