---
title: "Commands overview"
date: "2024-10-13"
description: "The commands available in the DataPrime Query Language"
---

# Commands overview

Commands in DataPrime unlock the ability for users to transform, filter, expand, remove, replace and much more. They're the foundational components of the language.

They are considerably more varied than functions, meaning their syntax will be different for each command, so be sure to understand the syntax and arguments before running any DataPrime queries on your production data. 