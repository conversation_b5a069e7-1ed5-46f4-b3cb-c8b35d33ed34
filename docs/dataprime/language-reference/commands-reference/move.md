---
title: "move"
date: "2024-10-13"
description: "Move a keypath"
---

# move - Move a keypath

Keypaths are sometimes spread out, which complicates queries and makes reading the resulting document challenging. `move` will move a given keypath to a different location in a document. 

!!! note
    If a keypath is an object, the keypath **and** all child keys are moved.

## Syntax
```dataprime
(m|move) <source-keypath> to <target-keypath>
```

### Example - Moving a key to an easier location

Consider the following document:

```json
{
    "data":{
        "my_data": {
            "labels": {
                "label1": "value1"
            }
        }
    }
}
```

We are primarily interested in `labels` but to access it, we must traverse the object hierarchy. This can quickly make queries needlessly complex. Instead, we can `move` the `labels` keypath to a better location.

```dataprime
move data.my_data.labels to labels
```

This results in the following document:

```json
{
    "data":{
        "my_data": {}
    },
    "labels": {
        "label1": "value1"
    }
}
```

Now, instead of `data.my_data.labels`, I can simply access `labels` directly. 


