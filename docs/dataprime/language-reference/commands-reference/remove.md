---
title: "remove"
date: "2024-10-13"
description: "Remove unwanted keypaths"
---

# remove - Remove unwanted keypaths

The `remove` operator will take a set of keypaths out of every document in the working set.

This is the negation of `choose`. 

!!! note
    `remove` works on scalar values or objects. 

## Syntax

```dataprime
r|remove <keypath1> [ "," <keypath2> ]...
```

### Example - Removing personal information from data

Consider the following document:

```json
{
    "username": "chris",
    "email": "<EMAIL>",
    "location": "UK",
    "domain": "chris.com"
}
```

There are several fields in this document we want to keep, but `email` is personal, so we want to remove it without touching the others. We can do this using the `remove` operator:

```dataprime
remove email
```

