---
title: "filter"
date: "2024-10-13"
description: "Filter out events that to not match a condition."
---

# filter - Filter out events that to not match a condition.

The `filter` operator will remove all events that do not match a given condition. The condition must return `true` if an event matches.

## Syntax

```dataprime
(f|filter|where) <condition-expression>
```

!!! note
    When using a condition to compare a keypath with `null`, this will only work on scalar values (string, number, timestamp and so on). For JSON objects within a given document, comparing with null will always return `null`. 

### Example - Filter for HTTP status codes in the 500 range.

Consider the following documents:

```json
{
    "http_status_code": 502
},
{
    "http_status_code": 404
}
```

We can filter for only those documents containing status codes that are in the 500 range, using the `filter` operator:

```dataprime
filter http_status_code > 500
```

This can also be written using one of the filter aliases, like `f` or `where`:

* `f http_status_code > 500`
* `where http_status_code > 500`

### Example - Filter for IP addresses in a given range.

`filter` can be coupled with functions to perform complex searches with very little syntax, for example using the `ipInSubnet` function:

```dataprime
filter ipInSubnet(ip_address, '***********/24')
```


