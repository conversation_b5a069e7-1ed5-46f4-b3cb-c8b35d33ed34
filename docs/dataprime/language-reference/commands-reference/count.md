---
title: "count"
date: "2024-10-13"
description: "Count the number of rows in a set."
---

# count - Count the number of rows in a set.

The `count` operator returns a single row, that represents a count of the results. 

## Syntax

```dataprime
count [into <keypath>]
```

### Example - Counting the number of HTTP Requests

If we have a log document that represents HTTP requests, we can use a simple count to tally them up:

```dataprime
filter status_code != null 
| count
```

This will return a single numeric value. We can also alias this field for better readability:

```dataprime
filter status_code != null 
| count into http_request_count
```

Now, our document that we get back will look like this:

```json
{ "http_request_count": 1242 }
```


