---
title: "countby"
date: "2024-10-13"
description: "Count grouped values"
---

# countby - Count grouped values

The `countby` operator will generate a count for each row in a group. 

!!! note
    This differs from `count`, which will count the entire resultset, and will instead count based on a grouping key.

## Syntax

```dataprime
countby <expression> [as <alias>] [into <keypath>]
```

!!! note
    An alias can be provided to override the keypath the result will be written into.

### Example - Count the number of requests by HTTP Status Code

A simple calculation to perform on HTTP request logs is to count by HTTP status code.

```dataprime
countby status_code
```

We can also name our count column using the `into` keyword:

```dataprime
countby status_code into request_count
```
