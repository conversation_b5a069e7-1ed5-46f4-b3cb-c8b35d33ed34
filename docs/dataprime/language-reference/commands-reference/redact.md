---
title: "redact"
date: "2024-10-13"
description: "Obscure values from a string"
---

# redact - Obscure values from a string

The `redact` operator will look for a substring or string that matches a given regular expression, and replace it with the desired redacted string. This is especially useful for hiding emails, especially when they're hidden inside existing messages.

## Syntax

```dataprime
redact <keypath> [matching] /<regular-expression>/ to '<redacted_str>'
redact <keypath> [matching] <string> to '<redacted_str>'
```

!!! note
    The `matching` keyword is optional and can be used to increase readability.

### Example - Removing an email from a message value

It is typical for sensitive values to come nested in message fields. We can use the `redact` keyword to make sure these are hidden from view. 

Consider the following document:

```json
{
    "msg": "User chris <NAME_EMAIL> just signed in!"
}
```

We want to remove the email from the `msg` field. To do this, we'll create a regular expression, using a regex literal, and replace it with a clear redacted message.

```dataprime
redact msg matching /[a-z0-9]+@coralogix.com/ to 'REDACTED'
```

This results in the following document:

```json
{
    "msg": "User chris with email REDACTED just signed in!"
}
```



