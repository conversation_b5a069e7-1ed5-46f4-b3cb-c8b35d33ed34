---
title: "case_greaterthan"
date: "2024-10-13"
description: "Shorthand for case for greater than operations"
---

# case_greaterthan - Shorthand for case for greater than operations

This shorthand will allow users to quickly define case statements for numeric comparisons where a given value is larger than the comparison field.

!!! note
    This case statement, like all case statements, will return the _first matching value_. This means the order of your clauses in your case statement are extremely important.

## Syntax

```dataprime
case_greaterthan {
  n: number,
  value1: number -> result1,
  value2: number -> result2,
  ...
  valueN: number -> resultN,
  _              -> <default-value>
}
```

### Example - Mapping numeric HTTP status codes to text descriptions

Our goal is to add a field, `status_description` which is a text description of a given HTTP Status code. Consider the following log documents:

```json
{
  "status_code": 201,
  ...
},
{
  "status_code": 500,
  ...
},
{
  "status_code": 404,
}
```

We simply need to use the `status_code` field as our candidate, and compare in *descending order*. We compare in descending order because the case statement will return the **first value that matches**. For example, a value of `404` should not match `500` but it should match `400` and nothing else. 

```dataprime
case_greaterthan {
  $d.status_code,
  500 -> 'server-error',
  400 -> 'client-error',
  300 -> 'redirection',
  200 -> 'success',
  100 -> 'information',
  _   -> 'other'
}
```

We can then save the output of this into a new field, like so:

```dataprime
create status_description from 
  case_greaterthan {
    $d.status_code,
    500 -> 'server-error',
    400 -> 'client-error',
    300 -> 'redirection',
    200 -> 'success',
    100 -> 'information',
    _   -> 'other'
  }
```
