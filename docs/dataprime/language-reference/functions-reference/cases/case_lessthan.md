---
title: "case_lessthan"
date: "2024-10-13"
description: "Shorthand for case for less than operations"
---

# case_lessthan - Shorthand for case for less than operations

This shorthand will allow users to quickly define case statements for numeric comparisons where a given value is less than the comparison field.

!!! note
    This case statement, like all case statements, will return the _first matching value_. This means the order of your clauses in your case statement are extremely important.

## Syntax

```dataprime
case_lessthan {
  n: number,
  value1: number -> result1,
  value2: number -> result2,
  ...
  valueN: number -> resultN,
  _              -> <default-value>
}
```

### Example - Mapping temperature values to a simple description

Our goal is to add a field, `temperature_description` which is a text description of a given celsius value. Consider the following log documents:

```json
{
  "temperature_celsius": 10,
  ...
},
{
  "temperature_celsius": 50,
  ...
},
{
  "temperature_celsius": 20,
}
```

We can use a `case_lessthan` statement to map the values, *in ascending order* to their most appropriate description. It is important to use ascending order here. For example, a celsius value of `20` is less than `45`, but it is also less than `30`, meaning it is most appropriate to label it `fun`. If we do not use ascending order, we will always match our highest value. 

```dataprime
case_lessthan {
  $d.temperature_celsius,
  10 -> 'freezing',
  20 -> 'cold',
  30 -> 'fun',
  45 -> 'hot',
  _  -> 'burning'
}

```

We can then save the output of this into a new field, like so:

```dataprime
create temperature_description from 
  case_lessthan {
    $d.temperature_celsius,
    10 -> 'freezing',
    20 -> 'cold',
    30 -> 'fun',
    45 -> 'hot',
    _  -> 'burning'
  }
```
