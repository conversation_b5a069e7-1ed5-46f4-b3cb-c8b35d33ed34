---
title: "contains"
date: "2024-10-13"
description: "Check for the present of a substring"
---

# contains - Check for the present of a substring

The `contains` function returns `true` iff a substring exists within a given `string`. 

## Syntax

```dataprime
contains(string: string, substring: string): bool
```

### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| string | string | **true** | The full string, i.e the haystack |
| substring | string | **true** | The substring for which we'll search in the full string, i.e the needle |

### Example - Check if an AWS Account ID appears in an ARN

Sometimes we only have a broader field, like an ARN, rather than its constituent parts. In this case, the `contains` function can be used to inspect values within the string.

```dataprime
create is_from_account from arn_field.contains('************')
```

This will produce a field called `is_from_account` which is `true` if the ARN contains the associated AWS account ID.

### Example - Check if a domain appears within a path

We can use `contains` to check if a domain appears within a given URL, for example:

```dataprime
create is_from_google from contains($d.url, 'google.com')
```

This will produce a field called `is_from_google` that is `true` is the URL contains `google.com`. 
