---
title: "rtrim"
date: "2024-10-13"
description: "Remove whitspace from string end"
---

# rtrim - Remove whitspace from string end

The `rtrim` function will remove whitespace from the end of a given string value, but not from the start.

## Syntax

```dataprime
rtrim(value: string): string
```
### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| value | string | **true** | The string to be trimmed |

### Example - Cleaning up an extracted username

Consider the following document:

```json
{
  "message": "user <PERSON>  has logged in"
}
```

We want to extract the username from this string, so that we can search and query it directly. This can be done using the `extract` keyword with a regular expression.

```dataprime
extract message into my_data using regexp(e=/user (?<user>.*) has logged in/)
```

This results in this log object:

```json
{
  "message": "user <PERSON>  has logged in",
  "my_data": {
    "user": "<PERSON> "
  }
}
```

Notice the trailing space at the end of the username. This is because a stray space has made its way in. We can use `rtrim` to clean this up:

```dataprime
replace my_data.user with my_data.user.rtrim()
```

This will remove the trailing space from the username and result in a cleaner value.
