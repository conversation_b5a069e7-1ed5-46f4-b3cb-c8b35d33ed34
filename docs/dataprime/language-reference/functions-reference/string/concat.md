---
title: "concat"
date: "2024-10-13"
description: "Join together multiple string values"
---

# concat - Join together multiple string values

The `concat` function connects multiple strings together and produces a single string as a result.

## Syntax

```dataprime
concat(value: string, ...values: string): string
```

### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| value | string | **true** | The first string value to be concatenated |
| ...values | string | **true** | All subsequent string values to be concatenated, in the order they should be concatenated |

### Example - Combining first and last names into full name

Consider the following document, representing a user action in a system

```json
{
    "username": "<PERSON>",
    "first_name": "<PERSON>",
    "last_name": "<PERSON>c<PERSON>hr<PERSON>",
    "timestamp_ns": ****************,
    "path": "/home",
    "ip": "********"
}
```

Using the `concat` function, we can join together `first_name` and `last_name` to form a new field, `full_name`:

```dataprime
create full_name from first_name.concat(' ', last_name)
```

Our new document looks like this:

```json
{
    "username": "Chris",
    "first_name": "Chris",
    "last_name": "McChris",
    "full_name": "Chris McChris",
    "timestamp_ns": ****************,
    "path": "/home",
    "ip": "********"
}
```

### Example - Building an Amazon ARN

Documents are often decorated with metadata from a cloud provider, for example Amazon EC2. Consider the following document:

```json
{
    "aws": {
        "region": "eu-west-1",
        "instance_id": "i-02084e96d21517df8",
        "account_number": ************,
        ...
    }
}
```

Amazon ARNs are of the form: `arn:aws:ec2:<region>:<account-number>:instance/<instance-id>`, and we can use this information to build our new ARN field:

```dataprime
create aws.instance_arn from concat('arn:aws:ec2:', aws.region, ':', aws.account_number, ':instance/', aws.instance_id)
```

Our new document looks something like this:

```json
{
    "aws": {
        "region": "eu-west-1",
        "instance_id": "i-02084e96d21517df8",
        "account_number": ************,
        "instance_arn": "arn:aws:ec2:eu-west-1:************:instance/i-02084e96d21517df8",
        ...
    }
}
```
