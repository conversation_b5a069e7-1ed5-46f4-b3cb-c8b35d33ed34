---
title: "diffTime"
date: "2024-10-13"
description: "Compute the difference between two timestamps"
---

# diffTime - Compute the difference between two timestamps

The `diffTime` function calculates the duration between two timestamps. It is important to note that this **is not the absolute difference**.

* The result is positive if `to > from`
* The result is negative if `to < from`

## Syntax

```
diffTime(to: timestamp, from: timestamp): interval
```
### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| to | timestamp | **true** | The timestamp to compare (typically the larger) |
| from | timestamp | **true** | The timestamp to compare (typically smaller) |

### Example - Computing the time taken for a process to complete.

Consider the following document:

```json
{
    "processing_start_time": 1728636298,
    "processing_end_time": 1728636358
}
```

In this example, we know which is the `to` and which is the `from` time, making `diffTime` a good candidate.

```dataprime
create time_taken_duration from diffTime(processing_start_time, processing_end_time)
```

This will result in an interval with a positive value, because `processing_end_time` is greater than `processing_start_time`. The resulting document will look like this:

```json
{
    "processing_start_time": 1728636298,
    "processing_end_time": 1728636358,
    "time_taken_duration": 50s
}
```


