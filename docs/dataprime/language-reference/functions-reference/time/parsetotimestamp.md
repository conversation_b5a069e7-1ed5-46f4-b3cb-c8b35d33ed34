---
title: "parseToTimestamp"
date: "2024-10-13"
description: "Parse a timestamp from a `string`"
---

# parseToTimestamp - Parse a timestamp from a `string`

_Deprecated:_ use `parseTimestamp` instead

Parses a timestamp from `string` with an optional format specification and time zone override. See `parseTimestamp` for more details.

## Syntax

```
parseToTimestamp(string: string, format: string?, tz: string?): timestamp
```
### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| timestamp | string | **true** | The timestamp string to parse |
| format | string | **false** | The format of the timestamp. **Defaults to `auto`** |
| tz | string | **false** | Must be a valid Time Zone string. See Time Zone section to find out more. |