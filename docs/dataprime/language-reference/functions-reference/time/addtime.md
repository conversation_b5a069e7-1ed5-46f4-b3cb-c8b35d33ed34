---
title: "addTime"
date: "2024-10-13"
description: "Adds an interval to a timestamp"
---

# addTime - Adds an interval to a timestamp

This function adds an interval to a timestamp value. Both `timestamp` and `interval` are first class types in DataPrime, meaning it's important to ensure that when you're using this function, the correct data types are being passed in.

!!! note
    The `addTime` function also works with negative `interval` values, which is the same as subtracting a value.

## Syntax

```
addTime(t: timestamp, i: interval): timestamp
```
### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| t | timestamp | **true** | The timestamp to modify |
| i | interval | **true** | The interval, determining how much to modify the timestamp |

### Example - Computing end timestamp

In this example, the `start_timestamp` field is used, which is an existing timestamp field on the log. A `duration` field exists on the logs, which represents how long some process took in seconds. This `duration` field is parsed into an `interval` and then the `addTime` function is used to compute the time when this particular event ended.

```dataprime
source logs
| create end_timestamp from addTime(start_timestamp, duration.toInterval('s'))
```


