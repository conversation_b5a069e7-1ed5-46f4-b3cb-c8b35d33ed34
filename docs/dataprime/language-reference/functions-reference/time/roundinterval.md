---
title: "roundInterval"
date: "2024-10-13"
description: "Round intervals to a lower precision"
---

# roundInterval - Round intervals to a lower precision

`roundInterval` will reduce the precision of an `interval` value to some desired `timeunit`. All `timeunit` fields that are smaller than the desired `timeunit` will be zeroed out.

## Syntax

```dataprime
roundInterval(interval: interval, scale: timeunit): interval
```
### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| interval | interval | **true** | The interval to round |
| scale | timeunit | **true** | The highest `timeunit` to render |

### Example - Grouping time differences into buckets

Consider the following documents:

```json
{
    "time_taken": 2h10m15s
},
{
    "time_taken": 3h8m15s
},
{
    "time_taken": 3h10m15s
},
{
    "time_taken": 53m15s
}
```

We want to group these values into a bucket per the number of hours taken.

Our first step is to use `roundInterval` to convert the `interval` into `1` hour counts.

```dataprime
create time_bucket from roundInterval(time_taken, 'h')
```

We now have a new field `time_bucket` in our document:

```json
{
    "time_taken": 2h10m15s,
    "time_bucket": 2h10m15s
},
{
    "time_taken": 3h8m15s,
    "time_bucket": 3h8m15s
},
{
    "time_taken": 3h10m15s,
    "time_bucket": 3h10m15s
},
{
    "time_taken": 53m15s,
    "time_bucket": 0ns
}
```

Next, we want to express these intervals as hours.

```dataprime
replace time_bucket with time_bucket.formatInterval('h')
```

This will result in the following document:


```json
{
    "time_taken": 2h10m15s,
    "time_bucket": 2h
},
{
    "time_taken": 5h8m15s,
    "time_bucket": 3h
},
{
    "time_taken": 3h10m15s,
    "time_bucket": 3h
},
{
    "time_taken": 53m15s,
    "time_bucket": 0ns
}
```

Finally, we can group by `time_bucket` to count up each value.

```dataprime
groupby time_bucket agg count() as action_count
```

This results in the following documents:

```json
{
    "time_bucket": 2h,
    "action_count": 1
},
{
    "time_bucket": 3h,
    "action_count": 2
},
{
    "time_bucket": 0ns,
    "action_count": 1
}
```


