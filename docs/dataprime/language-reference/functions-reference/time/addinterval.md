---
title: "addInterval"
date: "2024-10-13"
description: "Add two intervals together"
---

# addInterval - Add two intervals together

`addInterval` combines two intervals into a single value. For example, an interval with the value of `1d` (one day) and another interval with the same value, will result in a single interval with the value of `2d` (two days). In effect, it adds the two intervals together.

!!! note
    This function will also work with negative intervals. If an interval of `1d` (one day) is added to an interval of value `-1h` then the resultant interval is equal to `23h`. As in arithmatic, the addition of a positive and a negative value is equal to subtraction.

## Syntax

```
addInterval(left: interval, right: interval): interval
```
### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| left | interval | **true** | The interval to add |
| right | interval | **true** | The other interval to add |

### Example - Computing total processing time with two different unit measurements:

For this example, consider this document: 

```json
{
    "loading_duration_ms": 5432,
    "processing_start_time": 1728582889,
    "processing_end_time": 1728582899
}
```

Our goal is to understand how long the entire process took, but it is not simple, because we have some values in seconds, some values in miliseconds and we don't have two intervals to start with. In order to tackle this problem, we'll begin by calculating the interval difference between these two timestamps. We'll then add the new processing interval to our loading duration, to calculate the overall processing time.

```dataprime
create processing_duration from diffTime(processing_start_time, processing_end_time)
| create total_duration from addInterval(loading_duration_ms.toInterval('ms'), processing_duration)
```

We now have a field called `total_duration` which represents the time taken for the overall transaction, from the start of loading to the end of processing. Our final document looks something like this:

```json
{
    "loading_duration_ms": 5432,
    "processing_start_time": 1728582889,
    "processing_end_time": 1728582899,
    "processing_duration": "10s",
    "total_duration": "15432ms"
}
```


