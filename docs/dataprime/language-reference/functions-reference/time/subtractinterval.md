---
title: "subtractInterval"
date: "2024-10-13"
description: "Subtract two interval values"
---

# subtractInterval - Subtract two interval values

The `subtractInterval` function will subtract two interval values from each other. 

!!! note
    Equivalent to `addInterval(left, -right)` and `left - right`.

## Syntax

```
subtractInterval(left: interval, right: interval): interval
```
### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| left | interval | **true** | The interval to be subtracted |
| right | interval | **true** | The interval to subtract |

### Example - Calculating pause time

Consider the following document:

```json
{
    "event": "PROCESSING_COMPLETE",
    "timestamp": 1728763337,
    "total_time_taken": 1m,
    "processing_time_taken": 30s,
    "loading_time_taken": 28s
}
```

We want to work out the idle time, when nothing was being done. To do this, we can add the `processing_time_taken` field to the `loading_time_taken` field, before subtracting the total from the `total_time_taken`:

```dataprime
create idle_time from total_time_taken.subtractInterval(loading_time_taken + processing_time_taken)
```

This will result in the following document:

```json
{
    "event": "PROCESSING_COMPLETE",
    "timestamp": 1728763337,
    "total_time_taken": 1m,
    "processing_time_taken": 30s,
    "loading_time_taken": 28s,
    "idle_time": 2s
}
```

### Try it yourself

Open your explore screen and paste in the following dataprime query:

```dataprime
create processing_time_taken from toInterval(30, 's')
| create loading_time_taken from toInterval(28, 's')
| create total_time_taken from toInterval(1, 'minute')
| create idle_time from total_time_taken.subtractInterval(loading_time_taken + processing_time_taken)
```


