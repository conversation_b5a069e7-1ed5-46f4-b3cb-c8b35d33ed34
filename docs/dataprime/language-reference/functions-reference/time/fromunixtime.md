---
title: "fromUnixTime"
date: "2024-10-13"
description: "Parse a timestamp from a unix timestamp"
---

# fromUnixTime - Parse a timestamp from a unix timestamp

The `fromUnixTime` is helpful if you have a keypath that contains a unix timestamp, but it has not been parsed as a `timestamp` and is a `number` instead. The UNIX epoch starts on January 1, 1970 - earlier timestamps are represented by negative numbers.

## Syntax

```dataprime
fromUnixTime(unixTime: number, timeUnit: timeunit?): timestamp
```
### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| unixTime | number | **true** | The unix timestamp to parse |
| timeUnit | timeunit | **false** | The units of the unix timestamp. **Defaults to `milli`** |

### Example - Basic usage

We can simply convert a numerical literal or keypath into a timestamp.

In this example, we interpret a numerical value as the number of miliseconds since epoch. This is then parsed into a field called `ts`.

```dataprime
choose fromUnixTime(1658958157515, 'ms') as ts # Result: { "ts": 1658958157515000000 }
```


