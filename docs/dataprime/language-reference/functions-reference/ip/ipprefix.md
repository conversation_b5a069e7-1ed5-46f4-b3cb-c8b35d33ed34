---
title: "ipPrefix"
date: "2024-10-13"
description: "Generate the CIDR range for a given subnet"
---

# ipPrefix - Generate the CIDR range for a given subnet

The `ipPrefix` function is very useful for working out the subnet in which an IP address belongs.

## Syntax

```dataprime
ipPrefix(ip: string, subnetSize: number): string
```
### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| ip | string | **true** | The IP address to process |
| subnetSize | number | **true** | The number of mask bits in the subnet. For example, `10` will result in a CIDR range ending in `/10` |

### Example - Generating the CIDR range for a given IP in a subnet of known size

Consider the following document:

```json
{
    "ip": "***********"
}
```

To calculate the subnet for this IP, and assuming that we know the number of mask bits, we can use the `ipPrefix` function:

```dataprime
create ip_subnet from ipPrefix(ip, 10)
```

This results in the following document:

```json
{
    "ip": "***********",
    "ip_subnet": "**********/10"
}
```
