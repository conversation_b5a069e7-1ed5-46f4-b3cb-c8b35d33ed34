---
title: "ipInSubnet"
date: "2024-10-13"
description: "Check if IP belongs to a given subnet"
---

# ipInSubnet - Check if IP belongs to a given subnet

The `ipInSubnet` function will detect if an IP address belongs to a given subnet.

## Syntax

```dataprime
ipInSubnet(ip: string, ipPrefix: string): bool
```

### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| ip | string | **true** | The IP address to test |
| ipPrefix | string | **true** | The CIDR range to check against the IP address e.g `**********/24` |

### Example - Filter for IP addresses in a given range

`filter` can be coupled with `ipInSubnet` to perform complex searches with very little syntax:

```dataprime
filter ipInSubnet(ip_address, '**********/24')
```

This will search for a field, `ip_address`, check if that `ip_address` is within the range `**********/24`. If it is, it is included in the results, otherwise it is excluded.
