---
title: "round"
date: "2024-10-13"
description: "Round numerical value to nearest integer"
---

# round - Round numerical value to nearest integer

The `round` function will round a given numerical value to the nearest integer. For example, `1.5` becomes `2`, `8.1` becomes `8`.

## Syntax

```dataprime
round(number: number, digits: number?): number
```

### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| number | number | **true** | The number to round |
| digits | number | **false** | The number of decimal places. **Defaults to `0` (whole numbers)** |

### Example - Calculate average number of users on the site

When computing averages, we sometimes wish to round to give a more realistic number. It may not be clear to say, `There are 3.5 users on the site`, so instead, we round to the nearest integer. 

Consider the following documents:

```json
{
    "total_users_online": 1093
},
{
    "total_users_online": 942
},
{
    "total_users_online": 120
},
{
    "total_users_online": 123
},
```

First, we'll take the average.

```dataprime
aggregate avg(total_users_online) as average_users_online
```

However, this value comes with a decimal place, which may not be how we wish to present the data. To make a realistic figure, we can use `round`. 

```dataprime
aggregate round(avg(total_users_online)) as average_users_online
```

Instead of a value of `569.5`, we now have a value of `570`, which is more appropriate when counting discrete values, like users.

### Example - Cleaning up latency calculations from traces

When rendering latency in milliseconds, some of our calculations, such as an average, may result in lengthy decimal fields. We can use `round` to round a value to a desired number of decimal places. A very common query where users run into this is when understanding their slowest paths:

```dataprime
top 20 $l.operationName by avg($m.duration)
```

The results from these queries often have long, complex decimal places. We can fix this, using `round`:

```dataprime
top 20 $l.operationName by avg($m.duration) as avg_duration
| replace avg_duration with avg_duration.round(2)
```

Now, the calculations will present durations to two decimal places.
