---
title: "mod"
date: "2024-10-13"
description: "Calculate division remainder using Modulus"
---

# mod - Calculate division remainder using Modulus

The `mod` function returns the modulus (remainder) of `number` divided by `divisor`.

## Syntax

```dataprime
mod(number: number, divisor: number): number
```

### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| number | number | **true** | The number whose modulus we seek to find |
| divisor | number | **true** | The modulo value we will use, for example a divisor of `2` is the equivalent of calculating `number % 2` |

### Example - Check if a value is even or odd

If we have a numeric value `val`, we can use modulus to test if the number is odd or even.

```dataprime
create is_even from (mod(val, 2) == 0)
```

This will set `is_even` to `true` if the number is even (if the division remainder of 2 is 0), or `false` if the number is odd.
