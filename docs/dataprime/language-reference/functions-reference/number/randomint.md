---
title: "randomInt"
date: "2024-10-13"
description: "Generate a pseudorandom integer"
---

# randomInt - Generate a pseudorandom integer

`randomInt` will create a pseudorandom number between `0` and `n` exclusively. 

!!! note
    `randomInt` is not a cryptographically secure source of randomness.

## Syntax

```dataprime
randomInt(upperBound: number): number
```

### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| upperBound | number | **true** | The highest value the random number can be, exclusively. |

### Example - Basic usage

To create a random number from 0 to 99 and add it as a field named `random_num` to a document, one can simple combine `randomInt` with the `create` command:

```dataprime
create random_num from randomInt(100)
```
