---
title: "toBase"
date: "2024-10-13"
description: "Change base of numerical value"
---

# toBase - Change base of numerical value

The `toBase` function will render out a `string` representation of a `number`.

## Syntax

```dataprime
toBase(number: number, radix: number): string
```

### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| number | number | **true** | The number to convert into its string representation |
| radix | number | **true** | The base for the new stringified number. e.g hexidecimal is `16` |

### Example - Converting a number to hexidecimal

Consider the following document:

```json
{
    "val": 10
}
```

If we wish to convert `val` into a hex representation, we can use `toBase`:

```dataprime
create val_hex from val.toBase(16)
```

This results in the following document:

```json
{
    "val": 10,
    "val_hex": "a"
}
```

### Try it yourself

Open the Explore screen in Coralogix and paste this query:

```dataprime
create val from 10
| create val_hex from val.toBase(16)
```
