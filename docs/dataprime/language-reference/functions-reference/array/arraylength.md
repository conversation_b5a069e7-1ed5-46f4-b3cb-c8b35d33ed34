---
title: "arrayLength"
date: "2024-10-13"
description: "Get number of elements in an array"
---

# arrayLength - Get number of elements in an array

`arrayLength` gets the number of elements in a given array.

## Syntax

```dataprime
arrayLength(array: array<any>): number
```

### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| array | array of type any | **true** | The array to test |

### Example - Find out how many jobs are waiting

Consider the following document:

```json
{
    "loading_jobs_waiting": ["loading_job1", "loading_job2", "loading_job3"]
}
```

If we wish to know how many loading jobs are waiting to be executed, we can use `arrayLength`:

```dataprime
create waiting_jobs from loading_jobs_waiting.arrayLength()
```

This results in the following document:

```json
{
    "loading_jobs_waiting": ["loading_job1", "loading_job2", "loading_job3"],
    "waiting_jobs": 3
}
```