---
title: "setDiff"
date: "2024-10-13"
description: "Returns difference between two arrays"
---

# setDiff - Returns difference between two arrays

`setDiff` returns the set difference of two arrays. The resulting array includes elements from `array1` that are not in `array2`.

## Syntax

```dataprime
setDiff(array1: array<T>, array2: array<T>): array<T>
```
### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| array1 | array of type T | **true** | T must be either `string`, `bool`, `number`, `interval`, `timestamp`, `regexp` or `enum` |
| array2 | array of type T | **true** | T must be either `string`, `bool`, `number`, `interval`, `timestamp`, `regexp` or `enum` |

### Example - Compare IP addresses & Allow Lists

Consider the following documents:

```json
{
    "ip_address": "***********",
    "path": "/home"
},
{
    "ip_address": "***********",
    "path": "/checkout"
},
{
    "ip_address": "***********",
    "path": "/home"
},
{
    "ip_address": "**********",
    "path": "/home"
}
```

We want to see if all of the IP addresses listed in the above traffic, are in an allow list. First, we'll use a `collect` aggregation to create a new array.

```dataprime
groupby path collect(ip_address) as ip_addresses
```

This results in the following documents:

```json
{
    "path": "/home",
    "ip_addresses": ["***********", "***********", "**********"]
},
{
    "path": "/checkout",
    "ip_addresses": ["***********"]
}
```

We now know which IP addresses accessed which paths. We can now compare them against our known allow list:

```dataprime
create unauthorized_ip_addresses from ip_addresses.setDiff(["***********", "***********"])
```

This results in the following documents:

```json
{
    "path": "/home",
    "ip_addresses": ["***********", "***********", "**********"],
    "unauthorized_ip_addresses": ["**********"]
},
{
    "path": "/checkout",
    "ip_addresses": ["***********"],
    "unauthorized_ip_addresses": []
}
```

As we can see, the path value `/home` has been accesed by one IP address that is not part of our allow list. 
