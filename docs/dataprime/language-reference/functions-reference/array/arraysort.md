---
title: "arraySort"
date: "2024-10-13"
description: "Sorts array according to arguments"
---

# arraySort - Sorts array according to arguments

The `arraySort` function will sort an array acccording to some arguments, and return the sorted array.

## Syntax

```dataprime
arraySort(array: array<T>, desc: bool?, nullsFirst: bool?): array<T>
```
### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| array | array of type T | **true** | T must be either `string`, `bool`, `number`, `interval`, `timestamp`, `regexp` or `enum` |
| desc | bool | **false** | when `true`, the array is sorted in reverse (descending) order. Must be a literal. **Defaults to false**|
| nullsFirst | bool | **false** | when `true`, nulls will appear at the start of the output. Must be a literal. **Defaults to false** |

### Example - Sorting names by alphabetical order

Consider the following document:

```json
{
    "names": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]
}
```

Sorting these names by alphabetical order is straight forward:

```dataprime
replace names with names.arraySort()
```

This results in the following document:

```json
{
    "names": ["Adam", "Chris", "John", "Jose"]
}
```