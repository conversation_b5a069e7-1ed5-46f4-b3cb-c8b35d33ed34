---
title: "arrayReplaceAll"
date: "2024-10-13"
description: "Replaces all instances of a value with a new value"
---

# arrayReplaceAll - Replaces all instances of a value with a new value

`arrayReplaceAll` will replace all instances of a value in a given array with a new value. 

## Syntax

```dataprime
arrayReplaceAll(array: array<T>, value: T, newValue: T): array<T>
```
### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| array | array of type T | **true** | T must be either `string`, `bool`, `number`, `interval`, `timestamp`, `regexp` or `enum` |
| value | T | **true** | T must be either `string`, `bool`, `number`, `interval`, `timestamp`, `regexp` or `enum` |
| newValue | T | **true** | T must be either `string`, `bool`, `number`, `interval`, `timestamp`, `regexp` or `enum` |

### Example - Cleaning up old values in lists

Consider the following two documents:

```json
{
    "values": ["NewVal1", "NewVal2", "NewVal3"]
},
{
    "values": ["OldVal1", "NewVal2", "NewVal3"]
}
```

We can see that there has been a schema change between `NewVal1` and `OldVal1`. To overcome this, we can use `arrayReplaceAll` to replace the old schema with the new schema, before we continue processing.

```dataprime
create updated_values from values.arrayReplaceAll('OldVal1', 'NewVal1')
```

This results in the following documents:

```json
{
    "values": ["NewVal1", "NewVal2", "NewVal3"]
},
{
    "values": ["NewVal1", "NewVal2", "NewVal3"]
}
```