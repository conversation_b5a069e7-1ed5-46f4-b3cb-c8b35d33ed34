---
title: "setEqualsTo"
date: "2024-10-13"
description: "Check equality of two sets"
---

# setEqualsTo - Check equality of two sets

`setEqualsTo` returns true if `array1` has the same elements as `array2`.

!!! note
    When comparing `array1` and `array2`, duplicates will be discarded. This means two arrays of differing lengths but the same unique elements will be considered equal.

## Syntax

```dataprime
setEqualsTo(array1: array<T>, array2: array<T>): bool
```

### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| array1 | array of type T | **true** | T must be either `string`, `bool`, `number`, `interval`, `timestamp`, `regexp` or `enum` |
| array2 | array of type T | **true** | T must be either `string`, `bool`, `number`, `interval`, `timestamp`, `regexp` or `enum` |

### Example - Basic usage

Consider the following document:

```json
{
    "array_1": ["val1", "val1", "val2", "val2", "val3"],
    "array_2": ["val1", "val2", "val3", "val3", "val3"]
}
```

We can see that while these two arrays have different counts of the values, they both possess the _same unique values_. We can check this, using `setEqualsTo`:

```dataprime
create arrays_equal from array_1.setEqualsTo(array_2)
```

This results in the following document:

```json
{
    "array_1": ["val1", "val1", "val2", "val2", "val3"],
    "array_2": ["val1", "val2", "val3", "val3", "val3"],
    "arrays_equal": true
}
```