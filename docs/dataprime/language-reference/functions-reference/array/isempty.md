---
title: "isEmpty"
date: "2024-10-13"
description: "Check if array contains no elements"
---

# isEmpty - Check if array contains no elements

Returns true if the array is empty (contains no elements).

## Syntax

```dataprime
isEmpty(array: array<T>): bool
```

### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| array | array of type T | **true** | T must be either `string`, `bool`, `number`, `interval`, `timestamp`, `regexp` or `enum` |

### Example - Basic usage

Checking if an array is empty is a simple method call against that array:

```dataprime
create is_empty from my_array.isEmpty()
```

This will produce a new field, `is_empty` which is a boolean value indicating if the array is or isn't empty.

### Example - Shortcutting execution using isEmpty

If we want to perform some operation on our array, we can use `isEmpty` and `if` to optimize our query. Consider the following documents:

```json
{
    "names": ["<PERSON>", "<PERSON>", "<PERSON>"]
},
{
    "names": []
}
```

If we wish to build a string from these values, we can save some cycles by checking if the array `isEmpty` and only continuing if it is:

```dataprime
create msg from if(!names.isEmpty(), names.arrayJoin(','), '')
```

Now `arrayJoin` is only invoked if the array value for `names` has any elements.

### Try it yourself

Open up your explore screen and paste in the following command:

```dataprime
create names from ['Job 1', 'Job 2', 'Job 4']
| create msg from if(!names.isEmpty(), names.arrayJoin(','), '')
```