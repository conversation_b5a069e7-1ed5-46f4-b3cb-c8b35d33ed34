---
title: "arrayReplaceAt"
date: "2024-10-13"
description: "Replaces element at specific position"
---

# arrayReplaceAt - Replaces element at specific position

`arrayReplaceAt` will replace an element at a specific position and return the modified array.

## Syntax

```dataprime
arrayReplaceAt(array: array<T>, position: number, value: T): array<T>
```

### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| array | array of type T | **true** | T must be either `string`, `bool`, `number`, `interval`, `timestamp`, `regexp` or `enum` |
| position | number | **true** | The index at which to replace the element in the array |
| value | T | **true** | T must be either `string`, `bool`, `number`, `interval`, `timestamp`, `regexp` or `enum` |

### Example - Cleaning up an old value in a list

Consider the following two documents:

```json
{
    "values": ["NewVal1", "NewVal2", "NewVal3"]
},
{
    "values": ["OldVal1", "NewVal2", "NewVal3"]
}
```

We can see that there has been a schema change between `NewVal1` and `OldVal1`. To overcome this, we can use `arrayReplaceAt` to replace the old schema with the new schema, before we continue processing.

```dataprime
create updated_values from values.arrayReplaceAt(0, 'NewVal1')
```

This results in the following documents:

```json
{
    "values": ["NewVal1", "NewVal2", "NewVal3"]
},
{
    "values": ["NewVal1", "NewVal2", "NewVal3"]
}
```