---
title: "arrayInsertAt"
date: "2024-10-13"
description: "Add element at specified position in array"
---

# arrayInsertAt - Add element at specified position in array

`arrayInsertAt` will add an element at a given `position`. 

## Syntax

```dataprime
arrayInsertAt(array: array<T>, position: number, value: T): array<T>
```

### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| array | array of type T | **true** | T must be either `string`, `bool`, `number`, `interval`, `timestamp`, `regexp` or `enum` |
| position | number | **true** | The index at which an element should be inserted |
| value | T | **true** | T must be either `string`, `bool`, `number`, `interval`, `timestamp`, `regexp` or `enum` |

### Example - Adding a job in the middle of a queue

Consider the following document:

```json
{
    "values": ["Job 1", "Job 2", "Job 4"]
}
```

Inserting an item in a given point in an array is as simple as:

```dataprime
replace values with values.arrayInsertAt(2, 'Job 3')
```

This results in the following document:

```json
{
    "values": ["Job 1", "Job 2", "Job 3", "Job 4"]
}
```

### Try it yourself

Open up your explore screen and enter the following command:

```dataprime
create values from ['Job 1', 'Job 2', 'Job 4']
| create updated_values from values.arrayInsertAt(2, 'Job 3')
```