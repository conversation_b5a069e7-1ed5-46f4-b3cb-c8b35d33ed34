---
title: "arrayRemove"
date: "2024-10-13"
description: "Remove given element from array"
---

# arrayRemove - Remove given element from array

`arrayRemove` will remove a given element from an array and return the modified array.

## Syntax

```dataprime
arrayRemove(array: array<T>, element: T): array<T>
```

### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| array | array of type T | **true** | T must be either `string`, `bool`, `number`, `interval`, `timestamp`, `regexp` or `enum` |
| element | T | **true** | T must be either `string`, `bool`, `number`, `interval`, `timestamp`, `regexp` or `enum` |

### Example - Removing a job from the middle of a queue

Consider the following document:

```json
{
    "values": ["Job 1", "Job 2", "Job 3", "Job 4"]
}
```

Removing an item from the array can be done as follows:

```dataprime
replace values with values.arrayRemove('Job 3')
```

This results in the following document:

```json
{
    "values": ["Job 1", "Job 2", "Job 4"]
}
```

### Try it yourself

Open up your explore screen and enter the following command:

```dataprime
create values from ['Job 1', 'Job 2', 'Job 3', 'Job 4']
| create updated_values from values.arrayRemove('Job 3')
```