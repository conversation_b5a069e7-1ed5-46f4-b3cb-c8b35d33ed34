---
title: "min"
date: "2024-10-13"
description: "Find the smallest value in a set of numbers"
---

# min - Find the smallest value in a set of numbers

The `min` function will return the smallest number in the set of numbers it receives.

## Syntax

```dataprime
min(value: number, ...values: number): number
```

### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| value | number | **true** | The first numerical value to test |
| ...values | number | **true** | All subsequent numerical values |

### Example - Quickest latency

Consider the following document:

```json
{
    "path": "/home",
    "latency": 320
},
{
    "path": "/checkout",
    "latency": 5000
}
```

We want to understand the fastest latency per page. We can do this using the `bottom` command, and the `min` function.

```dataprime
bottom 20 path by min(latency)
```

This will give the 20 pages that had the single fastest response, as they appear in the log.
