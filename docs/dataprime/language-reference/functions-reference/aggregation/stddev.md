---
title: "stddev"
date: "2024-10-13"
description: "Calculate standard deviation"
---

# stddev - Calculate standard deviation

Computes the standard deviation of a numerical expression in the group.

## Syntax

```dataprime
stddev(expression: number): number
```

### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| expression | number | **true** | The expression whose standard deviation we seek |

### Example - Calculate standard deviation of API using trace Data

Traces always come with an associated field `duration` as part of their metadata. This value is a `number` and understanding its standard deviation is powerful for understanding the normal variance of API performance.

```dataprime
groupby $m.operationName calculate stddev($m.duration)
```

### Example - Calculate standard deviation of HTTP byte responses for request profiling

Understanding the normal variance of your HTTP response sizes will give a clear metric for indicating when HTTP responses are non-standard. Non-standard HTTP response volumes can be a sign of data exfiltration.

```dataprime
groupby $d.path aggregate stddev($d.http_resp_bytes)
```


