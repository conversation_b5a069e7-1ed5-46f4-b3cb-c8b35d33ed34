---
title: "count_if"
date: "2024-10-13"
description: "Count values that satisfy a given condition"
---

# count_if - Count values that satisfy a given condition

Counts non-null expression values on rows which satisfy condition. Extremely useful for taking multi-dimensional measurements of a given value.

!!! note
    `count_if` is an aggregation function, so should be used in conjunction with a grouping keyword, like `groupby`. 

## Syntax

```dataprime
count_if(condition: bool, expression: any?)
```

### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| condition | bool | **true** | `bool` value indicating if row should be counted or not |
| expression | any | **false** | The non-null value to be counted |

### Example - Tracking high duration HTTP requests in traces

`count_if` is very useful when we're looking to aggregate on a specific field, like `operationName`, but we are only interested in a subset of the values. Consider the following traces:

```json
{
    "operationName": "/home",
    "duration": 567
},
{
    "operationName": "/about",
    "duration": 512
},
{
    "operationName": "/store",
    "duration": 33
}
```

Our goal is to count the number of requests, by `operationName`, that exceed `500ms`. To do this, we can use `count_if`:

```dataprime
groupby $l.operationName aggregate count_if($m.duration > 500) as high_duration_request_count
```

### Example - Tracking high duration HTTP requests with some error present

`count_if` is able to assess multiple conditions, and seek `non-null` fields as part of its calculation. For example, let's say if our HTTP request contains an error, then a field called `http_error` is populated, otherwise it is null. We can only count erroring documents by factoring this in:

```dataprime
groupby $d.path aggregate count_if($d.duration > 500, $d.http_error) as $d.high_duration_error_requests
```

In a single query, we're finding our slowest, most impactful errors. 

!!! note
    If the second field is left empty, then `count_if` will only use the condition in the first argument to decide if a given event should be counted.


