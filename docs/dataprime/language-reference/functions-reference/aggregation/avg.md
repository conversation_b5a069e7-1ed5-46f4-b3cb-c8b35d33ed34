---
title: "avg"
date: "2024-10-13"
description: "Compute the average of a numerical field"
---

# avg - Compute the average of a numerical field

The `avg` function computes the average (or _mean_) value for a given numerical field.

!!! note
    The input must be a number. If it is a string, it can be parsed into a number using a cast. 

## Syntax

```dataprime
avg(expression: number): number
```

### Arguments

| Name  | Type | Required | Description |
|-------|------|----------|--------------|
| expression | number | **true** | An expression that returns a `number` |

### Example - Computing average HTTP request latency for each path

In this example, we're going to group our HTTP traffic logs by a given URL, which is available in a field `path`. We will then perform an aggregation to  calculate the average duration for each of the path values.

```dataprime
groupby $d.path aggregate avg($d.duration) as average_duration_for_path
```

!!! note
    This type of aggregation removes all fields besides the grouping key and any calculated aggregations. In effect, our resultant document looks like this:

```json
{
    "path": "/home",
    "average_duration_for_path": 500
}
```




