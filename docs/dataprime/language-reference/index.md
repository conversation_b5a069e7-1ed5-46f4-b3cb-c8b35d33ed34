---
title: "DataPrime language overview"
date: "2024-10-13"
description: "The full glossary of the DataPrime Query Language"
---

# DataPrime language overview

The following documents provide the syntax, usage and use cases for every command and function in DataPrime. There are some conventions in the following documents:

* Be aware of the _Deprecated_ flag at the top of a file. If you see this, please look for the recommended alternative. Where possible, avoid using any deprecated features.
* Some commands have different syntax depending on use case. Be sure you know what you want to do, so you can choose the best syntax

Explore the different commands and functions in the navigation bar. There are use cases, syntax, worked examples and more, for each piece of functionality that DataPrime has to offer.