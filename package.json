{"name": "coralogix-developer-portal", "version": "1.0.0", "description": "What is this? This is a copy of the documentation website hosted under https://coralogix.com/docs/. Instead of using Wordpress, the site is built with MKDocs.", "directories": {"doc": "docs"}, "scripts": {"init": "python3 -m venv venv && npm run install-deps && npm run init-submodules", "dev": "(source venv/bin/activate && SITE_URL=http://127.0.0.1:8000/docs/ NODE_ENV=development trap 'kill 0' SIGINT; node esbuild-dev.mjs & python3 -m mkdocs serve --watch-theme)", "prod": "NODE_ENV=production IS_PROD=true node esbuild-prod.mjs && bash -c 'source venv/bin/activate && python3 -m mkdocs build'", "publish": "aws s3 sync site s3://cx-mkdocs/ --profile cx-prod-marketing --size-only", "install-deps": "npm i && bash -c 'source venv/bin/activate && pip3 install -r requirements.txt'", "init-submodules": "bash -c 'source venv/bin/activate && python3 submodules_init.py'", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/coralogix/documentation.git"}, "author": "Coralogix", "license": "ISC", "bugs": {"url": "https://github.com/coralogix/documentation/issues"}, "homepage": "https://github.com/coralogix/documentation", "devDependencies": {"@eslint/js": "^9.15.0", "@types/anchor-js": "^5.0.3", "@types/node": "^22.9.1", "@types/rx": "^4.1.4", "autoprefixer": "^10.4.20", "esbuild": "^0.24.0", "esbuild-envfile-plugin": "^1.0.7", "esbuild-sass-plugin": "^3.3.1", "eslint": "^9.15.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "globals": "^15.12.0", "postcss": "^8.4.49", "postcss-minify": "^1.1.0", "prettier": "^3.3.3", "typescript-eslint": "^8.15.0"}, "dependencies": {"@intercom/messenger-js-sdk": "^0.0.14", "@popperjs/core": "^2.11.8", "@segment/analytics-next": "^1.76.0", "anchor-js": "^5.0.0"}}