name: ci 
on:
  workflow_call:
  push:
    branches:
      - master 
      - main
      - ci-fixes2
      - ci-fixes4
permissions:
  contents: write
jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: s3
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Configure Git Credentials
        run: |
          git config user.name github-actions[bot]
          git config user.email 41898282+github-actions[bot]@users.noreply.github.com
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
      - uses: actions/setup-python@v5
        with:
          python-version: 3.x
      - run: echo "cache_id=$(date --utc '+%V')" >> $GITHUB_ENV 
      - uses: actions/cache@v4
        with:
          key: mkdocs-material-${{ env.cache_id }}
          path: .cache
          restore-keys: |
            mkdocs-material-
      - run: npm run init
      - name: Dependency installation
        run: |
          npm run install-deps
        shell: bash
      - name: Build
        run: |
          SITE_URL=${{ vars.SITE_URL }} npm run prod
        shell: bash
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-north-1
      - name: Sync site to S3
        run: |
          aws s3 sync site s3://${{ secrets.CX_DOCS_S3_BUCKET }}/ --size-only
          aws s3 sync site s3://${{ secrets.CX_DOCS_S3_BUCKET }}/ --exclude "*" --include "*.html" --include "*.css*" --include "*.js*"
      - name: Invalidate cloudfront to clear cache
        run: |
          aws cloudfront create-invalidation --distribution-id ${{ secrets.CX_DOCS_CF_ID }} --paths "/*"
