#!/usr/bin/env python3
import json
import subprocess
import os
import re
import requests
import shutil

# This script synchronizes external repositories and processes markdown files
# Uses GitHub API instead of git clone for unification and traffic savings
# The external directory is completely ignored by git in the main repository

def run_bash_command(command):
    try:
        print(f"Running command: {command}")
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"Command running error: {e}")
        return None

def download_file_via_api(repo_url, file_path, local_file_path):
    # Downloads individual file via GitHub API with caching
    namespace, repo_name = get_repo_info_from_url(repo_url)
    if not namespace or not repo_name:
        return False

    api_url = f"https://api.github.com/repos/{namespace}/{repo_name}/contents/{file_path.strip('/')}"

    try:
        response = requests.get(api_url)
        if response.status_code == 200:
            import base64
            file_info = response.json()
            content = base64.b64decode(file_info['content']).decode('utf-8')

            # Check if file exists and has the same SHA (no changes)
            cache_dir = '.cache/external_repos'
            os.makedirs(cache_dir, exist_ok=True)
            cache_file = os.path.join(cache_dir, f"{namespace}_{repo_name}_{file_path.replace('/', '_')}.sha")
            if os.path.exists(local_file_path) and os.path.exists(cache_file):
                with open(cache_file, 'r') as f:
                    cached_sha = f.read().strip()
                if cached_sha == file_info['sha']:
                    print(f"File {file_path} unchanged (cached)")
                    return True

            # File changed or doesn't exist, download it
            os.makedirs(os.path.dirname(local_file_path), exist_ok=True)
            with open(local_file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            # Save SHA for caching
            with open(cache_file, 'w') as f:
                f.write(file_info['sha'])

            print(f"Downloaded {file_path} via GitHub API")
            return True
    except Exception as e:
        print(f"Failed to download {file_path} via API: {e}")

    return False

def create_repo_metadata(repo_url, local_path, docs, processed_files=None):
    # Creates metadata file for repository information
    namespace, repo_name = get_repo_info_from_url(repo_url)
    if not namespace or not repo_name:
        return

    # Use processed files if provided, otherwise original ones
    actual_files = processed_files if processed_files else docs

    metadata = {
        "repo_name": repo_name,
        "repo_url": repo_url,
        "namespace": namespace,
        "sync_method": "github_api",
        "last_sync": __import__('datetime').datetime.now().isoformat(),
        "original_files": docs,
        "actual_files": actual_files
    }

    metadata_file = os.path.join(local_path, ".repo_metadata.json")
    os.makedirs(os.path.dirname(metadata_file), exist_ok=True)

    with open(metadata_file, 'w') as f:
        __import__('json').dump(metadata, f, indent=2)

def sync_repo_unified(repo_url, local_path, docs):
    # Unified synchronization: always uses GitHub API for consistency
    namespace, repo_name = get_repo_info_from_url(repo_url)
    if not namespace or not repo_name:
        print(f"Could not parse repo URL: {repo_url}")
        return

    print(f"Syncing {repo_name} via GitHub API...")

    # Download each file via API
    for doc in docs:
        file_path = doc.strip('/')
        local_file_path = os.path.join(local_path, file_path)

        if download_file_via_api(repo_url, file_path, local_file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ Failed to download {file_path}")

    # Metadata will be created later with actual file paths

def sync_repo_optimized(repo_url, local_path, docs):
    # Optimized sync: use API for single files, Git for multiple files.
    # If only one file and it's a markdown file, try API first
    if len(docs) == 1 and docs[0].endswith('.md'):
        file_path = docs[0]
        local_file_path = os.path.join(local_path, file_path.strip('/'))

        if download_file_via_api(repo_url, file_path, local_file_path):
            print(f"Downloaded {file_path} via GitHub API (faster)")
            return
        else:
            print(f"API download failed, falling back to Git clone")

    # Fall back to Git clone for multiple files or API failure
    clone_or_update_repo(repo_url, local_path, docs)

def check_git_files_changed(repo_url, local_path, docs):
    # Check if any files changed using git ls-remote.
    namespace, repo_name = get_repo_info_from_url(repo_url)
    if not namespace or not repo_name:
        return True  # If can't check, assume changed

    cache_dir = '.cache/external_repos'
    os.makedirs(cache_dir, exist_ok=True)
    cache_file = os.path.join(cache_dir, f"{namespace}_{repo_name}_git_head.sha")

    # Get current HEAD from remote
    result = run_bash_command(f"git ls-remote {repo_url} HEAD")
    if not result:
        return True

    current_head = result.split()[0]

    # Check cached HEAD
    if os.path.exists(cache_file):
        with open(cache_file, 'r') as f:
            cached_head = f.read().strip()
        if cached_head == current_head:
            print(f"Repository {repo_name} unchanged (cached)")
            return False

    # Save new HEAD
    with open(cache_file, 'w') as f:
        f.write(current_head)

    return True

def clone_or_update_repo(repo_url, local_path, docs):
    # Clone or update a repository and set up sparse checkout for specific files.
    if os.path.exists(local_path):
        # Check if repository changed before updating
        if not check_git_files_changed(repo_url, local_path, docs):
            return  # No changes, skip update

        print(f"Updating existing repository: {local_path}")
        # Update existing repository
        run_bash_command(f"git -C {local_path} fetch origin")
        run_bash_command(f"git -C {local_path} reset --hard origin/master")
    else:
        print(f"Cloning repository: {repo_url} to {local_path}")
        # Clone the repository with shallow clone and sparse checkout from the start
        run_bash_command(f"git clone --depth 1 --filter=blob:none --no-checkout {repo_url} {local_path}")

    # Set up sparse checkout for only the needed files
    run_bash_command(f"git -C {local_path} config core.sparseCheckout true")

    # Create sparse-checkout file
    sparse_checkout_file = os.path.join(local_path, ".git", "info", "sparse-checkout")
    os.makedirs(os.path.dirname(sparse_checkout_file), exist_ok=True)

    with open(sparse_checkout_file, "w") as f:
        for doc in docs:
            f.write(f"{doc.strip('/')}\n")

    # Apply sparse checkout
    run_bash_command(f"git -C {local_path} read-tree -m -u HEAD")

    # If this is a fresh clone with --no-checkout, checkout the files now
    if not os.path.exists(os.path.join(local_path, ".git", "HEAD")):
        run_bash_command(f"git -C {local_path} checkout")

def get_repo_info_from_url(repo_url):
    # Extract namespace and repo name from GitHub URL.
    match = re.search(r'github\.com[:/](.*?)/(.*?)(\.git)?$', repo_url)
    if match:
        namespace = match.group(1)
        repo_name = match.group(2)
        return namespace, repo_name
    return None, None

def extract_block(content, start_marker, end_marker):
    # Extracts content between HTML comments
    # For example: <!-- tag --> content <!-- /tag -->
    pattern = rf'<!-- {start_marker} -->(.*?)<!-- {end_marker} -->'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1).strip() if match else ""

def extract_descriptions(content):
    # Extracts description blocks with their attributes from HTML comments
    # Format: <!-- description id="ID" title="TITLE" examples_path="PATH" --> content <!-- /description -->
    descriptions = []

    pattern = r'<!-- description id="(.*?)" title="(.*?)" examples_path="(.*?)" -->(.*?)<!-- /description -->'

    for match in re.finditer(pattern, content, re.DOTALL):
        descriptions.append({
            "id": match.group(1),
            "title": match.group(2),
            "examples_path": match.group(3),
            "content": match.group(4).strip()
        })

    return descriptions

def extract_example(content, example_id):
    # Extracts example blocks with specific ID from HTML comments
    # Format: <!-- example id="ID" --> content <!-- /example -->
    pattern = rf'<!-- example id="{example_id}" -->(.*?)<!-- /example -->'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1).strip() if match else ""

def extract_split_tags(content):
    # Extracts split blocks with their attributes from HTML comments
    # Format: <!-- split title="TITLE" path="PATH" --> content <!-- /split -->
    splits = []

    pattern = r'<!-- split title="(.*?)" path="(.*?)" -->(.*?)<!-- /split -->'

    for match in re.finditer(pattern, content, re.DOTALL):
        splits.append({
            "title": match.group(1),
            "path": match.group(2),
            "content": match.group(3).strip()
        })

    return splits

def process_split_tags(file_path, content):
    # Processes split tags and creates new files
    splits = extract_split_tags(content)
    base_dir = os.path.dirname(file_path)

    for split in splits:
        # Create output path relative to original file
        output_path = os.path.join(base_dir, split["path"])

        # Ensure directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Create content with title at the beginning
        generated_content = f"""# {split['title']}

{split['content']}
""".strip()

        # Write content to new file
        with open(output_path, 'w', encoding='utf-8') as output_file:
            output_file.write(generated_content)

        print(f"Generated split file: {output_path}")

    # Remove split tags from original content
    return re.sub(r'<!-- split title=".*?" path=".*?" -->.*?<!-- /split -->', '', content, flags=re.DOTALL)

def fetch_file_from_github(url):
    try:
        response = requests.get(url)
        if response.status_code == 200:
            return response.text
        else:
            print(f"Failed to fetch {url}, status code: {response.status_code}")
            return None
    except requests.RequestException as e:
        print(f"Error fetching {url}: {e}")
        return None

def convert_github_callouts_to_mkdocs_style(content: str) -> str:
    # Converts GitHub-style callouts to MkDocs admonition format
    # For example: > [!NOTE] becomes !!! note
    lines = content.splitlines()
    result = []
    i = 0

    callout_start_pattern = re.compile(r"^>\s*\[!(NOTE|TIP|IMPORTANT|WARNING|CAUTION)\]\s*$", re.IGNORECASE)
    callout_line_pattern = re.compile(r"^>\s?(.*)$")

    while i < len(lines):
        line = lines[i]
        match = callout_start_pattern.match(line.strip())
        if match:
            tag = match.group(1).lower()
            result.append(f"!!! {tag}\\n")
            i += 1
            while i < len(lines):
                next_line = lines[i]
                stripped = next_line.strip()
                if stripped == "":
                    result.append("")
                    i += 1
                    break
                match_line = callout_line_pattern.match(next_line)
                if match_line:
                    result.append(f"    {match_line.group(1)}")
                else:
                    result.append(f"    {next_line}")
                i += 1
        else:
            result.append(line)
            i += 1

    return "\n".join(result)

def process_file(file_path, repo_url, doc):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        # Remove content within delete blocks
        # Format: <!-- delete --> content to be removed <!-- /delete -->
        content = re.sub(r'<!-- delete -->.*?<!-- /delete -->', '', content, flags=re.DOTALL)

        # Update static file paths
        content = re.sub(r'\(\./static/(.*?)\)', rf'({repo_url}{os.path.dirname(doc)}/static/\1)', content)

        content = convert_github_callouts_to_mkdocs_style(content)

        # Process split tags and create new files
        content = process_split_tags(file_path, content)

        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)

        print(f"Processed {file_path}")

        start_content = extract_block(content, "static-modules-readme-start-description", "/static-modules-readme-start-description")
        descriptions = extract_descriptions(content)
        end_content = extract_block(content, "static-modules-readme-end-description", "/static-modules-readme-end-description")

        base_dir = os.path.dirname(file_path)

        for desc in descriptions:
            example_url = f"{repo_url}/{desc['examples_path'].strip('/')}"
            example_content = fetch_file_from_github(example_url)
            if example_content:
                example_content = extract_example(example_content, desc["id"])
            else:
                example_content = ""

            main_doc_filename = os.path.basename(doc)

            additional_config_line = f"\nFor additional configuration options, please refer to our main [Terraform documentation](../{main_doc_filename}).\n"

            generated_content = f"""
# {desc['title']}

{start_content}

{desc['content']}

{example_content}

{end_content}

{additional_config_line}
""".strip()

            output_path = os.path.join(base_dir, "examples", f"{desc['id'].lower()}.md")
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            with open(output_path, 'w', encoding='utf-8') as output_file:
                output_file.write(generated_content)

            print(f"Generated file: {output_path}")

    except FileNotFoundError:
        print(f"File {file_path} not found.")
    except Exception as e:
        print(f"Error processing {file_path}: {e}")

def main():
    # Main function to process repositories based on configuration in external_repos.json
    json_file_path = "external_repos.json"

    try:
        with open(json_file_path, 'r') as file:
            data = json.load(file)

        for item in data:
            local_path = item["local_path"]
            repo_url = item["repo_url"]
            docs = item["docs"]

            # Synchronize repository via unified method (always GitHub API)
            sync_repo_unified(repo_url, local_path, docs)

            # Determine GitHub URL for raw content access
            namespace, repo_name = get_repo_info_from_url(repo_url)

            if namespace and repo_name:
                raw_repo_url = f"https://raw.githubusercontent.com/{namespace}/{repo_name}/master"
            else:
                print(f"Could not determine namespace for {repo_url}, skipping...")
                continue

            # Process each documentation file and track actual file paths
            actual_files = []
            for doc in docs:
                file_path = os.path.join(local_path, doc.strip('/'))
                new_file_path = file_path

                # Special handling: rename /docs/ to /documents/ to avoid hosting conflicts
                # Some hosting platforms have issues with /docs/ directories
                if '/docs/' in file_path:
                    new_file_path = file_path.replace('/docs/', '/documents/')
                    os.makedirs(os.path.dirname(new_file_path), exist_ok=True)
                    if os.path.exists(file_path):
                        shutil.move(file_path, new_file_path)
                        print(f"📁 Moved: {doc} → {doc.replace('/docs/', '/documents/')} (hosting compatibility)")
                    # Update actual file path
                    actual_doc = doc.replace('/docs/', '/documents/')
                    actual_files.append(actual_doc)
                else:
                    actual_files.append(doc)

                if os.path.exists(new_file_path):
                    process_file(new_file_path, raw_repo_url, doc)
                else:
                    print(f"File not found: {new_file_path}")

            # Update metadata with actual file paths
            create_repo_metadata(repo_url, local_path, docs, actual_files)

    except FileNotFoundError:
        print(f"File {json_file_path} not found.")
    except json.JSONDecodeError as e:
        print(f"JSON parsing error: {e}")

    # Show repository status after synchronization
    print("\n" + "="*50)
    print("📊 Repository status after synchronization:")
    print("="*50)

    try:
        import subprocess
        subprocess.run(["python3", "scripts/repo_status.py"], check=True)
    except subprocess.CalledProcessError:
        print("❌ Failed to run repo_status.py")
    except FileNotFoundError:
        print("❌ File scripts/repo_status.py not found")

if __name__ == "__main__":
    main()
