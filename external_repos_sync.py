#!/usr/bin/env python3
import json
import subprocess
import os
import re
import requests
import shutil

# This script synchronizes external repositories and processes markdown files.
# It uses regular git clone instead of submodules to avoid conflicts with the main repository.
# The external directory is completely ignored by git in the main repository.

def run_bash_command(command):
    try:
        print(f"Running command: {command}")
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"Command running error: {e}")
        return None

def clone_or_update_repo(repo_url, local_path, docs):
    """Clone or update a repository and set up sparse checkout for specific files."""
    if os.path.exists(local_path):
        print(f"Updating existing repository: {local_path}")
        # Update existing repository
        run_bash_command(f"git -C {local_path} fetch origin")
        run_bash_command(f"git -C {local_path} reset --hard origin/master")
    else:
        print(f"Cloning repository: {repo_url} to {local_path}")
        # Clone the repository
        run_bash_command(f"git clone {repo_url} {local_path}")

    # Set up sparse checkout for only the needed files
    run_bash_command(f"git -C {local_path} config core.sparseCheckout true")

    # Create sparse-checkout file
    sparse_checkout_file = os.path.join(local_path, ".git", "info", "sparse-checkout")
    os.makedirs(os.path.dirname(sparse_checkout_file), exist_ok=True)

    with open(sparse_checkout_file, "w") as f:
        for doc in docs:
            f.write(f"{doc.strip('/')}\n")

    # Apply sparse checkout
    run_bash_command(f"git -C {local_path} read-tree -m -u HEAD")

def get_repo_info_from_url(repo_url):
    """Extract namespace and repo name from GitHub URL."""
    match = re.search(r'github\.com[:/](.*?)/(.*?)(\.git)?$', repo_url)
    if match:
        namespace = match.group(1)
        repo_name = match.group(2)
        return namespace, repo_name
    return None, None

def extract_block(content, start_marker, end_marker):
    # Extract content between HTML comment tags
    # For example: <!-- tag --> content <!-- /tag -->
    pattern = rf'<!-- {start_marker} -->(.*?)<!-- {end_marker} -->'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1).strip() if match else ""

def extract_descriptions(content):
    # Extract description blocks with their attributes from HTML comments
    # Format: <!-- description id="ID" title="TITLE" examples_path="PATH" --> content <!-- /description -->
    descriptions = []

    pattern = r'<!-- description id="(.*?)" title="(.*?)" examples_path="(.*?)" -->(.*?)<!-- /description -->'

    for match in re.finditer(pattern, content, re.DOTALL):
        descriptions.append({
            "id": match.group(1),
            "title": match.group(2),
            "examples_path": match.group(3),
            "content": match.group(4).strip()
        })

    return descriptions

def extract_example(content, example_id):
    # Extract example blocks with specific ID from HTML comments
    # Format: <!-- example id="ID" --> content <!-- /example -->
    pattern = rf'<!-- example id="{example_id}" -->(.*?)<!-- /example -->'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1).strip() if match else ""

def extract_split_tags(content):
    # Extract split blocks with their attributes from HTML comments
    # Format: <!-- split title="TITLE" path="PATH" --> content <!-- /split -->
    splits = []

    pattern = r'<!-- split title="(.*?)" path="(.*?)" -->(.*?)<!-- /split -->'

    for match in re.finditer(pattern, content, re.DOTALL):
        splits.append({
            "title": match.group(1),
            "path": match.group(2),
            "content": match.group(3).strip()
        })

    return splits

def process_split_tags(file_path, content):
    # Process split tags and create new files
    splits = extract_split_tags(content)
    base_dir = os.path.dirname(file_path)

    for split in splits:
        # Create the output path relative to the original file
        output_path = os.path.join(base_dir, split["path"])

        # Ensure the directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Create content with title at the beginning
        generated_content = f"""# {split['title']}

{split['content']}
""".strip()

        # Write the content to the new file
        with open(output_path, 'w', encoding='utf-8') as output_file:
            output_file.write(generated_content)

        print(f"Generated split file: {output_path}")

    # Remove split tags from the original content
    return re.sub(r'<!-- split title=".*?" path=".*?" -->.*?<!-- /split -->', '', content, flags=re.DOTALL)

def fetch_file_from_github(url):
    try:
        response = requests.get(url)
        if response.status_code == 200:
            return response.text
        else:
            print(f"Failed to fetch {url}, status code: {response.status_code}")
            return None
    except requests.RequestException as e:
        print(f"Error fetching {url}: {e}")
        return None

def convert_github_callouts_to_mkdocs_style(content: str) -> str:
    # Convert GitHub-style callouts to MkDocs admonition format
    # For example: > [!NOTE] becomes !!! note
    lines = content.splitlines()
    result = []
    i = 0

    callout_start_pattern = re.compile(r"^>\s*\[!(NOTE|TIP|IMPORTANT|WARNING|CAUTION)\]\s*$", re.IGNORECASE)
    callout_line_pattern = re.compile(r"^>\s?(.*)$")

    while i < len(lines):
        line = lines[i]
        match = callout_start_pattern.match(line.strip())
        if match:
            tag = match.group(1).lower()
            result.append(f"!!! {tag}\\n")
            i += 1
            while i < len(lines):
                next_line = lines[i]
                stripped = next_line.strip()
                if stripped == "":
                    result.append("")
                    i += 1
                    break
                match_line = callout_line_pattern.match(next_line)
                if match_line:
                    result.append(f"    {match_line.group(1)}")
                else:
                    result.append(f"    {next_line}")
                i += 1
        else:
            result.append(line)
            i += 1

    return "\n".join(result)

def process_file(file_path, repo_url, doc):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        # Remove content within delete blocks
        # Format: <!-- delete --> content to be removed <!-- /delete -->
        content = re.sub(r'<!-- delete -->.*?<!-- /delete -->', '', content, flags=re.DOTALL)

        # Update static paths
        content = re.sub(r'\(\./static/(.*?)\)', rf'({repo_url}{os.path.dirname(doc)}/static/\1)', content)

        content = convert_github_callouts_to_mkdocs_style(content)

        # Process split tags and create new files
        content = process_split_tags(file_path, content)

        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)

        print(f"Processed {file_path}")

        start_content = extract_block(content, "static-modules-readme-start-description", "/static-modules-readme-start-description")
        descriptions = extract_descriptions(content)
        end_content = extract_block(content, "static-modules-readme-end-description", "/static-modules-readme-end-description")

        base_dir = os.path.dirname(file_path)

        for desc in descriptions:
            example_url = f"{repo_url}/{desc['examples_path'].strip('/')}"
            example_content = fetch_file_from_github(example_url)
            if example_content:
                example_content = extract_example(example_content, desc["id"])
            else:
                example_content = ""

            main_doc_filename = os.path.basename(doc)

            additional_config_line = f"\nFor additional configuration options, please refer to our main [Terraform documentation](../{main_doc_filename}).\n"

            generated_content = f"""
# {desc['title']}

{start_content}

{desc['content']}

{example_content}

{end_content}

{additional_config_line}
""".strip()

            output_path = os.path.join(base_dir, "examples", f"{desc['id'].lower()}.md")
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            with open(output_path, 'w', encoding='utf-8') as output_file:
                output_file.write(generated_content)

            print(f"Generated file: {output_path}")

    except FileNotFoundError:
        print(f"File {file_path} not found.")
    except Exception as e:
        print(f"Error processing {file_path}: {e}")

def main():
    # Main function to process repositories based on configuration in external_repos.json
    json_file_path = "external_repos.json"

    try:
        with open(json_file_path, 'r') as file:
            data = json.load(file)

        for item in data:
            local_path = item["local_path"]
            repo_url = item["repo_url"]
            docs = item["docs"]

            # Clone or update the repository
            clone_or_update_repo(repo_url, local_path, docs)

            # Determine the GitHub repository URL for raw content access
            namespace, repo_name = get_repo_info_from_url(repo_url)

            if namespace and repo_name:
                raw_repo_url = f"https://raw.githubusercontent.com/{namespace}/{repo_name}/master"
            else:
                print(f"Could not determine namespace for {repo_url}, skipping...")
                continue

            # Process each documentation file specified in the configuration
            for doc in docs:
                file_path = os.path.join(local_path, doc.strip('/'))
                new_file_path = file_path

                if '/docs/' in file_path:
                    new_file_path = file_path.replace('/docs/', '/documents/')
                    os.makedirs(os.path.dirname(new_file_path), exist_ok=True)
                    if os.path.exists(file_path):
                        shutil.move(file_path, new_file_path)

                if os.path.exists(new_file_path):
                    process_file(new_file_path, raw_repo_url, doc)
                else:
                    print(f"File not found: {new_file_path}")

    except FileNotFoundError:
        print(f"File {json_file_path} not found.")
    except json.JSONDecodeError as e:
        print(f"JSON parsing error: {e}")

if __name__ == "__main__":
    main()
