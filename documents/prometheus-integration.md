# Prometheus Integration\n\nThe Coralogix Operator integrates with the [Prometheus Operator](https://prometheus-operator.dev/) PrometheusRule CRD, to simplify the transition to Coralogix.\nBy using existing monitoring configurations, the operator makes it easier to adopt Coralogix's advanced monitoring and alerting features.\n\nThe operator watches PrometheusRule resources and automatically creates Coralogix custom resources in the cluster including Alerts and RecordingRuleGroupSets.\n\n## PrometheusRule Integration\n\n### Alerts\n\nPrometheusRule alerts can be used to configure Coralogix Metric Alerts. Since Coralogix Metric Alerts provide more advanced alerting capabilities than PrometheusRule alerts, this integration is ideal for quickly setting up basic alerts. To leverage the full capabilities of Coralogix Metric Alerts, you should manage the alerts directly through the Coralogix Alert custom resource.\n\nTo enable the operator to monitor alerts in a PrometheusRule, add the following annotation to the PrometheusRule:\n\n```yaml\napp.coralogix.com/track-alerting-rules: "true"\n```\n\nThe operator will create a Coralogix Alert in the PrometheusRule namespace, for each alert in the PrometheusRule.\n\nThe following Coralogix Alert properties are derived from the PrometheusRule alerting rule:\n\n- `Alert.Spec.Name`: Set to `rule.Alert` value\n- `Alert.Spec.Description`: Set to `rule.Annotations["description"]` value\n- `Alert.Spec.EntityLabels`: Set to `rule.Labels` property\n- `Alert.Spec.Priority`: Set to `rule.Labels["severity"]` value, with the next priority mapping:\n    - `critical` -> `p1`\n    - `error` -> `p2`\n    - `warning` -> `p3`\n    - `info` -> `p4`\n    - `low` -> `p5`\n- `Alert.Spec.AlertType.MetricThreshold.OfTheLast.DynamicDuration`: Set to `rule.For` value\n- `Alert.Spec.AlertType.MetricThreshold.Rules[0].Condition.ConditionType`: Set to `moreThan`\n- `Alert.Spec.AlertType.MetricThreshold.Rules[0].Condition.Threshold`: Set to `0`\n- `Alert.Spec.AlertType.MetricThreshold.Rules[0].Condition.ForOverPct`: Set to `100`\n\nOther properties will not be overridden by the operator and can be modified directly in the Coralogix Alert resource.\n\n#### Example\n\nFor the following PrometheusRule:\n\n```yaml\napiVersion: monitoring.coreos.com/v1\nkind: PrometheusRule\nmetadata:\n  labels:\n    app.coralogix.com/track-alerting-rules: "true"\n  name: prometheus-example-rules\nspec:\n  groups:\n    - name: example.rules\n      interval: "60s"\n      rules:\n        - alert: example-alert\n          expr: vector(1) > 0\n          for: 5m\n          annotations:\n            description: "app latency alert"\n          labels:\n            severity: critical\n            slack_channel: "#observability"\n```\n\nThe following Coralogix Alert will be created:\n\n```yaml\napiVersion: coralogix.com/v1beta1\nkind: Alert\nmetadata:\n  labels:\n    app.coralogix.com/track-alerting-rules: "true"\n    app.kubernetes.io/managed-by: prometheus-example-rules\n  name: prometheus-example-rules-example-alert-0\n  namespace: default\nspec:\n  alertType:\n    metricThreshold:\n      metricFilter:\n        promql: vector(1) > 0\n      missingValues:\n        minNonNullValuesPct: 0\n        replaceWithZero: false\n      rules:\n        - condition:\n            conditionType: moreThan\n            forOverPct: 100\n            ofTheLast:\n              dynamicDuration: 5m\n            threshold: "0"\n  description: app latency alert\n  enabled: true\n  entityLabels:\n    severity: critical\n    slack_channel: '#observability'\n  name: example-alert\n  phantomMode: false\n  priority: p1\n```\n\n### Recording Rules\n\nPrometheusRule recording rules can be used to configure the Coralogix RecordingRuleGroupSet.\n\nTo enable the operator to monitor recording rules in a PrometheusRule, add the following annotation to the PrometheusRule:\n\n```yaml\napp.coralogix.com/track-recording-rules: "true"\n```\n\nThe operator will create a Coralogix RecordingRuleGroupSet in the PrometheusRule namespace, containing all the PrometheusRule's recording rules.\n\n#### Example\n\nFor the following PrometheusRule:\n\n```yaml\napiVersion: monitoring.coreos.com/v1\nkind: PrometheusRule\nmetadata:\n  labels:\n    app.coralogix.com/track-recording-rules: "true"\n  name: prometheus-example-rules\nspec:\n  groups:\n    - name: first.group\n      interval: "60s"\n      rules:\n        - record: example-record-1\n          expr: vector(1)\n        - record: example-record-2\n          expr: vector(2)\n    - name: second.group\n      interval: "60s"\n      rules:\n        - record: example-record-3\n          expr: vector(3)\n        - record: example-record-4\n          expr: vector(4)\n```\n\nThe following Coralogix RecordingRuleGroupSet will be created:\n\n```yaml\napiVersion: v1\nitems:\n  - apiVersion: coralogix.com/v1alpha1\n    kind: RecordingRuleGroupSet\n    metadata:\n      name: prometheus-example-rules\n      namespace: default\n    spec:\n      groups:\n        - intervalSeconds: 60\n          name: first.group\n          rules:\n            - expr: vector(1)\n              record: example-record-1\n            - expr: vector(2)\n              record: example-record-2\n        - intervalSeconds: 60\n          name: second.group\n          rules:\n            - expr: vector(3)\n              record: example-record-3\n            - expr: vector(4)\n              record: example-record-4\n```