# API Reference\n\nPackages:\n\n- [coralogix.com/v1beta1](#coralogixcomv1beta1)\n- [coralogix.com/v1alpha1](#coralogixcomv1alpha1)\n\n# coralogix.com/v1beta1\n\nResource Types:\n\n- [Alert](#alert)\n\n\n\n\n## Alert\n<sup><sup>[↩ Parent](#coralogixcomv1beta1 )</sup></sup>\n\n\n\n\n\n\nAlert is the Schema for the alerts API.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n      <td><b>apiVersion</b></td>\n      <td>string</td>\n      <td>coralogix.com/v1beta1</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b>kind</b></td>\n      <td>string</td>\n      <td>Alert</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b><a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.27/#objectmeta-v1-meta">metadata</a></b></td>\n      <td>object</td>\n      <td>Refer to the Kubernetes API documentation for the fields of the `metadata` field.</td>\n      <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspec">spec</a></b></td>\n        <td>object</td>\n        <td>\n          AlertSpec defines the desired state of a Coralogix Alert. For more info check - https://coralogix.com/docs/getting-started-with-coralogix-alerts/.\n\nNote that this is only for the latest version of the alerts API. If your account has been created before March 2025, make sure that your account has been migrated before using advanced features of alerts.<br/>\n          <br/>\n            <i>Validations</i>:<li>(self.alertType.logsImmediate == null && self.alertType.logsImmediate == null) || !has(self.groupByKeys): groupByKeys is not supported for this alert type</li>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertstatus">status</a></b></td>\n        <td>object</td>\n        <td>\n          AlertStatus defines the observed state of Alert<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec\n<sup><sup>[↩ Parent](#alert)</sup></sup>\n\n\n\nAlertSpec defines the desired state of a Coralogix Alert. For more info check - https://coralogix.com/docs/getting-started-with-coralogix-alerts/.\n\nNote that this is only for the latest version of the alerts API. If your account has been created before March 2025, make sure that your account has been migrated before using advanced features of alerts.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttype">alertType</a></b></td>\n        <td>object</td>\n        <td>\n          Type of alert.<br/>\n          <br/>\n            <i>Validations</i>:<li>(has(self.logsImmediate) ? 1 : 0) + (has(self.logsThreshold) ? 1 : 0) + (has(self.logsRatioThreshold) ? 1 : 0) + (has(self.logsTimeRelativeThreshold) ? 1 : 0) + (has(self.metricThreshold) ? 1 : 0) + (has(self.tracingThreshold) ? 1 : 0) + (has(self.tracingImmediate) ? 1 : 0) + (has(self.flow) ? 1 : 0) + (has(self.logsAnomaly) ? 1 : 0) + (has(self.metricAnomaly) ? 1 : 0) + (has(self.logsNewValue) ? 1 : 0) + (has(self.logsUniqueCount) ? 1 : 0) == 1: Exactly one of logsImmediate, logsThreshold, logsRatioThreshold, logsTimeRelativeThreshold, metricThreshold, tracingThreshold, tracingImmediate, flow, logsAnomaly, metricAnomaly, logsNewValue or logsUniqueCount must be set</li>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the alert<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>priority</b></td>\n        <td>enum</td>\n        <td>\n          Priority of the alert.<br/>\n          <br/>\n            <i>Enum</i>: p1, p2, p3, p4, p5<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>description</b></td>\n        <td>string</td>\n        <td>\n          Description of the alert<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>enabled</b></td>\n        <td>boolean</td>\n        <td>\n          Enable/disable the alert.<br/>\n          <br/>\n            <i>Default</i>: true<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>entityLabels</b></td>\n        <td>map[string]string</td>\n        <td>\n          Labels attached to the alert.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>groupByKeys</b></td>\n        <td>[]string</td>\n        <td>\n          Grouping fields for multiple alerts.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecincidentssettings">incidentsSettings</a></b></td>\n        <td>object</td>\n        <td>\n          Settings for the attached incidents.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroup">notificationGroup</a></b></td>\n        <td>object</td>\n        <td>\n          Where notifications should be sent to.<br/>\n          <br/>\n            <i>Validations</i>:<li>!(has(self.destinations) && has(self.router)): At most one of Destinations or Router can be set.</li>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindex">notificationGroupExcess</a></b></td>\n        <td>[]object</td>\n        <td>\n          Do not use.\nDeprecated: Legacy field for when multiple notification groups were attached.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>phantomMode</b></td>\n        <td>boolean</td>\n        <td>\n          <br/>\n          <br/>\n            <i>Default</i>: false<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecschedule">schedule</a></b></td>\n        <td>object</td>\n        <td>\n          Alert activity schedule. Will be activated all the time if not specified.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType\n<sup><sup>[↩ Parent](#alertspec)</sup></sup>\n\n\n\nType of alert.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypeflow">flow</a></b></td>\n        <td>object</td>\n        <td>\n          Flow alerts chaining multiple alerts together.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsanomaly">logsAnomaly</a></b></td>\n        <td>object</td>\n        <td>\n          Anomaly alerts for logs.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsimmediate">logsImmediate</a></b></td>\n        <td>object</td>\n        <td>\n          Immediate alerts for logs.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsnewvalue">logsNewValue</a></b></td>\n        <td>object</td>\n        <td>\n          Alerts when a new log value appears.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsratiothreshold">logsRatioThreshold</a></b></td>\n        <td>object</td>\n        <td>\n          Alerts for when a log exceeds a defined ratio.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsthreshold">logsThreshold</a></b></td>\n        <td>object</td>\n        <td>\n          Alerts for when a log crosses a threshold.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogstimerelativethreshold">logsTimeRelativeThreshold</a></b></td>\n        <td>object</td>\n        <td>\n          Alerts are sent when the number of logs matching a filter is more than or less than a threshold over a specific time window.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsuniquecount">logsUniqueCount</a></b></td>\n        <td>object</td>\n        <td>\n          Alerts for unique count changes.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypemetricanomaly">metricAnomaly</a></b></td>\n        <td>object</td>\n        <td>\n          Anomaly alerts for metrics.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypemetricthreshold">metricThreshold</a></b></td>\n        <td>object</td>\n        <td>\n          Alerts for when a metric crosses a threshold.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypetracingimmediate">tracingImmediate</a></b></td>\n        <td>object</td>\n        <td>\n          Immediate alerts for traces.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypetracingthreshold">tracingThreshold</a></b></td>\n        <td>object</td>\n        <td>\n          Alerts for when traces crosses a threshold.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.flow\n<sup><sup>[↩ Parent](#alertspecalerttype)</sup></sup>\n\n\n\nFlow alerts chaining multiple alerts together.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>enforceSuppression</b></td>\n        <td>boolean</td>\n        <td>\n          <br/>\n          <br/>\n            <i>Default</i>: false<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypeflowstagesindex">stages</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.flow.stages[index]\n<sup><sup>[↩ Parent](#alertspecalerttypeflow)</sup></sup>\n\n\n\nStages to go through.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypeflowstagesindexflowstagestype">flowStagesType</a></b></td>\n        <td>object</td>\n        <td>\n          Type of stage.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>timeframeMs</b></td>\n        <td>integer</td>\n        <td>\n          <br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>timeframeType</b></td>\n        <td>enum</td>\n        <td>\n          Type of timeframe.<br/>\n          <br/>\n            <i>Enum</i>: unspecified, upTo<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.flow.stages[index].flowStagesType\n<sup><sup>[↩ Parent](#alertspecalerttypeflowstagesindex)</sup></sup>\n\n\n\nType of stage.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypeflowstagesindexflowstagestypegroupsindex">groups</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.flow.stages[index].flowStagesType.groups[index]\n<sup><sup>[↩ Parent](#alertspecalerttypeflowstagesindexflowstagestype)</sup></sup>\n\n\n\nFlow stage grouping.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypeflowstagesindexflowstagestypegroupsindexalertdefsindex">alertDefs</a></b></td>\n        <td>[]object</td>\n        <td>\n          Alerts to group.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>alertsOp</b></td>\n        <td>enum</td>\n        <td>\n          Operation for the alert.<br/>\n          <br/>\n            <i>Enum</i>: and, or<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>nextOp</b></td>\n        <td>enum</td>\n        <td>\n          Link to the next alert.<br/>\n          <br/>\n            <i>Enum</i>: and, or<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.flow.stages[index].flowStagesType.groups[index].alertDefs[index]\n<sup><sup>[↩ Parent](#alertspecalerttypeflowstagesindexflowstagestypegroupsindex)</sup></sup>\n\n\n\nAlert references.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypeflowstagesindexflowstagestypegroupsindexalertdefsindexalertref">alertRef</a></b></td>\n        <td>object</td>\n        <td>\n          Reference for an alert, backend or Kubernetes resource<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.backendRef) != has(self.resourceRef): Exactly one of backendRef or resourceRef must be set</li>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>not</b></td>\n        <td>boolean</td>\n        <td>\n          Inversion.<br/>\n          <br/>\n            <i>Default</i>: false<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.flow.stages[index].flowStagesType.groups[index].alertDefs[index].alertRef\n<sup><sup>[↩ Parent](#alertspecalerttypeflowstagesindexflowstagestypegroupsindexalertdefsindex)</sup></sup>\n\n\n\nReference for an alert, backend or Kubernetes resource\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypeflowstagesindexflowstagestypegroupsindexalertdefsindexalertrefbackendref">backendRef</a></b></td>\n        <td>object</td>\n        <td>\n          Coralogix id reference.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.id) != has(self.name): One of id or name is required</li>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypeflowstagesindexflowstagestypegroupsindexalertdefsindexalertrefresourceref">resourceRef</a></b></td>\n        <td>object</td>\n        <td>\n          Kubernetes resource reference.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.flow.stages[index].flowStagesType.groups[index].alertDefs[index].alertRef.backendRef\n<sup><sup>[↩ Parent](#alertspecalerttypeflowstagesindexflowstagestypegroupsindexalertdefsindexalertref)</sup></sup>\n\n\n\nCoralogix id reference.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          Alert ID.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the alert.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.flow.stages[index].flowStagesType.groups[index].alertDefs[index].alertRef.resourceRef\n<sup><sup>[↩ Parent](#alertspecalerttypeflowstagesindexflowstagestypegroupsindexalertdefsindexalertref)</sup></sup>\n\n\n\nKubernetes resource reference.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the resource.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>namespace</b></td>\n        <td>string</td>\n        <td>\n          Kubernetes namespace.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsAnomaly\n<sup><sup>[↩ Parent](#alertspecalerttype)</sup></sup>\n\n\n\nAnomaly alerts for logs.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsanomalyrulesindex">rules</a></b></td>\n        <td>[]object</td>\n        <td>\n          Rules that match the alert to the data.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsanomalylogsfilter">logsFilter</a></b></td>\n        <td>object</td>\n        <td>\n          Filter to filter the logs with.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>notificationPayloadFilter</b></td>\n        <td>[]string</td>\n        <td>\n          Filter for the notification payload.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsAnomaly.rules[index]\n<sup><sup>[↩ Parent](#alertspecalerttypelogsanomaly)</sup></sup>\n\n\n\nThe rule to match the alert's conditions.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsanomalyrulesindexcondition">condition</a></b></td>\n        <td>object</td>\n        <td>\n          Condition to match to.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsAnomaly.rules[index].condition\n<sup><sup>[↩ Parent](#alertspecalerttypelogsanomalyrulesindex)</sup></sup>\n\n\n\nCondition to match to.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>minimumThreshold</b></td>\n        <td>int or string</td>\n        <td>\n          Minimum value<br/>\n          <br/>\n            <i>Default</i>: 0<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsanomalyrulesindexconditiontimewindow">timeWindow</a></b></td>\n        <td>object</td>\n        <td>\n          Time window to evaluate.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsAnomaly.rules[index].condition.timeWindow\n<sup><sup>[↩ Parent](#alertspecalerttypelogsanomalyrulesindexcondition)</sup></sup>\n\n\n\nTime window to evaluate.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>specificValue</b></td>\n        <td>enum</td>\n        <td>\n          Logs time window type<br/>\n          <br/>\n            <i>Enum</i>: 5m, 10m, 15m, 30m, 1h, 2h, 6h, 12h, 24h, 36h<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsAnomaly.logsFilter\n<sup><sup>[↩ Parent](#alertspecalerttypelogsanomaly)</sup></sup>\n\n\n\nFilter to filter the logs with.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsanomalylogsfiltersimplefilter">simpleFilter</a></b></td>\n        <td>object</td>\n        <td>\n          Simple lucene filter.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsAnomaly.logsFilter.simpleFilter\n<sup><sup>[↩ Parent](#alertspecalerttypelogsanomalylogsfilter)</sup></sup>\n\n\n\nSimple lucene filter.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsanomalylogsfiltersimplefilterlabelfilters">labelFilters</a></b></td>\n        <td>object</td>\n        <td>\n          Filter for labels.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>luceneQuery</b></td>\n        <td>string</td>\n        <td>\n          The query.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsAnomaly.logsFilter.simpleFilter.labelFilters\n<sup><sup>[↩ Parent](#alertspecalerttypelogsanomalylogsfiltersimplefilter)</sup></sup>\n\n\n\nFilter for labels.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsanomalylogsfiltersimplefilterlabelfiltersapplicationnameindex">applicationName</a></b></td>\n        <td>[]object</td>\n        <td>\n          Application name to filter for.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>severity</b></td>\n        <td>[]enum</td>\n        <td>\n          Severity to filter for.<br/>\n          <br/>\n            <i>Enum</i>: debug, info, warning, error, critical, verbose<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsanomalylogsfiltersimplefilterlabelfilterssubsystemnameindex">subsystemName</a></b></td>\n        <td>[]object</td>\n        <td>\n          Subsystem name to filter for.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsAnomaly.logsFilter.simpleFilter.labelFilters.applicationName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypelogsanomalylogsfiltersimplefilterlabelfilters)</sup></sup>\n\n\n\nLabel filter specifications\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Operation to apply.<br/>\n          <br/>\n            <i>Enum</i>: is, includes, endsWith, startsWith<br/>\n            <i>Default</i>: is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>value</b></td>\n        <td>string</td>\n        <td>\n          The value<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsAnomaly.logsFilter.simpleFilter.labelFilters.subsystemName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypelogsanomalylogsfiltersimplefilterlabelfilters)</sup></sup>\n\n\n\nLabel filter specifications\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Operation to apply.<br/>\n          <br/>\n            <i>Enum</i>: is, includes, endsWith, startsWith<br/>\n            <i>Default</i>: is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>value</b></td>\n        <td>string</td>\n        <td>\n          The value<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsImmediate\n<sup><sup>[↩ Parent](#alertspecalerttype)</sup></sup>\n\n\n\nImmediate alerts for logs.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsimmediatelogsfilter">logsFilter</a></b></td>\n        <td>object</td>\n        <td>\n          Filter to filter the logs with.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>notificationPayloadFilter</b></td>\n        <td>[]string</td>\n        <td>\n          Filter for the notification payload.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsImmediate.logsFilter\n<sup><sup>[↩ Parent](#alertspecalerttypelogsimmediate)</sup></sup>\n\n\n\nFilter to filter the logs with.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsimmediatelogsfiltersimplefilter">simpleFilter</a></b></td>\n        <td>object</td>\n        <td>\n          Simple lucene filter.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsImmediate.logsFilter.simpleFilter\n<sup><sup>[↩ Parent](#alertspecalerttypelogsimmediatelogsfilter)</sup></sup>\n\n\n\nSimple lucene filter.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsimmediatelogsfiltersimplefilterlabelfilters">labelFilters</a></b></td>\n        <td>object</td>\n        <td>\n          Filter for labels.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>luceneQuery</b></td>\n        <td>string</td>\n        <td>\n          The query.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsImmediate.logsFilter.simpleFilter.labelFilters\n<sup><sup>[↩ Parent](#alertspecalerttypelogsimmediatelogsfiltersimplefilter)</sup></sup>\n\n\n\nFilter for labels.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsimmediatelogsfiltersimplefilterlabelfiltersapplicationnameindex">applicationName</a></b></td>\n        <td>[]object</td>\n        <td>\n          Application name to filter for.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>severity</b></td>\n        <td>[]enum</td>\n        <td>\n          Severity to filter for.<br/>\n          <br/>\n            <i>Enum</i>: debug, info, warning, error, critical, verbose<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsimmediatelogsfiltersimplefilterlabelfilterssubsystemnameindex">subsystemName</a></b></td>\n        <td>[]object</td>\n        <td>\n          Subsystem name to filter for.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsImmediate.logsFilter.simpleFilter.labelFilters.applicationName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypelogsimmediatelogsfiltersimplefilterlabelfilters)</sup></sup>\n\n\n\nLabel filter specifications\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Operation to apply.<br/>\n          <br/>\n            <i>Enum</i>: is, includes, endsWith, startsWith<br/>\n            <i>Default</i>: is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>value</b></td>\n        <td>string</td>\n        <td>\n          The value<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsImmediate.logsFilter.simpleFilter.labelFilters.subsystemName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypelogsimmediatelogsfiltersimplefilterlabelfilters)</sup></sup>\n\n\n\nLabel filter specifications\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Operation to apply.<br/>\n          <br/>\n            <i>Enum</i>: is, includes, endsWith, startsWith<br/>\n            <i>Default</i>: is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>value</b></td>\n        <td>string</td>\n        <td>\n          The value<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsNewValue\n<sup><sup>[↩ Parent](#alertspecalerttype)</sup></sup>\n\n\n\nAlerts when a new log value appears.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsnewvaluelogsfilter">logsFilter</a></b></td>\n        <td>object</td>\n        <td>\n          Filter to filter the logs with.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsnewvaluerulesindex">rules</a></b></td>\n        <td>[]object</td>\n        <td>\n          Rules that match the alert to the data.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>notificationPayloadFilter</b></td>\n        <td>[]string</td>\n        <td>\n          Filter for the notification payload.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsNewValue.logsFilter\n<sup><sup>[↩ Parent](#alertspecalerttypelogsnewvalue)</sup></sup>\n\n\n\nFilter to filter the logs with.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsnewvaluelogsfiltersimplefilter">simpleFilter</a></b></td>\n        <td>object</td>\n        <td>\n          Simple lucene filter.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsNewValue.logsFilter.simpleFilter\n<sup><sup>[↩ Parent](#alertspecalerttypelogsnewvaluelogsfilter)</sup></sup>\n\n\n\nSimple lucene filter.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsnewvaluelogsfiltersimplefilterlabelfilters">labelFilters</a></b></td>\n        <td>object</td>\n        <td>\n          Filter for labels.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>luceneQuery</b></td>\n        <td>string</td>\n        <td>\n          The query.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsNewValue.logsFilter.simpleFilter.labelFilters\n<sup><sup>[↩ Parent](#alertspecalerttypelogsnewvaluelogsfiltersimplefilter)</sup></sup>\n\n\n\nFilter for labels.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsnewvaluelogsfiltersimplefilterlabelfiltersapplicationnameindex">applicationName</a></b></td>\n        <td>[]object</td>\n        <td>\n          Application name to filter for.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>severity</b></td>\n        <td>[]enum</td>\n        <td>\n          Severity to filter for.<br/>\n          <br/>\n            <i>Enum</i>: debug, info, warning, error, critical, verbose<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsnewvaluelogsfiltersimplefilterlabelfilterssubsystemnameindex">subsystemName</a></b></td>\n        <td>[]object</td>\n        <td>\n          Subsystem name to filter for.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsNewValue.logsFilter.simpleFilter.labelFilters.applicationName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypelogsnewvaluelogsfiltersimplefilterlabelfilters)</sup></sup>\n\n\n\nLabel filter specifications\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Operation to apply.<br/>\n          <br/>\n            <i>Enum</i>: is, includes, endsWith, startsWith<br/>\n            <i>Default</i>: is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>value</b></td>\n        <td>string</td>\n        <td>\n          The value<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsNewValue.logsFilter.simpleFilter.labelFilters.subsystemName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypelogsnewvaluelogsfiltersimplefilterlabelfilters)</sup></sup>\n\n\n\nLabel filter specifications\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Operation to apply.<br/>\n          <br/>\n            <i>Enum</i>: is, includes, endsWith, startsWith<br/>\n            <i>Default</i>: is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>value</b></td>\n        <td>string</td>\n        <td>\n          The value<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsNewValue.rules[index]\n<sup><sup>[↩ Parent](#alertspecalerttypelogsnewvalue)</sup></sup>\n\n\n\nThe rule to match the alert's conditions.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsnewvaluerulesindexcondition">condition</a></b></td>\n        <td>object</td>\n        <td>\n          Condition to match to<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsNewValue.rules[index].condition\n<sup><sup>[↩ Parent](#alertspecalerttypelogsnewvaluerulesindex)</sup></sup>\n\n\n\nCondition to match to\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>keypathToTrack</b></td>\n        <td>string</td>\n        <td>\n          Where to look<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsnewvaluerulesindexconditiontimewindow">timeWindow</a></b></td>\n        <td>object</td>\n        <td>\n          Which time window.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsNewValue.rules[index].condition.timeWindow\n<sup><sup>[↩ Parent](#alertspecalerttypelogsnewvaluerulesindexcondition)</sup></sup>\n\n\n\nWhich time window.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>specificValue</b></td>\n        <td>enum</td>\n        <td>\n          Time windows.<br/>\n          <br/>\n            <i>Enum</i>: 12h, 24h, 48h, 72h, 1w, 1mo, 2mo, 3mo<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsRatioThreshold\n<sup><sup>[↩ Parent](#alertspecalerttype)</sup></sup>\n\n\n\nAlerts for when a log exceeds a defined ratio.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsratiothresholddenominator">denominator</a></b></td>\n        <td>object</td>\n        <td>\n          A filter for logs.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>denominatorAlias</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsratiothresholdnumerator">numerator</a></b></td>\n        <td>object</td>\n        <td>\n          A filter for logs.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>numeratorAlias</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsratiothresholdrulesindex">rules</a></b></td>\n        <td>[]object</td>\n        <td>\n          Rules that match the alert to the data.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsRatioThreshold.denominator\n<sup><sup>[↩ Parent](#alertspecalerttypelogsratiothreshold)</sup></sup>\n\n\n\nA filter for logs.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsratiothresholddenominatorsimplefilter">simpleFilter</a></b></td>\n        <td>object</td>\n        <td>\n          Simple lucene filter.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsRatioThreshold.denominator.simpleFilter\n<sup><sup>[↩ Parent](#alertspecalerttypelogsratiothresholddenominator)</sup></sup>\n\n\n\nSimple lucene filter.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsratiothresholddenominatorsimplefilterlabelfilters">labelFilters</a></b></td>\n        <td>object</td>\n        <td>\n          Filter for labels.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>luceneQuery</b></td>\n        <td>string</td>\n        <td>\n          The query.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsRatioThreshold.denominator.simpleFilter.labelFilters\n<sup><sup>[↩ Parent](#alertspecalerttypelogsratiothresholddenominatorsimplefilter)</sup></sup>\n\n\n\nFilter for labels.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsratiothresholddenominatorsimplefilterlabelfiltersapplicationnameindex">applicationName</a></b></td>\n        <td>[]object</td>\n        <td>\n          Application name to filter for.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>severity</b></td>\n        <td>[]enum</td>\n        <td>\n          Severity to filter for.<br/>\n          <br/>\n            <i>Enum</i>: debug, info, warning, error, critical, verbose<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsratiothresholddenominatorsimplefilterlabelfilterssubsystemnameindex">subsystemName</a></b></td>\n        <td>[]object</td>\n        <td>\n          Subsystem name to filter for.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsRatioThreshold.denominator.simpleFilter.labelFilters.applicationName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypelogsratiothresholddenominatorsimplefilterlabelfilters)</sup></sup>\n\n\n\nLabel filter specifications\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Operation to apply.<br/>\n          <br/>\n            <i>Enum</i>: is, includes, endsWith, startsWith<br/>\n            <i>Default</i>: is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>value</b></td>\n        <td>string</td>\n        <td>\n          The value<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsRatioThreshold.denominator.simpleFilter.labelFilters.subsystemName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypelogsratiothresholddenominatorsimplefilterlabelfilters)</sup></sup>\n\n\n\nLabel filter specifications\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Operation to apply.<br/>\n          <br/>\n            <i>Enum</i>: is, includes, endsWith, startsWith<br/>\n            <i>Default</i>: is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>value</b></td>\n        <td>string</td>\n        <td>\n          The value<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsRatioThreshold.numerator\n<sup><sup>[↩ Parent](#alertspecalerttypelogsratiothreshold)</sup></sup>\n\n\n\nA filter for logs.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsratiothresholdnumeratorsimplefilter">simpleFilter</a></b></td>\n        <td>object</td>\n        <td>\n          Simple lucene filter.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsRatioThreshold.numerator.simpleFilter\n<sup><sup>[↩ Parent](#alertspecalerttypelogsratiothresholdnumerator)</sup></sup>\n\n\n\nSimple lucene filter.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsratiothresholdnumeratorsimplefilterlabelfilters">labelFilters</a></b></td>\n        <td>object</td>\n        <td>\n          Filter for labels.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>luceneQuery</b></td>\n        <td>string</td>\n        <td>\n          The query.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsRatioThreshold.numerator.simpleFilter.labelFilters\n<sup><sup>[↩ Parent](#alertspecalerttypelogsratiothresholdnumeratorsimplefilter)</sup></sup>\n\n\n\nFilter for labels.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsratiothresholdnumeratorsimplefilterlabelfiltersapplicationnameindex">applicationName</a></b></td>\n        <td>[]object</td>\n        <td>\n          Application name to filter for.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>severity</b></td>\n        <td>[]enum</td>\n        <td>\n          Severity to filter for.<br/>\n          <br/>\n            <i>Enum</i>: debug, info, warning, error, critical, verbose<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsratiothresholdnumeratorsimplefilterlabelfilterssubsystemnameindex">subsystemName</a></b></td>\n        <td>[]object</td>\n        <td>\n          Subsystem name to filter for.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsRatioThreshold.numerator.simpleFilter.labelFilters.applicationName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypelogsratiothresholdnumeratorsimplefilterlabelfilters)</sup></sup>\n\n\n\nLabel filter specifications\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Operation to apply.<br/>\n          <br/>\n            <i>Enum</i>: is, includes, endsWith, startsWith<br/>\n            <i>Default</i>: is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>value</b></td>\n        <td>string</td>\n        <td>\n          The value<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsRatioThreshold.numerator.simpleFilter.labelFilters.subsystemName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypelogsratiothresholdnumeratorsimplefilterlabelfilters)</sup></sup>\n\n\n\nLabel filter specifications\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Operation to apply.<br/>\n          <br/>\n            <i>Enum</i>: is, includes, endsWith, startsWith<br/>\n            <i>Default</i>: is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>value</b></td>\n        <td>string</td>\n        <td>\n          The value<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsRatioThreshold.rules[index]\n<sup><sup>[↩ Parent](#alertspecalerttypelogsratiothreshold)</sup></sup>\n\n\n\nThe rule to match the alert's conditions.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsratiothresholdrulesindexcondition">condition</a></b></td>\n        <td>object</td>\n        <td>\n          Condition to match<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsratiothresholdrulesindexoverride">override</a></b></td>\n        <td>object</td>\n        <td>\n          Override alert properties<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsRatioThreshold.rules[index].condition\n<sup><sup>[↩ Parent](#alertspecalerttypelogsratiothresholdrulesindex)</sup></sup>\n\n\n\nCondition to match\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>conditionType</b></td>\n        <td>enum</td>\n        <td>\n          Condition to evaluate with.<br/>\n          <br/>\n            <i>Enum</i>: moreThan, lessThan<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>threshold</b></td>\n        <td>int or string</td>\n        <td>\n          Threshold to pass.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsratiothresholdrulesindexconditiontimewindow">timeWindow</a></b></td>\n        <td>object</td>\n        <td>\n          Time window to evaluate.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsRatioThreshold.rules[index].condition.timeWindow\n<sup><sup>[↩ Parent](#alertspecalerttypelogsratiothresholdrulesindexcondition)</sup></sup>\n\n\n\nTime window to evaluate.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>specificValue</b></td>\n        <td>enum</td>\n        <td>\n          Time window type.<br/>\n          <br/>\n            <i>Enum</i>: 5m, 10m, 15m, 30m, 1h, 2h, 4h, 6h, 12h, 24h, 36h<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsRatioThreshold.rules[index].override\n<sup><sup>[↩ Parent](#alertspecalerttypelogsratiothresholdrulesindex)</sup></sup>\n\n\n\nOverride alert properties\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>priority</b></td>\n        <td>enum</td>\n        <td>\n          Priority to override it<br/>\n          <br/>\n            <i>Enum</i>: p1, p2, p3, p4, p5<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsThreshold\n<sup><sup>[↩ Parent](#alertspecalerttype)</sup></sup>\n\n\n\nAlerts for when a log crosses a threshold.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsthresholdrulesindex">rules</a></b></td>\n        <td>[]object</td>\n        <td>\n          Rules that match the alert to the data.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsthresholdlogsfilter">logsFilter</a></b></td>\n        <td>object</td>\n        <td>\n          Filter to filter the logs with.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>notificationPayloadFilter</b></td>\n        <td>[]string</td>\n        <td>\n          Filter for the notification payload.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsthresholdundetectedvaluesmanagement">undetectedValuesManagement</a></b></td>\n        <td>object</td>\n        <td>\n          How to work with undetected values.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsThreshold.rules[index]\n<sup><sup>[↩ Parent](#alertspecalerttypelogsthreshold)</sup></sup>\n\n\n\nThe rule to match the alert's conditions.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsthresholdrulesindexcondition">condition</a></b></td>\n        <td>object</td>\n        <td>\n          Condition to match<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsthresholdrulesindexoverride">override</a></b></td>\n        <td>object</td>\n        <td>\n          Alert overrides.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsThreshold.rules[index].condition\n<sup><sup>[↩ Parent](#alertspecalerttypelogsthresholdrulesindex)</sup></sup>\n\n\n\nCondition to match\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>logsThresholdConditionType</b></td>\n        <td>enum</td>\n        <td>\n          Condition type.<br/>\n          <br/>\n            <i>Enum</i>: moreThan, lessThan<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>threshold</b></td>\n        <td>int or string</td>\n        <td>\n          Threshold to match to.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsthresholdrulesindexconditiontimewindow">timeWindow</a></b></td>\n        <td>object</td>\n        <td>\n          Time window in which the condition is checked.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsThreshold.rules[index].condition.timeWindow\n<sup><sup>[↩ Parent](#alertspecalerttypelogsthresholdrulesindexcondition)</sup></sup>\n\n\n\nTime window in which the condition is checked.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>specificValue</b></td>\n        <td>enum</td>\n        <td>\n          Logs time window type<br/>\n          <br/>\n            <i>Enum</i>: 5m, 10m, 15m, 30m, 1h, 2h, 6h, 12h, 24h, 36h<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsThreshold.rules[index].override\n<sup><sup>[↩ Parent](#alertspecalerttypelogsthresholdrulesindex)</sup></sup>\n\n\n\nAlert overrides.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>priority</b></td>\n        <td>enum</td>\n        <td>\n          Priority to override it<br/>\n          <br/>\n            <i>Enum</i>: p1, p2, p3, p4, p5<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsThreshold.logsFilter\n<sup><sup>[↩ Parent](#alertspecalerttypelogsthreshold)</sup></sup>\n\n\n\nFilter to filter the logs with.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsthresholdlogsfiltersimplefilter">simpleFilter</a></b></td>\n        <td>object</td>\n        <td>\n          Simple lucene filter.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsThreshold.logsFilter.simpleFilter\n<sup><sup>[↩ Parent](#alertspecalerttypelogsthresholdlogsfilter)</sup></sup>\n\n\n\nSimple lucene filter.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsthresholdlogsfiltersimplefilterlabelfilters">labelFilters</a></b></td>\n        <td>object</td>\n        <td>\n          Filter for labels.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>luceneQuery</b></td>\n        <td>string</td>\n        <td>\n          The query.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsThreshold.logsFilter.simpleFilter.labelFilters\n<sup><sup>[↩ Parent](#alertspecalerttypelogsthresholdlogsfiltersimplefilter)</sup></sup>\n\n\n\nFilter for labels.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsthresholdlogsfiltersimplefilterlabelfiltersapplicationnameindex">applicationName</a></b></td>\n        <td>[]object</td>\n        <td>\n          Application name to filter for.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>severity</b></td>\n        <td>[]enum</td>\n        <td>\n          Severity to filter for.<br/>\n          <br/>\n            <i>Enum</i>: debug, info, warning, error, critical, verbose<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsthresholdlogsfiltersimplefilterlabelfilterssubsystemnameindex">subsystemName</a></b></td>\n        <td>[]object</td>\n        <td>\n          Subsystem name to filter for.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsThreshold.logsFilter.simpleFilter.labelFilters.applicationName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypelogsthresholdlogsfiltersimplefilterlabelfilters)</sup></sup>\n\n\n\nLabel filter specifications\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Operation to apply.<br/>\n          <br/>\n            <i>Enum</i>: is, includes, endsWith, startsWith<br/>\n            <i>Default</i>: is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>value</b></td>\n        <td>string</td>\n        <td>\n          The value<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsThreshold.logsFilter.simpleFilter.labelFilters.subsystemName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypelogsthresholdlogsfiltersimplefilterlabelfilters)</sup></sup>\n\n\n\nLabel filter specifications\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Operation to apply.<br/>\n          <br/>\n            <i>Enum</i>: is, includes, endsWith, startsWith<br/>\n            <i>Default</i>: is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>value</b></td>\n        <td>string</td>\n        <td>\n          The value<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsThreshold.undetectedValuesManagement\n<sup><sup>[↩ Parent](#alertspecalerttypelogsthreshold)</sup></sup>\n\n\n\nHow to work with undetected values.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>autoRetireTimeframe</b></td>\n        <td>enum</td>\n        <td>\n          Automatically retire the alerts after this time.<br/>\n          <br/>\n            <i>Enum</i>: never, 5m, 10m, 1h, 2h, 6h, 12h, 24h<br/>\n            <i>Default</i>: never<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>triggerUndetectedValues</b></td>\n        <td>boolean</td>\n        <td>\n          Deactivate triggering the alert on undetected values.<br/>\n          <br/>\n            <i>Default</i>: false<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsTimeRelativeThreshold\n<sup><sup>[↩ Parent](#alertspecalerttype)</sup></sup>\n\n\n\nAlerts are sent when the number of logs matching a filter is more than or less than a threshold over a specific time window.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>ignoreInfinity</b></td>\n        <td>boolean</td>\n        <td>\n          Ignore infinity on the threshold value.<br/>\n          <br/>\n            <i>Default</i>: false<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogstimerelativethresholdlogsfilter">logsFilter</a></b></td>\n        <td>object</td>\n        <td>\n          A filter for logs.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogstimerelativethresholdrulesindex">rules</a></b></td>\n        <td>[]object</td>\n        <td>\n          Rules that match the alert to the data.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>notificationPayloadFilter</b></td>\n        <td>[]string</td>\n        <td>\n          Filter for the notification payload.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogstimerelativethresholdundetectedvaluesmanagement">undetectedValuesManagement</a></b></td>\n        <td>object</td>\n        <td>\n          How to work with undetected values.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsTimeRelativeThreshold.logsFilter\n<sup><sup>[↩ Parent](#alertspecalerttypelogstimerelativethreshold)</sup></sup>\n\n\n\nA filter for logs.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogstimerelativethresholdlogsfiltersimplefilter">simpleFilter</a></b></td>\n        <td>object</td>\n        <td>\n          Simple lucene filter.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsTimeRelativeThreshold.logsFilter.simpleFilter\n<sup><sup>[↩ Parent](#alertspecalerttypelogstimerelativethresholdlogsfilter)</sup></sup>\n\n\n\nSimple lucene filter.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogstimerelativethresholdlogsfiltersimplefilterlabelfilters">labelFilters</a></b></td>\n        <td>object</td>\n        <td>\n          Filter for labels.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>luceneQuery</b></td>\n        <td>string</td>\n        <td>\n          The query.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsTimeRelativeThreshold.logsFilter.simpleFilter.labelFilters\n<sup><sup>[↩ Parent](#alertspecalerttypelogstimerelativethresholdlogsfiltersimplefilter)</sup></sup>\n\n\n\nFilter for labels.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogstimerelativethresholdlogsfiltersimplefilterlabelfiltersapplicationnameindex">applicationName</a></b></td>\n        <td>[]object</td>\n        <td>\n          Application name to filter for.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>severity</b></td>\n        <td>[]enum</td>\n        <td>\n          Severity to filter for.<br/>\n          <br/>\n            <i>Enum</i>: debug, info, warning, error, critical, verbose<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogstimerelativethresholdlogsfiltersimplefilterlabelfilterssubsystemnameindex">subsystemName</a></b></td>\n        <td>[]object</td>\n        <td>\n          Subsystem name to filter for.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsTimeRelativeThreshold.logsFilter.simpleFilter.labelFilters.applicationName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypelogstimerelativethresholdlogsfiltersimplefilterlabelfilters)</sup></sup>\n\n\n\nLabel filter specifications\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Operation to apply.<br/>\n          <br/>\n            <i>Enum</i>: is, includes, endsWith, startsWith<br/>\n            <i>Default</i>: is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>value</b></td>\n        <td>string</td>\n        <td>\n          The value<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsTimeRelativeThreshold.logsFilter.simpleFilter.labelFilters.subsystemName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypelogstimerelativethresholdlogsfiltersimplefilterlabelfilters)</sup></sup>\n\n\n\nLabel filter specifications\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Operation to apply.<br/>\n          <br/>\n            <i>Enum</i>: is, includes, endsWith, startsWith<br/>\n            <i>Default</i>: is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>value</b></td>\n        <td>string</td>\n        <td>\n          The value<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsTimeRelativeThreshold.rules[index]\n<sup><sup>[↩ Parent](#alertspecalerttypelogstimerelativethreshold)</sup></sup>\n\n\n\nThe rule to match the alert's conditions.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogstimerelativethresholdrulesindexcondition">condition</a></b></td>\n        <td>object</td>\n        <td>\n          The condition to match to.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogstimerelativethresholdrulesindexoverride">override</a></b></td>\n        <td>object</td>\n        <td>\n          Override alert properties<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsTimeRelativeThreshold.rules[index].condition\n<sup><sup>[↩ Parent](#alertspecalerttypelogstimerelativethresholdrulesindex)</sup></sup>\n\n\n\nThe condition to match to.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>comparedTo</b></td>\n        <td>enum</td>\n        <td>\n          Comparison window.<br/>\n          <br/>\n            <i>Enum</i>: previousHour, sameHourYesterday, sameHourLastWeek, yesterday, sameDayLastWeek, sameDayLastMonth<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>conditionType</b></td>\n        <td>enum</td>\n        <td>\n          How to compare.<br/>\n          <br/>\n            <i>Enum</i>: moreThan, lessThan<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>threshold</b></td>\n        <td>int or string</td>\n        <td>\n          Threshold to match.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsTimeRelativeThreshold.rules[index].override\n<sup><sup>[↩ Parent](#alertspecalerttypelogstimerelativethresholdrulesindex)</sup></sup>\n\n\n\nOverride alert properties\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>priority</b></td>\n        <td>enum</td>\n        <td>\n          Priority to override it<br/>\n          <br/>\n            <i>Enum</i>: p1, p2, p3, p4, p5<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsTimeRelativeThreshold.undetectedValuesManagement\n<sup><sup>[↩ Parent](#alertspecalerttypelogstimerelativethreshold)</sup></sup>\n\n\n\nHow to work with undetected values.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>autoRetireTimeframe</b></td>\n        <td>enum</td>\n        <td>\n          Automatically retire the alerts after this time.<br/>\n          <br/>\n            <i>Enum</i>: never, 5m, 10m, 1h, 2h, 6h, 12h, 24h<br/>\n            <i>Default</i>: never<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>triggerUndetectedValues</b></td>\n        <td>boolean</td>\n        <td>\n          Deactivate triggering the alert on undetected values.<br/>\n          <br/>\n            <i>Default</i>: false<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsUniqueCount\n<sup><sup>[↩ Parent](#alertspecalerttype)</sup></sup>\n\n\n\nAlerts for unique count changes.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsuniquecountlogsfilter">logsFilter</a></b></td>\n        <td>object</td>\n        <td>\n          Filter to filter the logs with.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsuniquecountrulesindex">rules</a></b></td>\n        <td>[]object</td>\n        <td>\n          Rules that match the alert to the data.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>uniqueCountKeypath</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>maxUniqueCountPerGroupByKey</b></td>\n        <td>integer</td>\n        <td>\n          <br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>notificationPayloadFilter</b></td>\n        <td>[]string</td>\n        <td>\n          Filter for the notification payload.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsUniqueCount.logsFilter\n<sup><sup>[↩ Parent](#alertspecalerttypelogsuniquecount)</sup></sup>\n\n\n\nFilter to filter the logs with.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsuniquecountlogsfiltersimplefilter">simpleFilter</a></b></td>\n        <td>object</td>\n        <td>\n          Simple lucene filter.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsUniqueCount.logsFilter.simpleFilter\n<sup><sup>[↩ Parent](#alertspecalerttypelogsuniquecountlogsfilter)</sup></sup>\n\n\n\nSimple lucene filter.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsuniquecountlogsfiltersimplefilterlabelfilters">labelFilters</a></b></td>\n        <td>object</td>\n        <td>\n          Filter for labels.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>luceneQuery</b></td>\n        <td>string</td>\n        <td>\n          The query.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsUniqueCount.logsFilter.simpleFilter.labelFilters\n<sup><sup>[↩ Parent](#alertspecalerttypelogsuniquecountlogsfiltersimplefilter)</sup></sup>\n\n\n\nFilter for labels.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsuniquecountlogsfiltersimplefilterlabelfiltersapplicationnameindex">applicationName</a></b></td>\n        <td>[]object</td>\n        <td>\n          Application name to filter for.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>severity</b></td>\n        <td>[]enum</td>\n        <td>\n          Severity to filter for.<br/>\n          <br/>\n            <i>Enum</i>: debug, info, warning, error, critical, verbose<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsuniquecountlogsfiltersimplefilterlabelfilterssubsystemnameindex">subsystemName</a></b></td>\n        <td>[]object</td>\n        <td>\n          Subsystem name to filter for.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsUniqueCount.logsFilter.simpleFilter.labelFilters.applicationName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypelogsuniquecountlogsfiltersimplefilterlabelfilters)</sup></sup>\n\n\n\nLabel filter specifications\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Operation to apply.<br/>\n          <br/>\n            <i>Enum</i>: is, includes, endsWith, startsWith<br/>\n            <i>Default</i>: is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>value</b></td>\n        <td>string</td>\n        <td>\n          The value<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsUniqueCount.logsFilter.simpleFilter.labelFilters.subsystemName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypelogsuniquecountlogsfiltersimplefilterlabelfilters)</sup></sup>\n\n\n\nLabel filter specifications\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Operation to apply.<br/>\n          <br/>\n            <i>Enum</i>: is, includes, endsWith, startsWith<br/>\n            <i>Default</i>: is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>value</b></td>\n        <td>string</td>\n        <td>\n          The value<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsUniqueCount.rules[index]\n<sup><sup>[↩ Parent](#alertspecalerttypelogsuniquecount)</sup></sup>\n\n\n\nThe rule to match the alert's conditions.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypelogsuniquecountrulesindexcondition">condition</a></b></td>\n        <td>object</td>\n        <td>\n          Condition to match to.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsUniqueCount.rules[index].condition\n<sup><sup>[↩ Parent](#alertspecalerttypelogsuniquecountrulesindex)</sup></sup>\n\n\n\nCondition to match to.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>threshold</b></td>\n        <td>integer</td>\n        <td>\n          Threshold to cross<br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypelogsuniquecountrulesindexconditiontimewindow">timeWindow</a></b></td>\n        <td>object</td>\n        <td>\n          Time window to evaluate.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.logsUniqueCount.rules[index].condition.timeWindow\n<sup><sup>[↩ Parent](#alertspecalerttypelogsuniquecountrulesindexcondition)</sup></sup>\n\n\n\nTime window to evaluate.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>specificValue</b></td>\n        <td>enum</td>\n        <td>\n          Time windows for Logs Unique Count<br/>\n          <br/>\n            <i>Enum</i>: 1m, 5m, 10m, 15m, 20m, 30m, 1h, 2h, 4h, 6h, 12h, 24h, 36h<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.metricAnomaly\n<sup><sup>[↩ Parent](#alertspecalerttype)</sup></sup>\n\n\n\nAnomaly alerts for metrics.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypemetricanomalymetricfilter">metricFilter</a></b></td>\n        <td>object</td>\n        <td>\n          PromQL filter for metrics<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypemetricanomalyrulesindex">rules</a></b></td>\n        <td>[]object</td>\n        <td>\n          Rules that match the alert to the data.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.metricAnomaly.metricFilter\n<sup><sup>[↩ Parent](#alertspecalerttypemetricanomaly)</sup></sup>\n\n\n\nPromQL filter for metrics\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>promql</b></td>\n        <td>string</td>\n        <td>\n          PromQL query: https://coralogix.com/academy/mastering-metrics-in-coralogix/promql-fundamentals/<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.metricAnomaly.rules[index]\n<sup><sup>[↩ Parent](#alertspecalerttypemetricanomaly)</sup></sup>\n\n\n\nThe rule to match the alert's conditions.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypemetricanomalyrulesindexcondition">condition</a></b></td>\n        <td>object</td>\n        <td>\n          Condition to match to.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.metricAnomaly.rules[index].condition\n<sup><sup>[↩ Parent](#alertspecalerttypemetricanomalyrulesindex)</sup></sup>\n\n\n\nCondition to match to.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>conditionType</b></td>\n        <td>enum</td>\n        <td>\n          Condition type.<br/>\n          <br/>\n            <i>Enum</i>: moreThanUsual, lessThanUsual<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>forOverPct</b></td>\n        <td>integer</td>\n        <td>\n          Percentage for the threshold<br/>\n          <br/>\n            <i>Format</i>: int32<br/>\n            <i>Maximum</i>: 100<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>minNonNullValuesPct</b></td>\n        <td>integer</td>\n        <td>\n          Replace with a number<br/>\n          <br/>\n            <i>Format</i>: int32<br/>\n            <i>Maximum</i>: 100<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypemetricanomalyrulesindexconditionofthelast">ofTheLast</a></b></td>\n        <td>object</td>\n        <td>\n          Time window to match within<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>threshold</b></td>\n        <td>int or string</td>\n        <td>\n          Threshold to clear.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.metricAnomaly.rules[index].condition.ofTheLast\n<sup><sup>[↩ Parent](#alertspecalerttypemetricanomalyrulesindexcondition)</sup></sup>\n\n\n\nTime window to match within\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>specificValue</b></td>\n        <td>enum</td>\n        <td>\n          Time window type.<br/>\n          <br/>\n            <i>Enum</i>: 1m, 5m, 10m, 15m, 20m, 30m, 1h, 2h, 4h, 6h, 12h, 24h, 36h<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.metricThreshold\n<sup><sup>[↩ Parent](#alertspecalerttype)</sup></sup>\n\n\n\nAlerts for when a metric crosses a threshold.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypemetricthresholdmetricfilter">metricFilter</a></b></td>\n        <td>object</td>\n        <td>\n          Filter for metrics<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypemetricthresholdmissingvalues">missingValues</a></b></td>\n        <td>object</td>\n        <td>\n          Missing values strategies.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypemetricthresholdrulesindex">rules</a></b></td>\n        <td>[]object</td>\n        <td>\n          Rules that match the alert to the data.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypemetricthresholdundetectedvaluesmanagement">undetectedValuesManagement</a></b></td>\n        <td>object</td>\n        <td>\n          How to work with undetected values.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.metricThreshold.metricFilter\n<sup><sup>[↩ Parent](#alertspecalerttypemetricthreshold)</sup></sup>\n\n\n\nFilter for metrics\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>promql</b></td>\n        <td>string</td>\n        <td>\n          PromQL query: https://coralogix.com/academy/mastering-metrics-in-coralogix/promql-fundamentals/<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.metricThreshold.missingValues\n<sup><sup>[↩ Parent](#alertspecalerttypemetricthreshold)</sup></sup>\n\n\n\nMissing values strategies.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>minNonNullValuesPct</b></td>\n        <td>integer</td>\n        <td>\n          Replace with a number<br/>\n          <br/>\n            <i>Format</i>: int32<br/>\n            <i>Maximum</i>: 100<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>replaceWithZero</b></td>\n        <td>boolean</td>\n        <td>\n          Replace missing values with 0s<br/>\n          <br/>\n            <i>Default</i>: false<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.metricThreshold.rules[index]\n<sup><sup>[↩ Parent](#alertspecalerttypemetricthreshold)</sup></sup>\n\n\n\nRules that match the alert to the data.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypemetricthresholdrulesindexcondition">condition</a></b></td>\n        <td>object</td>\n        <td>\n          Conditions to match for the rule.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypemetricthresholdrulesindexoverride">override</a></b></td>\n        <td>object</td>\n        <td>\n          Alert property overrides<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.metricThreshold.rules[index].condition\n<sup><sup>[↩ Parent](#alertspecalerttypemetricthresholdrulesindex)</sup></sup>\n\n\n\nConditions to match for the rule.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>conditionType</b></td>\n        <td>enum</td>\n        <td>\n          ConditionType type.<br/>\n          <br/>\n            <i>Enum</i>: moreThan, lessThan, moreThanOrEquals, lessThanOrEquals<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>forOverPct</b></td>\n        <td>integer</td>\n        <td>\n          <br/>\n          <br/>\n            <i>Format</i>: int32<br/>\n            <i>Maximum</i>: 100<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypemetricthresholdrulesindexconditionofthelast">ofTheLast</a></b></td>\n        <td>object</td>\n        <td>\n          Time window type.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.specificValue) != has(self.dynamicDuration): Exactly one of specificValue or dynamicDuration is required</li>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>threshold</b></td>\n        <td>int or string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.metricThreshold.rules[index].condition.ofTheLast\n<sup><sup>[↩ Parent](#alertspecalerttypemetricthresholdrulesindexcondition)</sup></sup>\n\n\n\nTime window type.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>dynamicDuration</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>specificValue</b></td>\n        <td>enum</td>\n        <td>\n          Time window type.<br/>\n          <br/>\n            <i>Enum</i>: 1m, 5m, 10m, 15m, 20m, 30m, 1h, 2h, 4h, 6h, 12h, 24h, 36h<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.metricThreshold.rules[index].override\n<sup><sup>[↩ Parent](#alertspecalerttypemetricthresholdrulesindex)</sup></sup>\n\n\n\nAlert property overrides\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>priority</b></td>\n        <td>enum</td>\n        <td>\n          Priority to override it<br/>\n          <br/>\n            <i>Enum</i>: p1, p2, p3, p4, p5<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.metricThreshold.undetectedValuesManagement\n<sup><sup>[↩ Parent](#alertspecalerttypemetricthreshold)</sup></sup>\n\n\n\nHow to work with undetected values.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>autoRetireTimeframe</b></td>\n        <td>enum</td>\n        <td>\n          Automatically retire the alerts after this time.<br/>\n          <br/>\n            <i>Enum</i>: never, 5m, 10m, 1h, 2h, 6h, 12h, 24h<br/>\n            <i>Default</i>: never<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>triggerUndetectedValues</b></td>\n        <td>boolean</td>\n        <td>\n          Deactivate triggering the alert on undetected values.<br/>\n          <br/>\n            <i>Default</i>: false<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingImmediate\n<sup><sup>[↩ Parent](#alertspecalerttype)</sup></sup>\n\n\n\nImmediate alerts for traces.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>notificationPayloadFilter</b></td>\n        <td>[]string</td>\n        <td>\n          Filter for the notification payload.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypetracingimmediatetracingfilter">tracingFilter</a></b></td>\n        <td>object</td>\n        <td>\n          A simple tracing filter.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingImmediate.tracingFilter\n<sup><sup>[↩ Parent](#alertspecalerttypetracingimmediate)</sup></sup>\n\n\n\nA simple tracing filter.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypetracingimmediatetracingfiltersimple">simple</a></b></td>\n        <td>object</td>\n        <td>\n          Simple tracing filter paired with a latency.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingImmediate.tracingFilter.simple\n<sup><sup>[↩ Parent](#alertspecalerttypetracingimmediatetracingfilter)</sup></sup>\n\n\n\nSimple tracing filter paired with a latency.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>latencyThresholdMs</b></td>\n        <td>integer</td>\n        <td>\n          <br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypetracingimmediatetracingfiltersimpletracinglabelfilters">tracingLabelFilters</a></b></td>\n        <td>object</td>\n        <td>\n          Filter for traces.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingImmediate.tracingFilter.simple.tracingLabelFilters\n<sup><sup>[↩ Parent](#alertspecalerttypetracingimmediatetracingfiltersimple)</sup></sup>\n\n\n\nFilter for traces.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypetracingimmediatetracingfiltersimpletracinglabelfiltersapplicationnameindex">applicationName</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypetracingimmediatetracingfiltersimpletracinglabelfiltersoperationnameindex">operationName</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypetracingimmediatetracingfiltersimpletracinglabelfiltersservicenameindex">serviceName</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypetracingimmediatetracingfiltersimpletracinglabelfiltersspanfieldsindex">spanFields</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypetracingimmediatetracingfiltersimpletracinglabelfilterssubsystemnameindex">subsystemName</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingImmediate.tracingFilter.simple.tracingLabelFilters.applicationName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypetracingimmediatetracingfiltersimpletracinglabelfilters)</sup></sup>\n\n\n\nFilter - values and operation.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Tracing filter operations.<br/>\n          <br/>\n            <i>Enum</i>: includes, endsWith, startsWith, isNot, is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>values</b></td>\n        <td>[]string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingImmediate.tracingFilter.simple.tracingLabelFilters.operationName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypetracingimmediatetracingfiltersimpletracinglabelfilters)</sup></sup>\n\n\n\nFilter - values and operation.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Tracing filter operations.<br/>\n          <br/>\n            <i>Enum</i>: includes, endsWith, startsWith, isNot, is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>values</b></td>\n        <td>[]string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingImmediate.tracingFilter.simple.tracingLabelFilters.serviceName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypetracingimmediatetracingfiltersimpletracinglabelfilters)</sup></sup>\n\n\n\nFilter - values and operation.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Tracing filter operations.<br/>\n          <br/>\n            <i>Enum</i>: includes, endsWith, startsWith, isNot, is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>values</b></td>\n        <td>[]string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingImmediate.tracingFilter.simple.tracingLabelFilters.spanFields[index]\n<sup><sup>[↩ Parent](#alertspecalerttypetracingimmediatetracingfiltersimpletracinglabelfilters)</sup></sup>\n\n\n\nFilter for spans\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypetracingimmediatetracingfiltersimpletracinglabelfiltersspanfieldsindexfiltertype">filterType</a></b></td>\n        <td>object</td>\n        <td>\n          Filter - values and operation.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>key</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingImmediate.tracingFilter.simple.tracingLabelFilters.spanFields[index].filterType\n<sup><sup>[↩ Parent](#alertspecalerttypetracingimmediatetracingfiltersimpletracinglabelfiltersspanfieldsindex)</sup></sup>\n\n\n\nFilter - values and operation.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Tracing filter operations.<br/>\n          <br/>\n            <i>Enum</i>: includes, endsWith, startsWith, isNot, is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>values</b></td>\n        <td>[]string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingImmediate.tracingFilter.simple.tracingLabelFilters.subsystemName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypetracingimmediatetracingfiltersimpletracinglabelfilters)</sup></sup>\n\n\n\nFilter - values and operation.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Tracing filter operations.<br/>\n          <br/>\n            <i>Enum</i>: includes, endsWith, startsWith, isNot, is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>values</b></td>\n        <td>[]string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingThreshold\n<sup><sup>[↩ Parent](#alertspecalerttype)</sup></sup>\n\n\n\nAlerts for when traces crosses a threshold.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypetracingthresholdrulesindex">rules</a></b></td>\n        <td>[]object</td>\n        <td>\n          Rules that match the alert to the data.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>notificationPayloadFilter</b></td>\n        <td>[]string</td>\n        <td>\n          Filter for the notification payload.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypetracingthresholdtracingfilter">tracingFilter</a></b></td>\n        <td>object</td>\n        <td>\n          Filter the base collection.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingThreshold.rules[index]\n<sup><sup>[↩ Parent](#alertspecalerttypetracingthreshold)</sup></sup>\n\n\n\nThe rule to match the alert's conditions.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypetracingthresholdrulesindexcondition">condition</a></b></td>\n        <td>object</td>\n        <td>\n          The condition to match to.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingThreshold.rules[index].condition\n<sup><sup>[↩ Parent](#alertspecalerttypetracingthresholdrulesindex)</sup></sup>\n\n\n\nThe condition to match to.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>spanAmount</b></td>\n        <td>int or string</td>\n        <td>\n          Threshold amount.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypetracingthresholdrulesindexconditiontimewindow">timeWindow</a></b></td>\n        <td>object</td>\n        <td>\n          Time window to evaluate.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingThreshold.rules[index].condition.timeWindow\n<sup><sup>[↩ Parent](#alertspecalerttypetracingthresholdrulesindexcondition)</sup></sup>\n\n\n\nTime window to evaluate.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>specificValue</b></td>\n        <td>enum</td>\n        <td>\n          Time window type for tracing.<br/>\n          <br/>\n            <i>Enum</i>: 5m, 10m, 15m, 20m, 30m, 1h, 2h, 4h, 6h, 12h, 24h, 36h<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingThreshold.tracingFilter\n<sup><sup>[↩ Parent](#alertspecalerttypetracingthreshold)</sup></sup>\n\n\n\nFilter the base collection.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypetracingthresholdtracingfiltersimple">simple</a></b></td>\n        <td>object</td>\n        <td>\n          Simple tracing filter paired with a latency.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingThreshold.tracingFilter.simple\n<sup><sup>[↩ Parent](#alertspecalerttypetracingthresholdtracingfilter)</sup></sup>\n\n\n\nSimple tracing filter paired with a latency.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>latencyThresholdMs</b></td>\n        <td>integer</td>\n        <td>\n          <br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypetracingthresholdtracingfiltersimpletracinglabelfilters">tracingLabelFilters</a></b></td>\n        <td>object</td>\n        <td>\n          Filter for traces.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingThreshold.tracingFilter.simple.tracingLabelFilters\n<sup><sup>[↩ Parent](#alertspecalerttypetracingthresholdtracingfiltersimple)</sup></sup>\n\n\n\nFilter for traces.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypetracingthresholdtracingfiltersimpletracinglabelfiltersapplicationnameindex">applicationName</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypetracingthresholdtracingfiltersimpletracinglabelfiltersoperationnameindex">operationName</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypetracingthresholdtracingfiltersimpletracinglabelfiltersservicenameindex">serviceName</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypetracingthresholdtracingfiltersimpletracinglabelfiltersspanfieldsindex">spanFields</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecalerttypetracingthresholdtracingfiltersimpletracinglabelfilterssubsystemnameindex">subsystemName</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingThreshold.tracingFilter.simple.tracingLabelFilters.applicationName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypetracingthresholdtracingfiltersimpletracinglabelfilters)</sup></sup>\n\n\n\nFilter - values and operation.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Tracing filter operations.<br/>\n          <br/>\n            <i>Enum</i>: includes, endsWith, startsWith, isNot, is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>values</b></td>\n        <td>[]string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingThreshold.tracingFilter.simple.tracingLabelFilters.operationName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypetracingthresholdtracingfiltersimpletracinglabelfilters)</sup></sup>\n\n\n\nFilter - values and operation.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Tracing filter operations.<br/>\n          <br/>\n            <i>Enum</i>: includes, endsWith, startsWith, isNot, is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>values</b></td>\n        <td>[]string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingThreshold.tracingFilter.simple.tracingLabelFilters.serviceName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypetracingthresholdtracingfiltersimpletracinglabelfilters)</sup></sup>\n\n\n\nFilter - values and operation.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Tracing filter operations.<br/>\n          <br/>\n            <i>Enum</i>: includes, endsWith, startsWith, isNot, is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>values</b></td>\n        <td>[]string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingThreshold.tracingFilter.simple.tracingLabelFilters.spanFields[index]\n<sup><sup>[↩ Parent](#alertspecalerttypetracingthresholdtracingfiltersimpletracinglabelfilters)</sup></sup>\n\n\n\nFilter for spans\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecalerttypetracingthresholdtracingfiltersimpletracinglabelfiltersspanfieldsindexfiltertype">filterType</a></b></td>\n        <td>object</td>\n        <td>\n          Filter - values and operation.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>key</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingThreshold.tracingFilter.simple.tracingLabelFilters.spanFields[index].filterType\n<sup><sup>[↩ Parent](#alertspecalerttypetracingthresholdtracingfiltersimpletracinglabelfiltersspanfieldsindex)</sup></sup>\n\n\n\nFilter - values and operation.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Tracing filter operations.<br/>\n          <br/>\n            <i>Enum</i>: includes, endsWith, startsWith, isNot, is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>values</b></td>\n        <td>[]string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.alertType.tracingThreshold.tracingFilter.simple.tracingLabelFilters.subsystemName[index]\n<sup><sup>[↩ Parent](#alertspecalerttypetracingthresholdtracingfiltersimpletracinglabelfilters)</sup></sup>\n\n\n\nFilter - values and operation.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          Tracing filter operations.<br/>\n          <br/>\n            <i>Enum</i>: includes, endsWith, startsWith, isNot, is<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>values</b></td>\n        <td>[]string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.incidentsSettings\n<sup><sup>[↩ Parent](#alertspec)</sup></sup>\n\n\n\nSettings for the attached incidents.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>notifyOn</b></td>\n        <td>enum</td>\n        <td>\n          When to notify.<br/>\n          <br/>\n            <i>Enum</i>: triggeredOnly, triggeredAndResolved<br/>\n            <i>Default</i>: triggeredOnly<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecincidentssettingsretriggeringperiod">retriggeringPeriod</a></b></td>\n        <td>object</td>\n        <td>\n          When to re-notify.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.incidentsSettings.retriggeringPeriod\n<sup><sup>[↩ Parent](#alertspecincidentssettings)</sup></sup>\n\n\n\nWhen to re-notify.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>minutes</b></td>\n        <td>integer</td>\n        <td>\n          Delay between re-triggered alerts.<br/>\n          <br/>\n            <i>Format</i>: int32<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup\n<sup><sup>[↩ Parent](#alertspec)</sup></sup>\n\n\n\nWhere notifications should be sent to.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecnotificationgroupdestinationsindex">destinations</a></b></td>\n        <td>[]object</td>\n        <td>\n          The destinations for notifications (Notification Center feature).<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>groupByKeys</b></td>\n        <td>[]string</td>\n        <td>\n          Group notification by these keys.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgrouprouter">router</a></b></td>\n        <td>object</td>\n        <td>\n          The router for notifications (Notification Center feature).<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupwebhooksindex">webhooks</a></b></td>\n        <td>[]object</td>\n        <td>\n          Webhooks to trigger for notifications.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup.destinations[index]\n<sup><sup>[↩ Parent](#alertspecnotificationgroup)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecnotificationgroupdestinationsindexconnector">connector</a></b></td>\n        <td>object</td>\n        <td>\n          Connector is the connector for the destination. Should be one of backendRef or resourceRef.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.backendRef) != has(self.resourceRef): Exactly one of backendRef or resourceRef must be set</li>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>notifyOn</b></td>\n        <td>enum</td>\n        <td>\n          When to notify.<br/>\n          <br/>\n            <i>Enum</i>: triggeredOnly, triggeredAndResolved<br/>\n            <i>Default</i>: triggeredOnly<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupdestinationsindextriggeredroutingoverrides">triggeredRoutingOverrides</a></b></td>\n        <td>object</td>\n        <td>\n          The routing configuration to override from the connector/preset for triggered notifications.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupdestinationsindexpreset">preset</a></b></td>\n        <td>object</td>\n        <td>\n          Preset is the preset for the destination. Should be one of backendRef or resourceRef.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.backendRef) != has(self.resourceRef): Exactly one of backendRef or resourceRef must be set</li>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupdestinationsindexresolvedroutingoverrides">resolvedRoutingOverrides</a></b></td>\n        <td>object</td>\n        <td>\n          Optional routing configuration to override from the connector/preset for resolved notifications.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup.destinations[index].connector\n<sup><sup>[↩ Parent](#alertspecnotificationgroupdestinationsindex)</sup></sup>\n\n\n\nConnector is the connector for the destination. Should be one of backendRef or resourceRef.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecnotificationgroupdestinationsindexconnectorbackendref">backendRef</a></b></td>\n        <td>object</td>\n        <td>\n          BackendRef is a reference to a backend resource.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupdestinationsindexconnectorresourceref">resourceRef</a></b></td>\n        <td>object</td>\n        <td>\n          ResourceRef is a reference to a Kubernetes resource.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup.destinations[index].connector.backendRef\n<sup><sup>[↩ Parent](#alertspecnotificationgroupdestinationsindexconnector)</sup></sup>\n\n\n\nBackendRef is a reference to a backend resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup.destinations[index].connector.resourceRef\n<sup><sup>[↩ Parent](#alertspecnotificationgroupdestinationsindexconnector)</sup></sup>\n\n\n\nResourceRef is a reference to a Kubernetes resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the resource.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>namespace</b></td>\n        <td>string</td>\n        <td>\n          Kubernetes namespace.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup.destinations[index].triggeredRoutingOverrides\n<sup><sup>[↩ Parent](#alertspecnotificationgroupdestinationsindex)</sup></sup>\n\n\n\nThe routing configuration to override from the connector/preset for triggered notifications.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecnotificationgroupdestinationsindextriggeredroutingoverridesconfigoverrides">configOverrides</a></b></td>\n        <td>object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup.destinations[index].triggeredRoutingOverrides.configOverrides\n<sup><sup>[↩ Parent](#alertspecnotificationgroupdestinationsindextriggeredroutingoverrides)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>outputSchemaId</b></td>\n        <td>string</td>\n        <td>\n          The ID of the output schema to use for routing notifications<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupdestinationsindextriggeredroutingoverridesconfigoverridesconnectorconfigfieldsindex">connectorConfigFields</a></b></td>\n        <td>[]object</td>\n        <td>\n          Connector configuration fields.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupdestinationsindextriggeredroutingoverridesconfigoverridesmessageconfigfieldsindex">messageConfigFields</a></b></td>\n        <td>[]object</td>\n        <td>\n          Notification message configuration fields.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup.destinations[index].triggeredRoutingOverrides.configOverrides.connectorConfigFields[index]\n<sup><sup>[↩ Parent](#alertspecnotificationgroupdestinationsindextriggeredroutingoverridesconfigoverrides)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>fieldName</b></td>\n        <td>string</td>\n        <td>\n          The name of the configuration field.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>template</b></td>\n        <td>string</td>\n        <td>\n          The template for the configuration field.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup.destinations[index].triggeredRoutingOverrides.configOverrides.messageConfigFields[index]\n<sup><sup>[↩ Parent](#alertspecnotificationgroupdestinationsindextriggeredroutingoverridesconfigoverrides)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>fieldName</b></td>\n        <td>string</td>\n        <td>\n          The name of the configuration field.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>template</b></td>\n        <td>string</td>\n        <td>\n          The template for the configuration field.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup.destinations[index].preset\n<sup><sup>[↩ Parent](#alertspecnotificationgroupdestinationsindex)</sup></sup>\n\n\n\nPreset is the preset for the destination. Should be one of backendRef or resourceRef.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecnotificationgroupdestinationsindexpresetbackendref">backendRef</a></b></td>\n        <td>object</td>\n        <td>\n          BackendRef is a reference to a backend resource.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupdestinationsindexpresetresourceref">resourceRef</a></b></td>\n        <td>object</td>\n        <td>\n          ResourceRef is a reference to a Kubernetes resource.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup.destinations[index].preset.backendRef\n<sup><sup>[↩ Parent](#alertspecnotificationgroupdestinationsindexpreset)</sup></sup>\n\n\n\nBackendRef is a reference to a backend resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup.destinations[index].preset.resourceRef\n<sup><sup>[↩ Parent](#alertspecnotificationgroupdestinationsindexpreset)</sup></sup>\n\n\n\nResourceRef is a reference to a Kubernetes resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the resource.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>namespace</b></td>\n        <td>string</td>\n        <td>\n          Kubernetes namespace.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup.destinations[index].resolvedRoutingOverrides\n<sup><sup>[↩ Parent](#alertspecnotificationgroupdestinationsindex)</sup></sup>\n\n\n\nOptional routing configuration to override from the connector/preset for resolved notifications.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecnotificationgroupdestinationsindexresolvedroutingoverridesconfigoverrides">configOverrides</a></b></td>\n        <td>object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup.destinations[index].resolvedRoutingOverrides.configOverrides\n<sup><sup>[↩ Parent](#alertspecnotificationgroupdestinationsindexresolvedroutingoverrides)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>outputSchemaId</b></td>\n        <td>string</td>\n        <td>\n          The ID of the output schema to use for routing notifications<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupdestinationsindexresolvedroutingoverridesconfigoverridesconnectorconfigfieldsindex">connectorConfigFields</a></b></td>\n        <td>[]object</td>\n        <td>\n          Connector configuration fields.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupdestinationsindexresolvedroutingoverridesconfigoverridesmessageconfigfieldsindex">messageConfigFields</a></b></td>\n        <td>[]object</td>\n        <td>\n          Notification message configuration fields.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup.destinations[index].resolvedRoutingOverrides.configOverrides.connectorConfigFields[index]\n<sup><sup>[↩ Parent](#alertspecnotificationgroupdestinationsindexresolvedroutingoverridesconfigoverrides)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>fieldName</b></td>\n        <td>string</td>\n        <td>\n          The name of the configuration field.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>template</b></td>\n        <td>string</td>\n        <td>\n          The template for the configuration field.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup.destinations[index].resolvedRoutingOverrides.configOverrides.messageConfigFields[index]\n<sup><sup>[↩ Parent](#alertspecnotificationgroupdestinationsindexresolvedroutingoverridesconfigoverrides)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>fieldName</b></td>\n        <td>string</td>\n        <td>\n          The name of the configuration field.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>template</b></td>\n        <td>string</td>\n        <td>\n          The template for the configuration field.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup.router\n<sup><sup>[↩ Parent](#alertspecnotificationgroup)</sup></sup>\n\n\n\nThe router for notifications (Notification Center feature).\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>notifyOn</b></td>\n        <td>enum</td>\n        <td>\n          When to notify.<br/>\n          <br/>\n            <i>Enum</i>: triggeredOnly, triggeredAndResolved<br/>\n            <i>Default</i>: triggeredOnly<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup.webhooks[index]\n<sup><sup>[↩ Parent](#alertspecnotificationgroup)</sup></sup>\n\n\n\nSettings for a notification webhook.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecnotificationgroupwebhooksindexintegration">integration</a></b></td>\n        <td>object</td>\n        <td>\n          Type and spec of webhook.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.integrationRef) || has(self.recipients): Exactly one of integrationRef or recipients is required</li>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>notifyOn</b></td>\n        <td>enum</td>\n        <td>\n          When to notify.<br/>\n          <br/>\n            <i>Enum</i>: triggeredOnly, triggeredAndResolved<br/>\n            <i>Default</i>: triggeredOnly<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupwebhooksindexretriggeringperiod">retriggeringPeriod</a></b></td>\n        <td>object</td>\n        <td>\n          When to re-trigger.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup.webhooks[index].integration\n<sup><sup>[↩ Parent](#alertspecnotificationgroupwebhooksindex)</sup></sup>\n\n\n\nType and spec of webhook.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecnotificationgroupwebhooksindexintegrationintegrationref">integrationRef</a></b></td>\n        <td>object</td>\n        <td>\n          Reference to the webhook.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.backendRef) || has(self.resourceRef): Exactly one of backendRef or resourceRef is required</li>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>recipients</b></td>\n        <td>[]string</td>\n        <td>\n          Recipients for the notification.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup.webhooks[index].integration.integrationRef\n<sup><sup>[↩ Parent](#alertspecnotificationgroupwebhooksindexintegration)</sup></sup>\n\n\n\nReference to the webhook.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecnotificationgroupwebhooksindexintegrationintegrationrefbackendref">backendRef</a></b></td>\n        <td>object</td>\n        <td>\n          Backend reference for the outbound webhook.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.id) != has(self.name): One of id or name is required</li>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupwebhooksindexintegrationintegrationrefresourceref">resourceRef</a></b></td>\n        <td>object</td>\n        <td>\n          Resource reference for use with the alert notification.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup.webhooks[index].integration.integrationRef.backendRef\n<sup><sup>[↩ Parent](#alertspecnotificationgroupwebhooksindexintegrationintegrationref)</sup></sup>\n\n\n\nBackend reference for the outbound webhook.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>id</b></td>\n        <td>integer</td>\n        <td>\n          Webhook Id.<br/>\n          <br/>\n            <i>Format</i>: int32<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the webhook.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup.webhooks[index].integration.integrationRef.resourceRef\n<sup><sup>[↩ Parent](#alertspecnotificationgroupwebhooksindexintegrationintegrationref)</sup></sup>\n\n\n\nResource reference for use with the alert notification.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the resource.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>namespace</b></td>\n        <td>string</td>\n        <td>\n          Kubernetes namespace.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroup.webhooks[index].retriggeringPeriod\n<sup><sup>[↩ Parent](#alertspecnotificationgroupwebhooksindex)</sup></sup>\n\n\n\nWhen to re-trigger.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>minutes</b></td>\n        <td>integer</td>\n        <td>\n          Delay between re-triggered alerts.<br/>\n          <br/>\n            <i>Format</i>: int32<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index]\n<sup><sup>[↩ Parent](#alertspec)</sup></sup>\n\n\n\nNotification group to use for alert notifications.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindexdestinationsindex">destinations</a></b></td>\n        <td>[]object</td>\n        <td>\n          The destinations for notifications (Notification Center feature).<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>groupByKeys</b></td>\n        <td>[]string</td>\n        <td>\n          Group notification by these keys.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindexrouter">router</a></b></td>\n        <td>object</td>\n        <td>\n          The router for notifications (Notification Center feature).<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindexwebhooksindex">webhooks</a></b></td>\n        <td>[]object</td>\n        <td>\n          Webhooks to trigger for notifications.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index].destinations[index]\n<sup><sup>[↩ Parent](#alertspecnotificationgroupexcessindex)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindexdestinationsindexconnector">connector</a></b></td>\n        <td>object</td>\n        <td>\n          Connector is the connector for the destination. Should be one of backendRef or resourceRef.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.backendRef) != has(self.resourceRef): Exactly one of backendRef or resourceRef must be set</li>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>notifyOn</b></td>\n        <td>enum</td>\n        <td>\n          When to notify.<br/>\n          <br/>\n            <i>Enum</i>: triggeredOnly, triggeredAndResolved<br/>\n            <i>Default</i>: triggeredOnly<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindexdestinationsindextriggeredroutingoverrides">triggeredRoutingOverrides</a></b></td>\n        <td>object</td>\n        <td>\n          The routing configuration to override from the connector/preset for triggered notifications.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindexdestinationsindexpreset">preset</a></b></td>\n        <td>object</td>\n        <td>\n          Preset is the preset for the destination. Should be one of backendRef or resourceRef.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.backendRef) != has(self.resourceRef): Exactly one of backendRef or resourceRef must be set</li>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindexdestinationsindexresolvedroutingoverrides">resolvedRoutingOverrides</a></b></td>\n        <td>object</td>\n        <td>\n          Optional routing configuration to override from the connector/preset for resolved notifications.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index].destinations[index].connector\n<sup><sup>[↩ Parent](#alertspecnotificationgroupexcessindexdestinationsindex)</sup></sup>\n\n\n\nConnector is the connector for the destination. Should be one of backendRef or resourceRef.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindexdestinationsindexconnectorbackendref">backendRef</a></b></td>\n        <td>object</td>\n        <td>\n          BackendRef is a reference to a backend resource.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindexdestinationsindexconnectorresourceref">resourceRef</a></b></td>\n        <td>object</td>\n        <td>\n          ResourceRef is a reference to a Kubernetes resource.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index].destinations[index].connector.backendRef\n<sup><sup>[↩ Parent](#alertspecnotificationgroupexcessindexdestinationsindexconnector)</sup></sup>\n\n\n\nBackendRef is a reference to a backend resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index].destinations[index].connector.resourceRef\n<sup><sup>[↩ Parent](#alertspecnotificationgroupexcessindexdestinationsindexconnector)</sup></sup>\n\n\n\nResourceRef is a reference to a Kubernetes resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the resource.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>namespace</b></td>\n        <td>string</td>\n        <td>\n          Kubernetes namespace.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index].destinations[index].triggeredRoutingOverrides\n<sup><sup>[↩ Parent](#alertspecnotificationgroupexcessindexdestinationsindex)</sup></sup>\n\n\n\nThe routing configuration to override from the connector/preset for triggered notifications.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindexdestinationsindextriggeredroutingoverridesconfigoverrides">configOverrides</a></b></td>\n        <td>object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index].destinations[index].triggeredRoutingOverrides.configOverrides\n<sup><sup>[↩ Parent](#alertspecnotificationgroupexcessindexdestinationsindextriggeredroutingoverrides)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>outputSchemaId</b></td>\n        <td>string</td>\n        <td>\n          The ID of the output schema to use for routing notifications<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindexdestinationsindextriggeredroutingoverridesconfigoverridesconnectorconfigfieldsindex">connectorConfigFields</a></b></td>\n        <td>[]object</td>\n        <td>\n          Connector configuration fields.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindexdestinationsindextriggeredroutingoverridesconfigoverridesmessageconfigfieldsindex">messageConfigFields</a></b></td>\n        <td>[]object</td>\n        <td>\n          Notification message configuration fields.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index].destinations[index].triggeredRoutingOverrides.configOverrides.connectorConfigFields[index]\n<sup><sup>[↩ Parent](#alertspecnotificationgroupexcessindexdestinationsindextriggeredroutingoverridesconfigoverrides)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>fieldName</b></td>\n        <td>string</td>\n        <td>\n          The name of the configuration field.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>template</b></td>\n        <td>string</td>\n        <td>\n          The template for the configuration field.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index].destinations[index].triggeredRoutingOverrides.configOverrides.messageConfigFields[index]\n<sup><sup>[↩ Parent](#alertspecnotificationgroupexcessindexdestinationsindextriggeredroutingoverridesconfigoverrides)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>fieldName</b></td>\n        <td>string</td>\n        <td>\n          The name of the configuration field.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>template</b></td>\n        <td>string</td>\n        <td>\n          The template for the configuration field.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index].destinations[index].preset\n<sup><sup>[↩ Parent](#alertspecnotificationgroupexcessindexdestinationsindex)</sup></sup>\n\n\n\nPreset is the preset for the destination. Should be one of backendRef or resourceRef.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindexdestinationsindexpresetbackendref">backendRef</a></b></td>\n        <td>object</td>\n        <td>\n          BackendRef is a reference to a backend resource.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindexdestinationsindexpresetresourceref">resourceRef</a></b></td>\n        <td>object</td>\n        <td>\n          ResourceRef is a reference to a Kubernetes resource.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index].destinations[index].preset.backendRef\n<sup><sup>[↩ Parent](#alertspecnotificationgroupexcessindexdestinationsindexpreset)</sup></sup>\n\n\n\nBackendRef is a reference to a backend resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index].destinations[index].preset.resourceRef\n<sup><sup>[↩ Parent](#alertspecnotificationgroupexcessindexdestinationsindexpreset)</sup></sup>\n\n\n\nResourceRef is a reference to a Kubernetes resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the resource.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>namespace</b></td>\n        <td>string</td>\n        <td>\n          Kubernetes namespace.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index].destinations[index].resolvedRoutingOverrides\n<sup><sup>[↩ Parent](#alertspecnotificationgroupexcessindexdestinationsindex)</sup></sup>\n\n\n\nOptional routing configuration to override from the connector/preset for resolved notifications.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindexdestinationsindexresolvedroutingoverridesconfigoverrides">configOverrides</a></b></td>\n        <td>object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index].destinations[index].resolvedRoutingOverrides.configOverrides\n<sup><sup>[↩ Parent](#alertspecnotificationgroupexcessindexdestinationsindexresolvedroutingoverrides)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>outputSchemaId</b></td>\n        <td>string</td>\n        <td>\n          The ID of the output schema to use for routing notifications<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindexdestinationsindexresolvedroutingoverridesconfigoverridesconnectorconfigfieldsindex">connectorConfigFields</a></b></td>\n        <td>[]object</td>\n        <td>\n          Connector configuration fields.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindexdestinationsindexresolvedroutingoverridesconfigoverridesmessageconfigfieldsindex">messageConfigFields</a></b></td>\n        <td>[]object</td>\n        <td>\n          Notification message configuration fields.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index].destinations[index].resolvedRoutingOverrides.configOverrides.connectorConfigFields[index]\n<sup><sup>[↩ Parent](#alertspecnotificationgroupexcessindexdestinationsindexresolvedroutingoverridesconfigoverrides)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>fieldName</b></td>\n        <td>string</td>\n        <td>\n          The name of the configuration field.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>template</b></td>\n        <td>string</td>\n        <td>\n          The template for the configuration field.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index].destinations[index].resolvedRoutingOverrides.configOverrides.messageConfigFields[index]\n<sup><sup>[↩ Parent](#alertspecnotificationgroupexcessindexdestinationsindexresolvedroutingoverridesconfigoverrides)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>fieldName</b></td>\n        <td>string</td>\n        <td>\n          The name of the configuration field.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>template</b></td>\n        <td>string</td>\n        <td>\n          The template for the configuration field.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index].router\n<sup><sup>[↩ Parent](#alertspecnotificationgroupexcessindex)</sup></sup>\n\n\n\nThe router for notifications (Notification Center feature).\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>notifyOn</b></td>\n        <td>enum</td>\n        <td>\n          When to notify.<br/>\n          <br/>\n            <i>Enum</i>: triggeredOnly, triggeredAndResolved<br/>\n            <i>Default</i>: triggeredOnly<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index].webhooks[index]\n<sup><sup>[↩ Parent](#alertspecnotificationgroupexcessindex)</sup></sup>\n\n\n\nSettings for a notification webhook.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindexwebhooksindexintegration">integration</a></b></td>\n        <td>object</td>\n        <td>\n          Type and spec of webhook.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.integrationRef) || has(self.recipients): Exactly one of integrationRef or recipients is required</li>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>notifyOn</b></td>\n        <td>enum</td>\n        <td>\n          When to notify.<br/>\n          <br/>\n            <i>Enum</i>: triggeredOnly, triggeredAndResolved<br/>\n            <i>Default</i>: triggeredOnly<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindexwebhooksindexretriggeringperiod">retriggeringPeriod</a></b></td>\n        <td>object</td>\n        <td>\n          When to re-trigger.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index].webhooks[index].integration\n<sup><sup>[↩ Parent](#alertspecnotificationgroupexcessindexwebhooksindex)</sup></sup>\n\n\n\nType and spec of webhook.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindexwebhooksindexintegrationintegrationref">integrationRef</a></b></td>\n        <td>object</td>\n        <td>\n          Reference to the webhook.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.backendRef) || has(self.resourceRef): Exactly one of backendRef or resourceRef is required</li>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>recipients</b></td>\n        <td>[]string</td>\n        <td>\n          Recipients for the notification.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index].webhooks[index].integration.integrationRef\n<sup><sup>[↩ Parent](#alertspecnotificationgroupexcessindexwebhooksindexintegration)</sup></sup>\n\n\n\nReference to the webhook.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindexwebhooksindexintegrationintegrationrefbackendref">backendRef</a></b></td>\n        <td>object</td>\n        <td>\n          Backend reference for the outbound webhook.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.id) != has(self.name): One of id or name is required</li>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertspecnotificationgroupexcessindexwebhooksindexintegrationintegrationrefresourceref">resourceRef</a></b></td>\n        <td>object</td>\n        <td>\n          Resource reference for use with the alert notification.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index].webhooks[index].integration.integrationRef.backendRef\n<sup><sup>[↩ Parent](#alertspecnotificationgroupexcessindexwebhooksindexintegrationintegrationref)</sup></sup>\n\n\n\nBackend reference for the outbound webhook.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>id</b></td>\n        <td>integer</td>\n        <td>\n          Webhook Id.<br/>\n          <br/>\n            <i>Format</i>: int32<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the webhook.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index].webhooks[index].integration.integrationRef.resourceRef\n<sup><sup>[↩ Parent](#alertspecnotificationgroupexcessindexwebhooksindexintegrationintegrationref)</sup></sup>\n\n\n\nResource reference for use with the alert notification.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the resource.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>namespace</b></td>\n        <td>string</td>\n        <td>\n          Kubernetes namespace.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.notificationGroupExcess[index].webhooks[index].retriggeringPeriod\n<sup><sup>[↩ Parent](#alertspecnotificationgroupexcessindexwebhooksindex)</sup></sup>\n\n\n\nWhen to re-trigger.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>minutes</b></td>\n        <td>integer</td>\n        <td>\n          Delay between re-triggered alerts.<br/>\n          <br/>\n            <i>Format</i>: int32<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.schedule\n<sup><sup>[↩ Parent](#alertspec)</sup></sup>\n\n\n\nAlert activity schedule. Will be activated all the time if not specified.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>timeZone</b></td>\n        <td>string</td>\n        <td>\n          Time zone.<br/>\n          <br/>\n            <i>Default</i>: UTC+00<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertspecscheduleactiveon">activeOn</a></b></td>\n        <td>object</td>\n        <td>\n          Schedule to have the alert active.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.spec.schedule.activeOn\n<sup><sup>[↩ Parent](#alertspecschedule)</sup></sup>\n\n\n\nSchedule to have the alert active.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>dayOfWeek</b></td>\n        <td>[]enum</td>\n        <td>\n          <br/>\n          <br/>\n            <i>Enum</i>: sunday, monday, tuesday, wednesday, thursday, friday, saturday<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>endTime</b></td>\n        <td>string</td>\n        <td>\n          Time of day.<br/>\n          <br/>\n            <i>Default</i>: 23:59<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>startTime</b></td>\n        <td>string</td>\n        <td>\n          Time of day.<br/>\n          <br/>\n            <i>Default</i>: 00:00<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.status\n<sup><sup>[↩ Parent](#alert)</sup></sup>\n\n\n\nAlertStatus defines the observed state of Alert\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertstatusconditionsindex">conditions</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Alert.status.conditions[index]\n<sup><sup>[↩ Parent](#alertstatus)</sup></sup>\n\n\n\nCondition contains details for one aspect of the current state of this API Resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>lastTransitionTime</b></td>\n        <td>string</td>\n        <td>\n          lastTransitionTime is the last time the condition transitioned from one status to another.\nThis should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.<br/>\n          <br/>\n            <i>Format</i>: date-time<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>message</b></td>\n        <td>string</td>\n        <td>\n          message is a human readable message indicating details about the transition.\nThis may be an empty string.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>reason</b></td>\n        <td>string</td>\n        <td>\n          reason contains a programmatic identifier indicating the reason for the condition's last transition.\nProducers of specific condition types may define expected values and meanings for this field,\nand whether the values are considered a guaranteed API.\nThe value should be a CamelCase string.\nThis field may not be empty.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>status</b></td>\n        <td>enum</td>\n        <td>\n          status of the condition, one of True, False, Unknown.<br/>\n          <br/>\n            <i>Enum</i>: True, False, Unknown<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>type</b></td>\n        <td>string</td>\n        <td>\n          type of condition in CamelCase or in foo.example.com/CamelCase.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>observedGeneration</b></td>\n        <td>integer</td>\n        <td>\n          observedGeneration represents the .metadata.generation that the condition was set based upon.\nFor instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date\nwith respect to the current state of the instance.<br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n            <i>Minimum</i>: 0<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n# coralogix.com/v1alpha1\n\nResource Types:\n\n- [AlertScheduler](#alertscheduler)\n\n- [ApiKey](#apikey)\n\n- [Connector](#connector)\n\n- [CustomRole](#customrole)\n\n- [Dashboard](#dashboard)\n\n- [DashboardsFolder](#dashboardsfolder)\n\n- [GlobalRouter](#globalrouter)\n\n- [Group](#group)\n\n- [Integration](#integration)\n\n- [OutboundWebhook](#outboundwebhook)\n\n- [Preset](#preset)\n\n- [RecordingRuleGroupSet](#recordingrulegroupset)\n\n- [RuleGroup](#rulegroup)\n\n- [Scope](#scope)\n\n- [TCOLogsPolicies](#tcologspolicies)\n\n- [TCOTracesPolicies](#tcotracespolicies)\n\n- [ViewFolder](#viewfolder)\n\n- [View](#view)\n\n\n\n\n## AlertScheduler\n<sup><sup>[↩ Parent](#coralogixcomv1alpha1 )</sup></sup>\n\n\n\n\n\n\nAlertScheduler is the Schema for the alertschedulers API.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n      <td><b>apiVersion</b></td>\n      <td>string</td>\n      <td>coralogix.com/v1alpha1</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b>kind</b></td>\n      <td>string</td>\n      <td>AlertScheduler</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b><a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.27/#objectmeta-v1-meta">metadata</a></b></td>\n      <td>object</td>\n      <td>Refer to the Kubernetes API documentation for the fields of the `metadata` field.</td>\n      <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertschedulerspec">spec</a></b></td>\n        <td>object</td>\n        <td>\n          AlertSchedulerSpec defines the desired state Coralogix AlertScheduler.\nIt is used to suppress or activate alerts based on a schedule.\nSee also https://coralogix.com/docs/user-guides/alerting/alert-suppression-rules/<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertschedulerstatus">status</a></b></td>\n        <td>object</td>\n        <td>\n          AlertSchedulerStatus defines the observed state of AlertScheduler.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### AlertScheduler.spec\n<sup><sup>[↩ Parent](#alertscheduler)</sup></sup>\n\n\n\nAlertSchedulerSpec defines the desired state Coralogix AlertScheduler.\nIt is used to suppress or activate alerts based on a schedule.\nSee also https://coralogix.com/docs/user-guides/alerting/alert-suppression-rules/\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertschedulerspecfilter">filter</a></b></td>\n        <td>object</td>\n        <td>\n          Alert Scheduler filter. Exactly one of `metaLabels` or `alerts` can be set.\nIf none of them set, all alerts will be affected.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.metaLabels) != has(self.alerts): Exactly one of metaLabels or alerts must be set</li>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Alert Scheduler name.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertschedulerspecschedule">schedule</a></b></td>\n        <td>object</td>\n        <td>\n          Alert Scheduler schedule. Exactly one of `oneTime` or `recurring` must be set.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.oneTime) != has(self.recurring): Exactly one of oneTime or recurring must be set</li>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>description</b></td>\n        <td>string</td>\n        <td>\n          Alert Scheduler description.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>enabled</b></td>\n        <td>boolean</td>\n        <td>\n          Alert Scheduler enabled. If set to `false`, the alert scheduler will be disabled. True by default.<br/>\n          <br/>\n            <i>Default</i>: true<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertschedulerspecmetalabelsindex">metaLabels</a></b></td>\n        <td>[]object</td>\n        <td>\n          Alert Scheduler meta labels.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### AlertScheduler.spec.filter\n<sup><sup>[↩ Parent](#alertschedulerspec)</sup></sup>\n\n\n\nAlert Scheduler filter. Exactly one of `metaLabels` or `alerts` can be set.\nIf none of them set, all alerts will be affected.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>whatExpression</b></td>\n        <td>string</td>\n        <td>\n          DataPrime query expression - https://coralogix.com/docs/dataprime-query-language.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertschedulerspecfilteralertsindex">alerts</a></b></td>\n        <td>[]object</td>\n        <td>\n          Alert references. Conflicts with `metaLabels`.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertschedulerspecfiltermetalabelsindex">metaLabels</a></b></td>\n        <td>[]object</td>\n        <td>\n          Alert Scheduler meta labels. Conflicts with `alerts`.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### AlertScheduler.spec.filter.alerts[index]\n<sup><sup>[↩ Parent](#alertschedulerspecfilter)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertschedulerspecfilteralertsindexresourceref">resourceRef</a></b></td>\n        <td>object</td>\n        <td>\n          Alert custom resource name and namespace. If namespace is not set, the AlertScheduler namespace will be used.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### AlertScheduler.spec.filter.alerts[index].resourceRef\n<sup><sup>[↩ Parent](#alertschedulerspecfilteralertsindex)</sup></sup>\n\n\n\nAlert custom resource name and namespace. If namespace is not set, the AlertScheduler namespace will be used.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the resource (not id).<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>namespace</b></td>\n        <td>string</td>\n        <td>\n          Kubernetes namespace.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### AlertScheduler.spec.filter.metaLabels[index]\n<sup><sup>[↩ Parent](#alertschedulerspecfilter)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>key</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>value</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### AlertScheduler.spec.schedule\n<sup><sup>[↩ Parent](#alertschedulerspec)</sup></sup>\n\n\n\nAlert Scheduler schedule. Exactly one of `oneTime` or `recurring` must be set.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>operation</b></td>\n        <td>enum</td>\n        <td>\n          The operation to perform. Can be `mute` or `activate`.<br/>\n          <br/>\n            <i>Enum</i>: mute, activate<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertschedulerspecscheduleonetime">oneTime</a></b></td>\n        <td>object</td>\n        <td>\n          One-time schedule. Conflicts with `recurring`.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.endTime) != has(self.duration): Exactly one of endTime or duration must be set</li>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertschedulerspecschedulerecurring">recurring</a></b></td>\n        <td>object</td>\n        <td>\n          Recurring schedule. Conflicts with `oneTime`.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.always) != has(self.dynamic): Exactly one of always or dynamic must be set</li>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### AlertScheduler.spec.schedule.oneTime\n<sup><sup>[↩ Parent](#alertschedulerspecschedule)</sup></sup>\n\n\n\nOne-time schedule. Conflicts with `recurring`.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>startTime</b></td>\n        <td>string</td>\n        <td>\n          The start time of the time frame. In isodate format. For example, `2021-01-01T00:00:00.000`.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>timezone</b></td>\n        <td>string</td>\n        <td>\n          The timezone of the time frame. For example, `UTC-4` or `UTC+10`.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertschedulerspecscheduleonetimeduration">duration</a></b></td>\n        <td>object</td>\n        <td>\n          The duration from the start time to wait before the operation is performed.\nConflicts with `endTime`.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>endTime</b></td>\n        <td>string</td>\n        <td>\n          The end time of the time frame. In isodate format. For example, `2021-01-01T00:00:00.000`.\nConflicts with `duration`.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### AlertScheduler.spec.schedule.oneTime.duration\n<sup><sup>[↩ Parent](#alertschedulerspecscheduleonetime)</sup></sup>\n\n\n\nThe duration from the start time to wait before the operation is performed.\nConflicts with `endTime`.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>forOver</b></td>\n        <td>integer</td>\n        <td>\n          The number of time units to wait before the alert is triggered. For example,\nif the frequency is set to `hours` and the value is set to `2`, the alert will be triggered after 2 hours.<br/>\n          <br/>\n            <i>Format</i>: int32<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>frequency</b></td>\n        <td>enum</td>\n        <td>\n          The time unit to wait before the alert is triggered. Can be `minutes`, `hours` or `days`.<br/>\n          <br/>\n            <i>Enum</i>: minutes, hours, days<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### AlertScheduler.spec.schedule.recurring\n<sup><sup>[↩ Parent](#alertschedulerspecschedule)</sup></sup>\n\n\n\nRecurring schedule. Conflicts with `oneTime`.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>always</b></td>\n        <td>object</td>\n        <td>\n          Recurring always.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertschedulerspecschedulerecurringdynamic">dynamic</a></b></td>\n        <td>object</td>\n        <td>\n          Dynamic schedule.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### AlertScheduler.spec.schedule.recurring.dynamic\n<sup><sup>[↩ Parent](#alertschedulerspecschedulerecurring)</sup></sup>\n\n\n\nDynamic schedule.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertschedulerspecschedulerecurringdynamicfrequency">frequency</a></b></td>\n        <td>object</td>\n        <td>\n          The rule will be activated in a recurring mode (daily, weekly or monthly).<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>repeatEvery</b></td>\n        <td>integer</td>\n        <td>\n          The rule will be activated in a recurring mode according to the interval.<br/>\n          <br/>\n            <i>Format</i>: int32<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertschedulerspecschedulerecurringdynamictimeframe">timeFrame</a></b></td>\n        <td>object</td>\n        <td>\n          The time frame of the rule.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.endTime) != has(self.duration): Exactly one of endTime or duration must be set</li>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>terminationDate</b></td>\n        <td>string</td>\n        <td>\n          The termination date of the rule.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### AlertScheduler.spec.schedule.recurring.dynamic.frequency\n<sup><sup>[↩ Parent](#alertschedulerspecschedulerecurringdynamic)</sup></sup>\n\n\n\nThe rule will be activated in a recurring mode (daily, weekly or monthly).\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>daily</b></td>\n        <td>object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertschedulerspecschedulerecurringdynamicfrequencymonthly">monthly</a></b></td>\n        <td>object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#alertschedulerspecschedulerecurringdynamicfrequencyweekly">weekly</a></b></td>\n        <td>object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### AlertScheduler.spec.schedule.recurring.dynamic.frequency.monthly\n<sup><sup>[↩ Parent](#alertschedulerspecschedulerecurringdynamicfrequency)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>days</b></td>\n        <td>[]integer</td>\n        <td>\n          The days of the month to activate the rule.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### AlertScheduler.spec.schedule.recurring.dynamic.frequency.weekly\n<sup><sup>[↩ Parent](#alertschedulerspecschedulerecurringdynamicfrequency)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>days</b></td>\n        <td>[]enum</td>\n        <td>\n          The days of the week to activate the rule.<br/>\n          <br/>\n            <i>Enum</i>: Sunday, Monday, Tuesday, Wednesday, Thursday, Friday, Saturday<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### AlertScheduler.spec.schedule.recurring.dynamic.timeFrame\n<sup><sup>[↩ Parent](#alertschedulerspecschedulerecurringdynamic)</sup></sup>\n\n\n\nThe time frame of the rule.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>startTime</b></td>\n        <td>string</td>\n        <td>\n          The start time of the time frame. In isodate format. For example, `2021-01-01T00:00:00.000`.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>timezone</b></td>\n        <td>string</td>\n        <td>\n          The timezone of the time frame. For example, `UTC-4` or `UTC+10`.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#alertschedulerspecschedulerecurringdynamictimeframeduration">duration</a></b></td>\n        <td>object</td>\n        <td>\n          The duration from the start time to wait before the operation is performed.\nConflicts with `endTime`.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>endTime</b></td>\n        <td>string</td>\n        <td>\n          The end time of the time frame. In isodate format. For example, `2021-01-01T00:00:00.000`.\nConflicts with `duration`.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### AlertScheduler.spec.schedule.recurring.dynamic.timeFrame.duration\n<sup><sup>[↩ Parent](#alertschedulerspecschedulerecurringdynamictimeframe)</sup></sup>\n\n\n\nThe duration from the start time to wait before the operation is performed.\nConflicts with `endTime`.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>forOver</b></td>\n        <td>integer</td>\n        <td>\n          The number of time units to wait before the alert is triggered. For example,\nif the frequency is set to `hours` and the value is set to `2`, the alert will be triggered after 2 hours.<br/>\n          <br/>\n            <i>Format</i>: int32<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>frequency</b></td>\n        <td>enum</td>\n        <td>\n          The time unit to wait before the alert is triggered. Can be `minutes`, `hours` or `days`.<br/>\n          <br/>\n            <i>Enum</i>: minutes, hours, days<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### AlertScheduler.spec.metaLabels[index]\n<sup><sup>[↩ Parent](#alertschedulerspec)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>key</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>value</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### AlertScheduler.status\n<sup><sup>[↩ Parent](#alertscheduler)</sup></sup>\n\n\n\nAlertSchedulerStatus defines the observed state of AlertScheduler.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#alertschedulerstatusconditionsindex">conditions</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### AlertScheduler.status.conditions[index]\n<sup><sup>[↩ Parent](#alertschedulerstatus)</sup></sup>\n\n\n\nCondition contains details for one aspect of the current state of this API Resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>lastTransitionTime</b></td>\n        <td>string</td>\n        <td>\n          lastTransitionTime is the last time the condition transitioned from one status to another.\nThis should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.<br/>\n          <br/>\n            <i>Format</i>: date-time<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>message</b></td>\n        <td>string</td>\n        <td>\n          message is a human readable message indicating details about the transition.\nThis may be an empty string.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>reason</b></td>\n        <td>string</td>\n        <td>\n          reason contains a programmatic identifier indicating the reason for the condition's last transition.\nProducers of specific condition types may define expected values and meanings for this field,\nand whether the values are considered a guaranteed API.\nThe value should be a CamelCase string.\nThis field may not be empty.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>status</b></td>\n        <td>enum</td>\n        <td>\n          status of the condition, one of True, False, Unknown.<br/>\n          <br/>\n            <i>Enum</i>: True, False, Unknown<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>type</b></td>\n        <td>string</td>\n        <td>\n          type of condition in CamelCase or in foo.example.com/CamelCase.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>observedGeneration</b></td>\n        <td>integer</td>\n        <td>\n          observedGeneration represents the .metadata.generation that the condition was set based upon.\nFor instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date\nwith respect to the current state of the instance.<br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n            <i>Minimum</i>: 0<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n## ApiKey\n<sup><sup>[↩ Parent](#coralogixcomv1alpha1 )</sup></sup>\n\n\n\n\n\n\nApiKey is the Schema for the apikeys API.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n      <td><b>apiVersion</b></td>\n      <td>string</td>\n      <td>coralogix.com/v1alpha1</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b>kind</b></td>\n      <td>string</td>\n      <td>ApiKey</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b><a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.27/#objectmeta-v1-meta">metadata</a></b></td>\n      <td>object</td>\n      <td>Refer to the Kubernetes API documentation for the fields of the `metadata` field.</td>\n      <td>true</td>\n      </tr><tr>\n        <td><b><a href="#apikeyspec">spec</a></b></td>\n        <td>object</td>\n        <td>\n          ApiKeySpec defines the desired state of a Coralogix ApiKey.\nSee also https://coralogix.com/docs/user-guides/account-management/api-keys/api-keys/<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.presets) || has(self.permissions): At least one of presets or permissions must be set</li>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#apikeystatus">status</a></b></td>\n        <td>object</td>\n        <td>\n          ApiKeyStatus defines the observed state of ApiKey.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### ApiKey.spec\n<sup><sup>[↩ Parent](#apikey)</sup></sup>\n\n\n\nApiKeySpec defines the desired state of a Coralogix ApiKey.\nSee also https://coralogix.com/docs/user-guides/account-management/api-keys/api-keys/\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the ApiKey<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#apikeyspecowner">owner</a></b></td>\n        <td>object</td>\n        <td>\n          Owner of the ApiKey.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.userId) != has(self.teamId): Exactly one of userId or teamId must be set</li>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>active</b></td>\n        <td>boolean</td>\n        <td>\n          Whether the ApiKey Is active.<br/>\n          <br/>\n            <i>Default</i>: true<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>permissions</b></td>\n        <td>[]string</td>\n        <td>\n          Permissions of the ApiKey<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>presets</b></td>\n        <td>[]string</td>\n        <td>\n          Permission Presets that the ApiKey uses.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### ApiKey.spec.owner\n<sup><sup>[↩ Parent](#apikeyspec)</sup></sup>\n\n\n\nOwner of the ApiKey.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>teamId</b></td>\n        <td>integer</td>\n        <td>\n          Team that owns the key.<br/>\n          <br/>\n            <i>Format</i>: int32<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>userId</b></td>\n        <td>string</td>\n        <td>\n          User that owns the key.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### ApiKey.status\n<sup><sup>[↩ Parent](#apikey)</sup></sup>\n\n\n\nApiKeyStatus defines the observed state of ApiKey.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#apikeystatusconditionsindex">conditions</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### ApiKey.status.conditions[index]\n<sup><sup>[↩ Parent](#apikeystatus)</sup></sup>\n\n\n\nCondition contains details for one aspect of the current state of this API Resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>lastTransitionTime</b></td>\n        <td>string</td>\n        <td>\n          lastTransitionTime is the last time the condition transitioned from one status to another.\nThis should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.<br/>\n          <br/>\n            <i>Format</i>: date-time<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>message</b></td>\n        <td>string</td>\n        <td>\n          message is a human readable message indicating details about the transition.\nThis may be an empty string.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>reason</b></td>\n        <td>string</td>\n        <td>\n          reason contains a programmatic identifier indicating the reason for the condition's last transition.\nProducers of specific condition types may define expected values and meanings for this field,\nand whether the values are considered a guaranteed API.\nThe value should be a CamelCase string.\nThis field may not be empty.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>status</b></td>\n        <td>enum</td>\n        <td>\n          status of the condition, one of True, False, Unknown.<br/>\n          <br/>\n            <i>Enum</i>: True, False, Unknown<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>type</b></td>\n        <td>string</td>\n        <td>\n          type of condition in CamelCase or in foo.example.com/CamelCase.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>observedGeneration</b></td>\n        <td>integer</td>\n        <td>\n          observedGeneration represents the .metadata.generation that the condition was set based upon.\nFor instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date\nwith respect to the current state of the instance.<br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n            <i>Minimum</i>: 0<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n## Connector\n<sup><sup>[↩ Parent](#coralogixcomv1alpha1 )</sup></sup>\n\n\n\n\n\n\nConnector is the Schema for the connectors API.\nNOTE: This CRD exposes a new feature and may have breaking changes in future releases.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n      <td><b>apiVersion</b></td>\n      <td>string</td>\n      <td>coralogix.com/v1alpha1</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b>kind</b></td>\n      <td>string</td>\n      <td>Connector</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b><a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.27/#objectmeta-v1-meta">metadata</a></b></td>\n      <td>object</td>\n      <td>Refer to the Kubernetes API documentation for the fields of the `metadata` field.</td>\n      <td>true</td>\n      </tr><tr>\n        <td><b><a href="#connectorspec">spec</a></b></td>\n        <td>object</td>\n        <td>\n          ConnectorSpec defines the desired state of Connector.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#connectorstatus">status</a></b></td>\n        <td>object</td>\n        <td>\n          ConnectorStatus defines the observed state of Connector.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Connector.spec\n<sup><sup>[↩ Parent](#connector)</sup></sup>\n\n\n\nConnectorSpec defines the desired state of Connector.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#connectorspecconnectorconfig">connectorConfig</a></b></td>\n        <td>object</td>\n        <td>\n          ConnectorConfig is the configuration of the connector.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>description</b></td>\n        <td>string</td>\n        <td>\n          Description is the description of the connector.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name is the name of the connector.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>type</b></td>\n        <td>enum</td>\n        <td>\n          Type is the type of the connector. Can be one of slack, genericHttps, or pagerDuty.<br/>\n          <br/>\n            <i>Enum</i>: slack, genericHttps, pagerDuty<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#connectorspecconfigoverridesindex">configOverrides</a></b></td>\n        <td>[]object</td>\n        <td>\n          ConfigOverrides are the entity type config overrides for the connector.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Connector.spec.connectorConfig\n<sup><sup>[↩ Parent](#connectorspec)</sup></sup>\n\n\n\nConnectorConfig is the configuration of the connector.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#connectorspecconnectorconfigfieldsindex">fields</a></b></td>\n        <td>[]object</td>\n        <td>\n          Fields are the fields of the connector config.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Connector.spec.connectorConfig.fields[index]\n<sup><sup>[↩ Parent](#connectorspecconnectorconfig)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>fieldName</b></td>\n        <td>string</td>\n        <td>\n          FieldName is the name of the field. e.g. "channel" for slack.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>value</b></td>\n        <td>string</td>\n        <td>\n          Value is the value of the field.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Connector.spec.configOverrides[index]\n<sup><sup>[↩ Parent](#connectorspec)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>entityType</b></td>\n        <td>enum</td>\n        <td>\n          EntityType is the entity type for the config override. Should equal "alerts".<br/>\n          <br/>\n            <i>Enum</i>: alerts<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#connectorspecconfigoverridesindexfieldsindex">fields</a></b></td>\n        <td>[]object</td>\n        <td>\n          Fields are the templated fields for the config override.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Connector.spec.configOverrides[index].fields[index]\n<sup><sup>[↩ Parent](#connectorspecconfigoverridesindex)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>fieldName</b></td>\n        <td>string</td>\n        <td>\n          FieldName is the name of the field. e.g. "channel" for slack.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>template</b></td>\n        <td>string</td>\n        <td>\n          Template is the template for the field.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Connector.status\n<sup><sup>[↩ Parent](#connector)</sup></sup>\n\n\n\nConnectorStatus defines the observed state of Connector.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#connectorstatusconditionsindex">conditions</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Connector.status.conditions[index]\n<sup><sup>[↩ Parent](#connectorstatus)</sup></sup>\n\n\n\nCondition contains details for one aspect of the current state of this API Resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>lastTransitionTime</b></td>\n        <td>string</td>\n        <td>\n          lastTransitionTime is the last time the condition transitioned from one status to another.\nThis should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.<br/>\n          <br/>\n            <i>Format</i>: date-time<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>message</b></td>\n        <td>string</td>\n        <td>\n          message is a human readable message indicating details about the transition.\nThis may be an empty string.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>reason</b></td>\n        <td>string</td>\n        <td>\n          reason contains a programmatic identifier indicating the reason for the condition's last transition.\nProducers of specific condition types may define expected values and meanings for this field,\nand whether the values are considered a guaranteed API.\nThe value should be a CamelCase string.\nThis field may not be empty.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>status</b></td>\n        <td>enum</td>\n        <td>\n          status of the condition, one of True, False, Unknown.<br/>\n          <br/>\n            <i>Enum</i>: True, False, Unknown<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>type</b></td>\n        <td>string</td>\n        <td>\n          type of condition in CamelCase or in foo.example.com/CamelCase.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>observedGeneration</b></td>\n        <td>integer</td>\n        <td>\n          observedGeneration represents the .metadata.generation that the condition was set based upon.\nFor instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date\nwith respect to the current state of the instance.<br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n            <i>Minimum</i>: 0<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n## CustomRole\n<sup><sup>[↩ Parent](#coralogixcomv1alpha1 )</sup></sup>\n\n\n\n\n\n\nCustomRole is the Schema for the customroles API.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n      <td><b>apiVersion</b></td>\n      <td>string</td>\n      <td>coralogix.com/v1alpha1</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b>kind</b></td>\n      <td>string</td>\n      <td>CustomRole</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b><a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.27/#objectmeta-v1-meta">metadata</a></b></td>\n      <td>object</td>\n      <td>Refer to the Kubernetes API documentation for the fields of the `metadata` field.</td>\n      <td>true</td>\n      </tr><tr>\n        <td><b><a href="#customrolespec">spec</a></b></td>\n        <td>object</td>\n        <td>\n          CustomRoleSpec defines the desired state of a Coralogix Custom Role.\nSee also https://coralogix.com/docs/user-guides/account-management/user-management/create-roles-and-permissions/<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#customrolestatus">status</a></b></td>\n        <td>object</td>\n        <td>\n          CustomRoleStatus defines the observed state of CustomRole.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### CustomRole.spec\n<sup><sup>[↩ Parent](#customrole)</sup></sup>\n\n\n\nCustomRoleSpec defines the desired state of a Coralogix Custom Role.\nSee also https://coralogix.com/docs/user-guides/account-management/user-management/create-roles-and-permissions/\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>description</b></td>\n        <td>string</td>\n        <td>\n          Description of the custom role.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the custom role.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>parentRoleName</b></td>\n        <td>string</td>\n        <td>\n          Parent role name.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>permissions</b></td>\n        <td>[]string</td>\n        <td>\n          Custom role permissions.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### CustomRole.status\n<sup><sup>[↩ Parent](#customrole)</sup></sup>\n\n\n\nCustomRoleStatus defines the observed state of CustomRole.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#customrolestatusconditionsindex">conditions</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### CustomRole.status.conditions[index]\n<sup><sup>[↩ Parent](#customrolestatus)</sup></sup>\n\n\n\nCondition contains details for one aspect of the current state of this API Resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>lastTransitionTime</b></td>\n        <td>string</td>\n        <td>\n          lastTransitionTime is the last time the condition transitioned from one status to another.\nThis should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.<br/>\n          <br/>\n            <i>Format</i>: date-time<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>message</b></td>\n        <td>string</td>\n        <td>\n          message is a human readable message indicating details about the transition.\nThis may be an empty string.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>reason</b></td>\n        <td>string</td>\n        <td>\n          reason contains a programmatic identifier indicating the reason for the condition's last transition.\nProducers of specific condition types may define expected values and meanings for this field,\nand whether the values are considered a guaranteed API.\nThe value should be a CamelCase string.\nThis field may not be empty.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>status</b></td>\n        <td>enum</td>\n        <td>\n          status of the condition, one of True, False, Unknown.<br/>\n          <br/>\n            <i>Enum</i>: True, False, Unknown<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>type</b></td>\n        <td>string</td>\n        <td>\n          type of condition in CamelCase or in foo.example.com/CamelCase.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>observedGeneration</b></td>\n        <td>integer</td>\n        <td>\n          observedGeneration represents the .metadata.generation that the condition was set based upon.\nFor instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date\nwith respect to the current state of the instance.<br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n            <i>Minimum</i>: 0<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n## Dashboard\n<sup><sup>[↩ Parent](#coralogixcomv1alpha1 )</sup></sup>\n\n\n\n\n\n\nDashboard is the Schema for the dashboards API.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n      <td><b>apiVersion</b></td>\n      <td>string</td>\n      <td>coralogix.com/v1alpha1</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b>kind</b></td>\n      <td>string</td>\n      <td>Dashboard</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b><a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.27/#objectmeta-v1-meta">metadata</a></b></td>\n      <td>object</td>\n      <td>Refer to the Kubernetes API documentation for the fields of the `metadata` field.</td>\n      <td>true</td>\n      </tr><tr>\n        <td><b><a href="#dashboardspec">spec</a></b></td>\n        <td>object</td>\n        <td>\n          DashboardSpec defines the desired state of Dashboard.<br/>\n          <br/>\n            <i>Validations</i>:<li>!(has(self.json) && has(self.configMapRef)): Only one of json or configMapRef can be declared at the same time</li>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#dashboardstatus">status</a></b></td>\n        <td>object</td>\n        <td>\n          DashboardStatus defines the observed state of Dashboard.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Dashboard.spec\n<sup><sup>[↩ Parent](#dashboard)</sup></sup>\n\n\n\nDashboardSpec defines the desired state of Dashboard.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#dashboardspecconfigmapref">configMapRef</a></b></td>\n        <td>object</td>\n        <td>\n          model from configmap<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#dashboardspecfolderref">folderRef</a></b></td>\n        <td>object</td>\n        <td>\n          <br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.backendRef) || has(self.resourceRef): One of backendRef or resourceRef is required</li><li>!(has(self.backendRef) && has(self.resourceRef)): Only one of backendRef or resourceRef can be declared at the same time</li>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>gzipJson</b></td>\n        <td>string</td>\n        <td>\n          GzipJson the model's JSON compressed with Gzip. Base64-encoded when in YAML.<br/>\n          <br/>\n            <i>Format</i>: byte<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>json</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Dashboard.spec.configMapRef\n<sup><sup>[↩ Parent](#dashboardspec)</sup></sup>\n\n\n\nmodel from configmap\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>key</b></td>\n        <td>string</td>\n        <td>\n          The key to select.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the referent.\nThis field is effectively required, but due to backwards compatibility is\nallowed to be empty. Instances of this type with an empty value here are\nalmost certainly wrong.\nMore info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names<br/>\n          <br/>\n            <i>Default</i>: <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>optional</b></td>\n        <td>boolean</td>\n        <td>\n          Specify whether the ConfigMap or its key must be defined<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Dashboard.spec.folderRef\n<sup><sup>[↩ Parent](#dashboardspec)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#dashboardspecfolderrefbackendref">backendRef</a></b></td>\n        <td>object</td>\n        <td>\n          <br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.id) || has(self.path): One of id or path is required</li><li>!(has(self.id) && has(self.path)): Only one of id or path can be declared at the same time</li>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#dashboardspecfolderrefresourceref">resourceRef</a></b></td>\n        <td>object</td>\n        <td>\n          Reference to a Coralogix resource within the cluster.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Dashboard.spec.folderRef.backendRef\n<sup><sup>[↩ Parent](#dashboardspecfolderref)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          Reference to a folder by its backend's ID.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>path</b></td>\n        <td>string</td>\n        <td>\n          Reference to a folder by its path (<parent-folder-name-1>/<parent-folder-name-2>/<folder-name>).<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Dashboard.spec.folderRef.resourceRef\n<sup><sup>[↩ Parent](#dashboardspecfolderref)</sup></sup>\n\n\n\nReference to a Coralogix resource within the cluster.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the resource (not id).<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>namespace</b></td>\n        <td>string</td>\n        <td>\n          Kubernetes namespace.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Dashboard.status\n<sup><sup>[↩ Parent](#dashboard)</sup></sup>\n\n\n\nDashboardStatus defines the observed state of Dashboard.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#dashboardstatusconditionsindex">conditions</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Dashboard.status.conditions[index]\n<sup><sup>[↩ Parent](#dashboardstatus)</sup></sup>\n\n\n\nCondition contains details for one aspect of the current state of this API Resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>lastTransitionTime</b></td>\n        <td>string</td>\n        <td>\n          lastTransitionTime is the last time the condition transitioned from one status to another.\nThis should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.<br/>\n          <br/>\n            <i>Format</i>: date-time<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>message</b></td>\n        <td>string</td>\n        <td>\n          message is a human readable message indicating details about the transition.\nThis may be an empty string.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>reason</b></td>\n        <td>string</td>\n        <td>\n          reason contains a programmatic identifier indicating the reason for the condition's last transition.\nProducers of specific condition types may define expected values and meanings for this field,\nand whether the values are considered a guaranteed API.\nThe value should be a CamelCase string.\nThis field may not be empty.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>status</b></td>\n        <td>enum</td>\n        <td>\n          status of the condition, one of True, False, Unknown.<br/>\n          <br/>\n            <i>Enum</i>: True, False, Unknown<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>type</b></td>\n        <td>string</td>\n        <td>\n          type of condition in CamelCase or in foo.example.com/CamelCase.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>observedGeneration</b></td>\n        <td>integer</td>\n        <td>\n          observedGeneration represents the .metadata.generation that the condition was set based upon.\nFor instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date\nwith respect to the current state of the instance.<br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n            <i>Minimum</i>: 0<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n## DashboardsFolder\n<sup><sup>[↩ Parent](#coralogixcomv1alpha1 )</sup></sup>\n\n\n\n\n\n\nDashboardsFolder is the Schema for the dashboardsfolders API.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n      <td><b>apiVersion</b></td>\n      <td>string</td>\n      <td>coralogix.com/v1alpha1</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b>kind</b></td>\n      <td>string</td>\n      <td>DashboardsFolder</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b><a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.27/#objectmeta-v1-meta">metadata</a></b></td>\n      <td>object</td>\n      <td>Refer to the Kubernetes API documentation for the fields of the `metadata` field.</td>\n      <td>true</td>\n      </tr><tr>\n        <td><b><a href="#dashboardsfolderspec">spec</a></b></td>\n        <td>object</td>\n        <td>\n          DashboardsFolderSpec defines the desired state of DashboardsFolder.<br/>\n          <br/>\n            <i>Validations</i>:<li>!(has(self.parentFolderId) && has(self.parentFolderRef)): Only one of parentFolderID or parentFolderRef can be declared at the same time</li>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#dashboardsfolderstatus">status</a></b></td>\n        <td>object</td>\n        <td>\n          DashboardsFolderStatus defines the observed state of DashboardsFolder.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### DashboardsFolder.spec\n<sup><sup>[↩ Parent](#dashboardsfolder)</sup></sup>\n\n\n\nDashboardsFolderSpec defines the desired state of DashboardsFolder.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>customId</b></td>\n        <td>string</td>\n        <td>\n          A custom ID for the folder. If not provided, a random UUID will be generated. The custom ID is immutable.<br/>\n          <br/>\n            <i>Validations</i>:<li>self == oldSelf: spec.customId is immutable</li>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>parentFolderId</b></td>\n        <td>string</td>\n        <td>\n          A reference to an existing folder by its backend's ID.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#dashboardsfolderspecparentfolderref">parentFolderRef</a></b></td>\n        <td>object</td>\n        <td>\n          A reference to an existing DashboardsFolder CR.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### DashboardsFolder.spec.parentFolderRef\n<sup><sup>[↩ Parent](#dashboardsfolderspec)</sup></sup>\n\n\n\nA reference to an existing DashboardsFolder CR.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the resource (not id).<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>namespace</b></td>\n        <td>string</td>\n        <td>\n          Kubernetes namespace.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### DashboardsFolder.status\n<sup><sup>[↩ Parent](#dashboardsfolder)</sup></sup>\n\n\n\nDashboardsFolderStatus defines the observed state of DashboardsFolder.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#dashboardsfolderstatusconditionsindex">conditions</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### DashboardsFolder.status.conditions[index]\n<sup><sup>[↩ Parent](#dashboardsfolderstatus)</sup></sup>\n\n\n\nCondition contains details for one aspect of the current state of this API Resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>lastTransitionTime</b></td>\n        <td>string</td>\n        <td>\n          lastTransitionTime is the last time the condition transitioned from one status to another.\nThis should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.<br/>\n          <br/>\n            <i>Format</i>: date-time<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>message</b></td>\n        <td>string</td>\n        <td>\n          message is a human readable message indicating details about the transition.\nThis may be an empty string.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>reason</b></td>\n        <td>string</td>\n        <td>\n          reason contains a programmatic identifier indicating the reason for the condition's last transition.\nProducers of specific condition types may define expected values and meanings for this field,\nand whether the values are considered a guaranteed API.\nThe value should be a CamelCase string.\nThis field may not be empty.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>status</b></td>\n        <td>enum</td>\n        <td>\n          status of the condition, one of True, False, Unknown.<br/>\n          <br/>\n            <i>Enum</i>: True, False, Unknown<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>type</b></td>\n        <td>string</td>\n        <td>\n          type of condition in CamelCase or in foo.example.com/CamelCase.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>observedGeneration</b></td>\n        <td>integer</td>\n        <td>\n          observedGeneration represents the .metadata.generation that the condition was set based upon.\nFor instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date\nwith respect to the current state of the instance.<br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n            <i>Minimum</i>: 0<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n## GlobalRouter\n<sup><sup>[↩ Parent](#coralogixcomv1alpha1 )</sup></sup>\n\n\n\n\n\n\nGlobalRouter is the Schema for the globalrouters API.\nNOTE: This CRD exposes a new feature and may have breaking changes in future releases.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n      <td><b>apiVersion</b></td>\n      <td>string</td>\n      <td>coralogix.com/v1alpha1</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b>kind</b></td>\n      <td>string</td>\n      <td>GlobalRouter</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b><a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.27/#objectmeta-v1-meta">metadata</a></b></td>\n      <td>object</td>\n      <td>Refer to the Kubernetes API documentation for the fields of the `metadata` field.</td>\n      <td>true</td>\n      </tr><tr>\n        <td><b><a href="#globalrouterspec">spec</a></b></td>\n        <td>object</td>\n        <td>\n          GlobalRouterSpec defines the desired state of GlobalRouter.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#globalrouterstatus">status</a></b></td>\n        <td>object</td>\n        <td>\n          GlobalRouterStatus defines the observed state of GlobalRouter.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### GlobalRouter.spec\n<sup><sup>[↩ Parent](#globalrouter)</sup></sup>\n\n\n\nGlobalRouterSpec defines the desired state of GlobalRouter.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>description</b></td>\n        <td>string</td>\n        <td>\n          Description is the description of the global router.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>entityType</b></td>\n        <td>enum</td>\n        <td>\n          EntityType is the entity type for the global router. Should equal "alerts".<br/>\n          <br/>\n            <i>Enum</i>: alerts<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name is the name of the global router.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>entityLabels</b></td>\n        <td>map[string]string</td>\n        <td>\n          EntityLabels are optional labels to attach to the global router.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#globalrouterspecfallbackindex">fallback</a></b></td>\n        <td>[]object</td>\n        <td>\n          Fallback is the fallback routing target for the global router.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#globalrouterspecrulesindex">rules</a></b></td>\n        <td>[]object</td>\n        <td>\n          Rules are the routing rules for the global router.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### GlobalRouter.spec.fallback[index]\n<sup><sup>[↩ Parent](#globalrouterspec)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#globalrouterspecfallbackindexconnector">connector</a></b></td>\n        <td>object</td>\n        <td>\n          Connector is the connector for the routing target. Should be one of backendRef or resourceRef.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.backendRef) != has(self.resourceRef): Exactly one of backendRef or resourceRef must be set</li>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>customDetails</b></td>\n        <td>map[string]string</td>\n        <td>\n          CustomDetails are optional custom details to attach to the routing target.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#globalrouterspecfallbackindexpreset">preset</a></b></td>\n        <td>object</td>\n        <td>\n          Preset is the preset for the routing target. Should be one of backendRef or resourceRef.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.backendRef) != has(self.resourceRef): Exactly one of backendRef or resourceRef must be set</li>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### GlobalRouter.spec.fallback[index].connector\n<sup><sup>[↩ Parent](#globalrouterspecfallbackindex)</sup></sup>\n\n\n\nConnector is the connector for the routing target. Should be one of backendRef or resourceRef.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#globalrouterspecfallbackindexconnectorbackendref">backendRef</a></b></td>\n        <td>object</td>\n        <td>\n          BackendRef is a reference to a backend resource.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#globalrouterspecfallbackindexconnectorresourceref">resourceRef</a></b></td>\n        <td>object</td>\n        <td>\n          ResourceRef is a reference to a Kubernetes resource.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### GlobalRouter.spec.fallback[index].connector.backendRef\n<sup><sup>[↩ Parent](#globalrouterspecfallbackindexconnector)</sup></sup>\n\n\n\nBackendRef is a reference to a backend resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### GlobalRouter.spec.fallback[index].connector.resourceRef\n<sup><sup>[↩ Parent](#globalrouterspecfallbackindexconnector)</sup></sup>\n\n\n\nResourceRef is a reference to a Kubernetes resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the resource (not id).<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>namespace</b></td>\n        <td>string</td>\n        <td>\n          Kubernetes namespace.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### GlobalRouter.spec.fallback[index].preset\n<sup><sup>[↩ Parent](#globalrouterspecfallbackindex)</sup></sup>\n\n\n\nPreset is the preset for the routing target. Should be one of backendRef or resourceRef.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#globalrouterspecfallbackindexpresetbackendref">backendRef</a></b></td>\n        <td>object</td>\n        <td>\n          BackendRef is a reference to a backend resource.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#globalrouterspecfallbackindexpresetresourceref">resourceRef</a></b></td>\n        <td>object</td>\n        <td>\n          ResourceRef is a reference to a Kubernetes resource.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### GlobalRouter.spec.fallback[index].preset.backendRef\n<sup><sup>[↩ Parent](#globalrouterspecfallbackindexpreset)</sup></sup>\n\n\n\nBackendRef is a reference to a backend resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### GlobalRouter.spec.fallback[index].preset.resourceRef\n<sup><sup>[↩ Parent](#globalrouterspecfallbackindexpreset)</sup></sup>\n\n\n\nResourceRef is a reference to a Kubernetes resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the resource (not id).<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>namespace</b></td>\n        <td>string</td>\n        <td>\n          Kubernetes namespace.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### GlobalRouter.spec.rules[index]\n<sup><sup>[↩ Parent](#globalrouterspec)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>condition</b></td>\n        <td>string</td>\n        <td>\n          Condition is the condition for the routing rule.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name is the name of the routing rule.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#globalrouterspecrulesindextargetsindex">targets</a></b></td>\n        <td>[]object</td>\n        <td>\n          Targets are the routing targets for the routing rule.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>customDetails</b></td>\n        <td>map[string]string</td>\n        <td>\n          CustomDetails are optional custom details to attach to the routing rule.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### GlobalRouter.spec.rules[index].targets[index]\n<sup><sup>[↩ Parent](#globalrouterspecrulesindex)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#globalrouterspecrulesindextargetsindexconnector">connector</a></b></td>\n        <td>object</td>\n        <td>\n          Connector is the connector for the routing target. Should be one of backendRef or resourceRef.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.backendRef) != has(self.resourceRef): Exactly one of backendRef or resourceRef must be set</li>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>customDetails</b></td>\n        <td>map[string]string</td>\n        <td>\n          CustomDetails are optional custom details to attach to the routing target.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#globalrouterspecrulesindextargetsindexpreset">preset</a></b></td>\n        <td>object</td>\n        <td>\n          Preset is the preset for the routing target. Should be one of backendRef or resourceRef.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.backendRef) != has(self.resourceRef): Exactly one of backendRef or resourceRef must be set</li>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### GlobalRouter.spec.rules[index].targets[index].connector\n<sup><sup>[↩ Parent](#globalrouterspecrulesindextargetsindex)</sup></sup>\n\n\n\nConnector is the connector for the routing target. Should be one of backendRef or resourceRef.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#globalrouterspecrulesindextargetsindexconnectorbackendref">backendRef</a></b></td>\n        <td>object</td>\n        <td>\n          BackendRef is a reference to a backend resource.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#globalrouterspecrulesindextargetsindexconnectorresourceref">resourceRef</a></b></td>\n        <td>object</td>\n        <td>\n          ResourceRef is a reference to a Kubernetes resource.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### GlobalRouter.spec.rules[index].targets[index].connector.backendRef\n<sup><sup>[↩ Parent](#globalrouterspecrulesindextargetsindexconnector)</sup></sup>\n\n\n\nBackendRef is a reference to a backend resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### GlobalRouter.spec.rules[index].targets[index].connector.resourceRef\n<sup><sup>[↩ Parent](#globalrouterspecrulesindextargetsindexconnector)</sup></sup>\n\n\n\nResourceRef is a reference to a Kubernetes resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the resource (not id).<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>namespace</b></td>\n        <td>string</td>\n        <td>\n          Kubernetes namespace.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### GlobalRouter.spec.rules[index].targets[index].preset\n<sup><sup>[↩ Parent](#globalrouterspecrulesindextargetsindex)</sup></sup>\n\n\n\nPreset is the preset for the routing target. Should be one of backendRef or resourceRef.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#globalrouterspecrulesindextargetsindexpresetbackendref">backendRef</a></b></td>\n        <td>object</td>\n        <td>\n          BackendRef is a reference to a backend resource.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#globalrouterspecrulesindextargetsindexpresetresourceref">resourceRef</a></b></td>\n        <td>object</td>\n        <td>\n          ResourceRef is a reference to a Kubernetes resource.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### GlobalRouter.spec.rules[index].targets[index].preset.backendRef\n<sup><sup>[↩ Parent](#globalrouterspecrulesindextargetsindexpreset)</sup></sup>\n\n\n\nBackendRef is a reference to a backend resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### GlobalRouter.spec.rules[index].targets[index].preset.resourceRef\n<sup><sup>[↩ Parent](#globalrouterspecrulesindextargetsindexpreset)</sup></sup>\n\n\n\nResourceRef is a reference to a Kubernetes resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the resource (not id).<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>namespace</b></td>\n        <td>string</td>\n        <td>\n          Kubernetes namespace.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### GlobalRouter.status\n<sup><sup>[↩ Parent](#globalrouter)</sup></sup>\n\n\n\nGlobalRouterStatus defines the observed state of GlobalRouter.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#globalrouterstatusconditionsindex">conditions</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### GlobalRouter.status.conditions[index]\n<sup><sup>[↩ Parent](#globalrouterstatus)</sup></sup>\n\n\n\nCondition contains details for one aspect of the current state of this API Resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>lastTransitionTime</b></td>\n        <td>string</td>\n        <td>\n          lastTransitionTime is the last time the condition transitioned from one status to another.\nThis should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.<br/>\n          <br/>\n            <i>Format</i>: date-time<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>message</b></td>\n        <td>string</td>\n        <td>\n          message is a human readable message indicating details about the transition.\nThis may be an empty string.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>reason</b></td>\n        <td>string</td>\n        <td>\n          reason contains a programmatic identifier indicating the reason for the condition's last transition.\nProducers of specific condition types may define expected values and meanings for this field,\nand whether the values are considered a guaranteed API.\nThe value should be a CamelCase string.\nThis field may not be empty.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>status</b></td>\n        <td>enum</td>\n        <td>\n          status of the condition, one of True, False, Unknown.<br/>\n          <br/>\n            <i>Enum</i>: True, False, Unknown<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>type</b></td>\n        <td>string</td>\n        <td>\n          type of condition in CamelCase or in foo.example.com/CamelCase.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>observedGeneration</b></td>\n        <td>integer</td>\n        <td>\n          observedGeneration represents the .metadata.generation that the condition was set based upon.\nFor instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date\nwith respect to the current state of the instance.<br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n            <i>Minimum</i>: 0<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n## Group\n<sup><sup>[↩ Parent](#coralogixcomv1alpha1 )</sup></sup>\n\n\n\n\n\n\nGroup is the Schema for the groups API.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n      <td><b>apiVersion</b></td>\n      <td>string</td>\n      <td>coralogix.com/v1alpha1</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b>kind</b></td>\n      <td>string</td>\n      <td>Group</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b><a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.27/#objectmeta-v1-meta">metadata</a></b></td>\n      <td>object</td>\n      <td>Refer to the Kubernetes API documentation for the fields of the `metadata` field.</td>\n      <td>true</td>\n      </tr><tr>\n        <td><b><a href="#groupspec">spec</a></b></td>\n        <td>object</td>\n        <td>\n          GroupSpec defines the desired state of Coralogix Group.\nSee also https://coralogix.com/docs/user-guides/account-management/user-management/assign-user-roles-and-scopes-via-groups/<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#groupstatus">status</a></b></td>\n        <td>object</td>\n        <td>\n          GroupStatus defines the observed state of Group.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Group.spec\n<sup><sup>[↩ Parent](#group)</sup></sup>\n\n\n\nGroupSpec defines the desired state of Coralogix Group.\nSee also https://coralogix.com/docs/user-guides/account-management/user-management/assign-user-roles-and-scopes-via-groups/\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#groupspeccustomrolesindex">customRoles</a></b></td>\n        <td>[]object</td>\n        <td>\n          Custom roles applied to the group.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the group.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>description</b></td>\n        <td>string</td>\n        <td>\n          Description of the group.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#groupspecmembersindex">members</a></b></td>\n        <td>[]object</td>\n        <td>\n          Members of the group.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#groupspecscope">scope</a></b></td>\n        <td>object</td>\n        <td>\n          Scope attached to the group.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Group.spec.customRoles[index]\n<sup><sup>[↩ Parent](#groupspec)</sup></sup>\n\n\n\nCustom role reference.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#groupspeccustomrolesindexresourceref">resourceRef</a></b></td>\n        <td>object</td>\n        <td>\n          Reference to the custom role within the cluster.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Group.spec.customRoles[index].resourceRef\n<sup><sup>[↩ Parent](#groupspeccustomrolesindex)</sup></sup>\n\n\n\nReference to the custom role within the cluster.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the resource (not id).<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>namespace</b></td>\n        <td>string</td>\n        <td>\n          Kubernetes namespace.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Group.spec.members[index]\n<sup><sup>[↩ Parent](#groupspec)</sup></sup>\n\n\n\nUser on Coralogix.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>userName</b></td>\n        <td>string</td>\n        <td>\n          User's name.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Group.spec.scope\n<sup><sup>[↩ Parent](#groupspec)</sup></sup>\n\n\n\nScope attached to the group.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#groupspecscoperesourceref">resourceRef</a></b></td>\n        <td>object</td>\n        <td>\n          Scope reference.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Group.spec.scope.resourceRef\n<sup><sup>[↩ Parent](#groupspecscope)</sup></sup>\n\n\n\nScope reference.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the resource (not id).<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>namespace</b></td>\n        <td>string</td>\n        <td>\n          Kubernetes namespace.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Group.status\n<sup><sup>[↩ Parent](#group)</sup></sup>\n\n\n\nGroupStatus defines the observed state of Group.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#groupstatusconditionsindex">conditions</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Group.status.conditions[index]\n<sup><sup>[↩ Parent](#groupstatus)</sup></sup>\n\n\n\nCondition contains details for one aspect of the current state of this API Resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>lastTransitionTime</b></td>\n        <td>string</td>\n        <td>\n          lastTransitionTime is the last time the condition transitioned from one status to another.\nThis should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.<br/>\n          <br/>\n            <i>Format</i>: date-time<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>message</b></td>\n        <td>string</td>\n        <td>\n          message is a human readable message indicating details about the transition.\nThis may be an empty string.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>reason</b></td>\n        <td>string</td>\n        <td>\n          reason contains a programmatic identifier indicating the reason for the condition's last transition.\nProducers of specific condition types may define expected values and meanings for this field,\nand whether the values are considered a guaranteed API.\nThe value should be a CamelCase string.\nThis field may not be empty.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>status</b></td>\n        <td>enum</td>\n        <td>\n          status of the condition, one of True, False, Unknown.<br/>\n          <br/>\n            <i>Enum</i>: True, False, Unknown<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>type</b></td>\n        <td>string</td>\n        <td>\n          type of condition in CamelCase or in foo.example.com/CamelCase.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>observedGeneration</b></td>\n        <td>integer</td>\n        <td>\n          observedGeneration represents the .metadata.generation that the condition was set based upon.\nFor instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date\nwith respect to the current state of the instance.<br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n            <i>Minimum</i>: 0<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n## Integration\n<sup><sup>[↩ Parent](#coralogixcomv1alpha1 )</sup></sup>\n\n\n\n\n\n\nIntegration is the Schema for the integrations API.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n      <td><b>apiVersion</b></td>\n      <td>string</td>\n      <td>coralogix.com/v1alpha1</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b>kind</b></td>\n      <td>string</td>\n      <td>Integration</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b><a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.27/#objectmeta-v1-meta">metadata</a></b></td>\n      <td>object</td>\n      <td>Refer to the Kubernetes API documentation for the fields of the `metadata` field.</td>\n      <td>true</td>\n      </tr><tr>\n        <td><b><a href="#integrationspec">spec</a></b></td>\n        <td>object</td>\n        <td>\n          IntegrationSpec defines the desired state of a Coralogix (managed) integration.\nSee also https://coralogix.com/docs/user-guides/getting-started/packages-and-extensions/integration-packages/\n\nFor available integrations see https://coralogix.com/docs/developer-portal/infrastructure-as-code/terraform-provider/integrations/aws-metrics-collector/ or at https://github.com/coralogix/coralogix-operator/tree/main/config/samples/v1alpha1/integrations.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#integrationstatus">status</a></b></td>\n        <td>object</td>\n        <td>\n          IntegrationStatus defines the observed state of Integration.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Integration.spec\n<sup><sup>[↩ Parent](#integration)</sup></sup>\n\n\n\nIntegrationSpec defines the desired state of a Coralogix (managed) integration.\nSee also https://coralogix.com/docs/user-guides/getting-started/packages-and-extensions/integration-packages/\n\nFor available integrations see https://coralogix.com/docs/developer-portal/infrastructure-as-code/terraform-provider/integrations/aws-metrics-collector/ or at https://github.com/coralogix/coralogix-operator/tree/main/config/samples/v1alpha1/integrations.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>integrationKey</b></td>\n        <td>string</td>\n        <td>\n          Unique name of the integration.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>parameters</b></td>\n        <td>object</td>\n        <td>\n          Parameters required by the integration.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>version</b></td>\n        <td>string</td>\n        <td>\n          Desired version of the integration<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Integration.status\n<sup><sup>[↩ Parent](#integration)</sup></sup>\n\n\n\nIntegrationStatus defines the observed state of Integration.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#integrationstatusconditionsindex">conditions</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Integration.status.conditions[index]\n<sup><sup>[↩ Parent](#integrationstatus)</sup></sup>\n\n\n\nCondition contains details for one aspect of the current state of this API Resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>lastTransitionTime</b></td>\n        <td>string</td>\n        <td>\n          lastTransitionTime is the last time the condition transitioned from one status to another.\nThis should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.<br/>\n          <br/>\n            <i>Format</i>: date-time<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>message</b></td>\n        <td>string</td>\n        <td>\n          message is a human readable message indicating details about the transition.\nThis may be an empty string.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>reason</b></td>\n        <td>string</td>\n        <td>\n          reason contains a programmatic identifier indicating the reason for the condition's last transition.\nProducers of specific condition types may define expected values and meanings for this field,\nand whether the values are considered a guaranteed API.\nThe value should be a CamelCase string.\nThis field may not be empty.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>status</b></td>\n        <td>enum</td>\n        <td>\n          status of the condition, one of True, False, Unknown.<br/>\n          <br/>\n            <i>Enum</i>: True, False, Unknown<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>type</b></td>\n        <td>string</td>\n        <td>\n          type of condition in CamelCase or in foo.example.com/CamelCase.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>observedGeneration</b></td>\n        <td>integer</td>\n        <td>\n          observedGeneration represents the .metadata.generation that the condition was set based upon.\nFor instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date\nwith respect to the current state of the instance.<br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n            <i>Minimum</i>: 0<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n## OutboundWebhook\n<sup><sup>[↩ Parent](#coralogixcomv1alpha1 )</sup></sup>\n\n\n\n\n\n\nOutboundWebhook is the Schema for the API\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n      <td><b>apiVersion</b></td>\n      <td>string</td>\n      <td>coralogix.com/v1alpha1</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b>kind</b></td>\n      <td>string</td>\n      <td>OutboundWebhook</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b><a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.27/#objectmeta-v1-meta">metadata</a></b></td>\n      <td>object</td>\n      <td>Refer to the Kubernetes API documentation for the fields of the `metadata` field.</td>\n      <td>true</td>\n      </tr><tr>\n        <td><b><a href="#outboundwebhookspec">spec</a></b></td>\n        <td>object</td>\n        <td>\n          OutboundWebhookSpec defines the desired state of OutboundWebhook\nSee also https://coralogix.com/docs/user-guides/alerting/outbound-webhooks/aws-eventbridge-outbound-webhook/<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#outboundwebhookstatus">status</a></b></td>\n        <td>object</td>\n        <td>\n          OutboundWebhookStatus defines the observed state of OutboundWebhook<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### OutboundWebhook.spec\n<sup><sup>[↩ Parent](#outboundwebhook)</sup></sup>\n\n\n\nOutboundWebhookSpec defines the desired state of OutboundWebhook\nSee also https://coralogix.com/docs/user-guides/alerting/outbound-webhooks/aws-eventbridge-outbound-webhook/\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the webhook.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#outboundwebhookspecoutboundwebhooktype">outboundWebhookType</a></b></td>\n        <td>object</td>\n        <td>\n          Type of webhook.<br/>\n          <br/>\n            <i>Validations</i>:<li>(has(self.genericWebhook) ? 1 : 0) + (has(self.slack) ? 1 : 0) + (has(self.pagerDuty) ? 1 : 0) + (has(self.sendLog) ? 1 : 0) + (has(self.emailGroup) ? 1 : 0) + (has(self.microsoftTeams) ? 1 : 0) + (has(self.jira) ? 1 : 0) + (has(self.opsgenie) ? 1 : 0) + (has(self.demisto) ? 1 : 0) + (has(self.awsEventBridge) ? 1 : 0) == 1: Exactly one of the following fields must be set: genericWebhook, slack, pagerDuty, sendLog, emailGroup, microsoftTeams, jira, opsgenie, demisto, awsEventBridge</li>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### OutboundWebhook.spec.outboundWebhookType\n<sup><sup>[↩ Parent](#outboundwebhookspec)</sup></sup>\n\n\n\nType of webhook.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#outboundwebhookspecoutboundwebhooktypeawseventbridge">awsEventBridge</a></b></td>\n        <td>object</td>\n        <td>\n          AWS eventbridge message.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#outboundwebhookspecoutboundwebhooktypedemisto">demisto</a></b></td>\n        <td>object</td>\n        <td>\n          Demisto notification.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#outboundwebhookspecoutboundwebhooktypeemailgroup">emailGroup</a></b></td>\n        <td>object</td>\n        <td>\n          Email notification.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#outboundwebhookspecoutboundwebhooktypegenericwebhook">genericWebhook</a></b></td>\n        <td>object</td>\n        <td>\n          Generic HTTP(s) webhook.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#outboundwebhookspecoutboundwebhooktypejira">jira</a></b></td>\n        <td>object</td>\n        <td>\n          Jira issue.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#outboundwebhookspecoutboundwebhooktypemicrosoftteams">microsoftTeams</a></b></td>\n        <td>object</td>\n        <td>\n          Teams message.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#outboundwebhookspecoutboundwebhooktypeopsgenie">opsgenie</a></b></td>\n        <td>object</td>\n        <td>\n          Opsgenie notification.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#outboundwebhookspecoutboundwebhooktypepagerduty">pagerDuty</a></b></td>\n        <td>object</td>\n        <td>\n          PagerDuty notification.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#outboundwebhookspecoutboundwebhooktypesendlog">sendLog</a></b></td>\n        <td>object</td>\n        <td>\n          SendLog notification.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#outboundwebhookspecoutboundwebhooktypeslack">slack</a></b></td>\n        <td>object</td>\n        <td>\n          Slack message.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### OutboundWebhook.spec.outboundWebhookType.awsEventBridge\n<sup><sup>[↩ Parent](#outboundwebhookspecoutboundwebhooktype)</sup></sup>\n\n\n\nAWS eventbridge message.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>detail</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>detailType</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>eventBusArn</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>roleName</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>source</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### OutboundWebhook.spec.outboundWebhookType.demisto\n<sup><sup>[↩ Parent](#outboundwebhookspecoutboundwebhooktype)</sup></sup>\n\n\n\nDemisto notification.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>payload</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>url</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>uuid</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### OutboundWebhook.spec.outboundWebhookType.emailGroup\n<sup><sup>[↩ Parent](#outboundwebhookspecoutboundwebhooktype)</sup></sup>\n\n\n\nEmail notification.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>emailAddresses</b></td>\n        <td>[]string</td>\n        <td>\n          Recipients<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### OutboundWebhook.spec.outboundWebhookType.genericWebhook\n<sup><sup>[↩ Parent](#outboundwebhookspecoutboundwebhooktype)</sup></sup>\n\n\n\nGeneric HTTP(s) webhook.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>method</b></td>\n        <td>enum</td>\n        <td>\n          HTTP Method to use.<br/>\n          <br/>\n            <i>Enum</i>: Unkown, Get, Post, Put<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>url</b></td>\n        <td>string</td>\n        <td>\n          URL to call<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>headers</b></td>\n        <td>map[string]string</td>\n        <td>\n          Attached HTTP headers.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>payload</b></td>\n        <td>string</td>\n        <td>\n          Payload of the webhook call.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### OutboundWebhook.spec.outboundWebhookType.jira\n<sup><sup>[↩ Parent](#outboundwebhookspecoutboundwebhooktype)</sup></sup>\n\n\n\nJira issue.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>apiToken</b></td>\n        <td>string</td>\n        <td>\n          API token<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>email</b></td>\n        <td>string</td>\n        <td>\n          Email address associated with the token<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>projectKey</b></td>\n        <td>string</td>\n        <td>\n          Project to add it to.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>url</b></td>\n        <td>string</td>\n        <td>\n          Jira URL<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### OutboundWebhook.spec.outboundWebhookType.microsoftTeams\n<sup><sup>[↩ Parent](#outboundwebhookspecoutboundwebhooktype)</sup></sup>\n\n\n\nTeams message.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>url</b></td>\n        <td>string</td>\n        <td>\n          Teams URL<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### OutboundWebhook.spec.outboundWebhookType.opsgenie\n<sup><sup>[↩ Parent](#outboundwebhookspecoutboundwebhooktype)</sup></sup>\n\n\n\nOpsgenie notification.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>url</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### OutboundWebhook.spec.outboundWebhookType.pagerDuty\n<sup><sup>[↩ Parent](#outboundwebhookspecoutboundwebhooktype)</sup></sup>\n\n\n\nPagerDuty notification.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>serviceKey</b></td>\n        <td>string</td>\n        <td>\n          PagerDuty service key.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### OutboundWebhook.spec.outboundWebhookType.sendLog\n<sup><sup>[↩ Parent](#outboundwebhookspecoutboundwebhooktype)</sup></sup>\n\n\n\nSendLog notification.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>payload</b></td>\n        <td>string</td>\n        <td>\n          Payload of the notification<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>url</b></td>\n        <td>string</td>\n        <td>\n          Sendlog URL.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### OutboundWebhook.spec.outboundWebhookType.slack\n<sup><sup>[↩ Parent](#outboundwebhookspecoutboundwebhooktype)</sup></sup>\n\n\n\nSlack message.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>url</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#outboundwebhookspecoutboundwebhooktypeslackattachmentsindex">attachments</a></b></td>\n        <td>[]object</td>\n        <td>\n          Attachments of the message.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#outboundwebhookspecoutboundwebhooktypeslackdigestsindex">digests</a></b></td>\n        <td>[]object</td>\n        <td>\n          Digest configuration.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### OutboundWebhook.spec.outboundWebhookType.slack.attachments[index]\n<sup><sup>[↩ Parent](#outboundwebhookspecoutboundwebhooktypeslack)</sup></sup>\n\n\n\nSlack attachment\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>isActive</b></td>\n        <td>boolean</td>\n        <td>\n          Active status.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>type</b></td>\n        <td>string</td>\n        <td>\n          Attachment to the message.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### OutboundWebhook.spec.outboundWebhookType.slack.digests[index]\n<sup><sup>[↩ Parent](#outboundwebhookspecoutboundwebhooktypeslack)</sup></sup>\n\n\n\nDigest config.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>isActive</b></td>\n        <td>boolean</td>\n        <td>\n          Active status.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>type</b></td>\n        <td>string</td>\n        <td>\n          Type of digest to send<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### OutboundWebhook.status\n<sup><sup>[↩ Parent](#outboundwebhook)</sup></sup>\n\n\n\nOutboundWebhookStatus defines the observed state of OutboundWebhook\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#outboundwebhookstatusconditionsindex">conditions</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>externalId</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### OutboundWebhook.status.conditions[index]\n<sup><sup>[↩ Parent](#outboundwebhookstatus)</sup></sup>\n\n\n\nCondition contains details for one aspect of the current state of this API Resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>lastTransitionTime</b></td>\n        <td>string</td>\n        <td>\n          lastTransitionTime is the last time the condition transitioned from one status to another.\nThis should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.<br/>\n          <br/>\n            <i>Format</i>: date-time<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>message</b></td>\n        <td>string</td>\n        <td>\n          message is a human readable message indicating details about the transition.\nThis may be an empty string.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>reason</b></td>\n        <td>string</td>\n        <td>\n          reason contains a programmatic identifier indicating the reason for the condition's last transition.\nProducers of specific condition types may define expected values and meanings for this field,\nand whether the values are considered a guaranteed API.\nThe value should be a CamelCase string.\nThis field may not be empty.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>status</b></td>\n        <td>enum</td>\n        <td>\n          status of the condition, one of True, False, Unknown.<br/>\n          <br/>\n            <i>Enum</i>: True, False, Unknown<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>type</b></td>\n        <td>string</td>\n        <td>\n          type of condition in CamelCase or in foo.example.com/CamelCase.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>observedGeneration</b></td>\n        <td>integer</td>\n        <td>\n          observedGeneration represents the .metadata.generation that the condition was set based upon.\nFor instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date\nwith respect to the current state of the instance.<br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n            <i>Minimum</i>: 0<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n## Preset\n<sup><sup>[↩ Parent](#coralogixcomv1alpha1 )</sup></sup>\n\n\n\n\n\n\nPreset is the Schema for the presets API.\nNOTE: This CRD exposes a new feature and may have breaking changes in future releases.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n      <td><b>apiVersion</b></td>\n      <td>string</td>\n      <td>coralogix.com/v1alpha1</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b>kind</b></td>\n      <td>string</td>\n      <td>Preset</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b><a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.27/#objectmeta-v1-meta">metadata</a></b></td>\n      <td>object</td>\n      <td>Refer to the Kubernetes API documentation for the fields of the `metadata` field.</td>\n      <td>true</td>\n      </tr><tr>\n        <td><b><a href="#presetspec">spec</a></b></td>\n        <td>object</td>\n        <td>\n          PresetSpec defines the desired state of Preset.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#presetstatus">status</a></b></td>\n        <td>object</td>\n        <td>\n          PresetStatus defines the observed state of Preset.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Preset.spec\n<sup><sup>[↩ Parent](#preset)</sup></sup>\n\n\n\nPresetSpec defines the desired state of Preset.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>connectorType</b></td>\n        <td>enum</td>\n        <td>\n          ConnectorType is the type of the connector. Can be one of slack, genericHttps, or pagerDuty.<br/>\n          <br/>\n            <i>Enum</i>: slack, genericHttps, pagerDuty<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>description</b></td>\n        <td>string</td>\n        <td>\n          Description is the description of the preset.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>entityType</b></td>\n        <td>enum</td>\n        <td>\n          EntityType is the entity type for the preset. Should equal "alerts".<br/>\n          <br/>\n            <i>Enum</i>: alerts<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name is the name of the preset.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#presetspecconfigoverridesindex">configOverrides</a></b></td>\n        <td>[]object</td>\n        <td>\n          ConfigOverrides are the entity type configs, allowing entity type templating.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>parentId</b></td>\n        <td>string</td>\n        <td>\n          ParentId is the ID of the parent preset. For example, "preset_system_slack_alerts_basic".<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Preset.spec.configOverrides[index]\n<sup><sup>[↩ Parent](#presetspec)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#presetspecconfigoverridesindexconditiontype">conditionType</a></b></td>\n        <td>object</td>\n        <td>\n          ConditionType is the condition type for the config override.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.matchEntityType) != has(self.matchEntityTypeAndSubType): exactly one of matchEntityType or matchEntityTypeAndSubType must be set</li>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#presetspecconfigoverridesindexmessageconfig">messageConfig</a></b></td>\n        <td>object</td>\n        <td>\n          MessageConfig is the message config for the config override.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>payloadType</b></td>\n        <td>string</td>\n        <td>\n          PayloadType is the payload type for the config override.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Preset.spec.configOverrides[index].conditionType\n<sup><sup>[↩ Parent](#presetspecconfigoverridesindex)</sup></sup>\n\n\n\nConditionType is the condition type for the config override.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>matchEntityType</b></td>\n        <td>object</td>\n        <td>\n          MatchEntityType is used for matching entity types.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#presetspecconfigoverridesindexconditiontypematchentitytypeandsubtype">matchEntityTypeAndSubType</a></b></td>\n        <td>object</td>\n        <td>\n          MatchEntityTypeAndSubType is used for matching entity subtypes.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Preset.spec.configOverrides[index].conditionType.matchEntityTypeAndSubType\n<sup><sup>[↩ Parent](#presetspecconfigoverridesindexconditiontype)</sup></sup>\n\n\n\nMatchEntityTypeAndSubType is used for matching entity subtypes.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>entitySubType</b></td>\n        <td>string</td>\n        <td>\n          EntitySubType is the entity subtype for the config override. For example, "logsImmediateTriggered".<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Preset.spec.configOverrides[index].messageConfig\n<sup><sup>[↩ Parent](#presetspecconfigoverridesindex)</sup></sup>\n\n\n\nMessageConfig is the message config for the config override.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#presetspecconfigoverridesindexmessageconfigfieldsindex">fields</a></b></td>\n        <td>[]object</td>\n        <td>\n          Fields are the fields of the message config.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Preset.spec.configOverrides[index].messageConfig.fields[index]\n<sup><sup>[↩ Parent](#presetspecconfigoverridesindexmessageconfig)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>fieldName</b></td>\n        <td>string</td>\n        <td>\n          FieldName is the name of the field. e.g. "title" for slack.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>template</b></td>\n        <td>string</td>\n        <td>\n          Template is the template for the field.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Preset.status\n<sup><sup>[↩ Parent](#preset)</sup></sup>\n\n\n\nPresetStatus defines the observed state of Preset.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#presetstatusconditionsindex">conditions</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Preset.status.conditions[index]\n<sup><sup>[↩ Parent](#presetstatus)</sup></sup>\n\n\n\nCondition contains details for one aspect of the current state of this API Resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>lastTransitionTime</b></td>\n        <td>string</td>\n        <td>\n          lastTransitionTime is the last time the condition transitioned from one status to another.\nThis should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.<br/>\n          <br/>\n            <i>Format</i>: date-time<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>message</b></td>\n        <td>string</td>\n        <td>\n          message is a human readable message indicating details about the transition.\nThis may be an empty string.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>reason</b></td>\n        <td>string</td>\n        <td>\n          reason contains a programmatic identifier indicating the reason for the condition's last transition.\nProducers of specific condition types may define expected values and meanings for this field,\nand whether the values are considered a guaranteed API.\nThe value should be a CamelCase string.\nThis field may not be empty.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>status</b></td>\n        <td>enum</td>\n        <td>\n          status of the condition, one of True, False, Unknown.<br/>\n          <br/>\n            <i>Enum</i>: True, False, Unknown<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>type</b></td>\n        <td>string</td>\n        <td>\n          type of condition in CamelCase or in foo.example.com/CamelCase.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>observedGeneration</b></td>\n        <td>integer</td>\n        <td>\n          observedGeneration represents the .metadata.generation that the condition was set based upon.\nFor instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date\nwith respect to the current state of the instance.<br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n            <i>Minimum</i>: 0<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n## RecordingRuleGroupSet\n<sup><sup>[↩ Parent](#coralogixcomv1alpha1 )</sup></sup>\n\n\n\n\n\n\nRecordingRuleGroupSet is the Schema for the RecordingRuleGroupSets API\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n      <td><b>apiVersion</b></td>\n      <td>string</td>\n      <td>coralogix.com/v1alpha1</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b>kind</b></td>\n      <td>string</td>\n      <td>RecordingRuleGroupSet</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b><a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.27/#objectmeta-v1-meta">metadata</a></b></td>\n      <td>object</td>\n      <td>Refer to the Kubernetes API documentation for the fields of the `metadata` field.</td>\n      <td>true</td>\n      </tr><tr>\n        <td><b><a href="#recordingrulegroupsetspec">spec</a></b></td>\n        <td>object</td>\n        <td>\n          RecordingRuleGroupSetSpec defines the desired state of a set of Coralogix recording rule groups.\nSee also https://coralogix.com/docs/user-guides/data-transformation/metric-rules/recording-rules/<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#recordingrulegroupsetstatus">status</a></b></td>\n        <td>object</td>\n        <td>\n          RecordingRuleGroupSetStatus defines the observed state of RecordingRuleGroupSet<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### RecordingRuleGroupSet.spec\n<sup><sup>[↩ Parent](#recordingrulegroupset)</sup></sup>\n\n\n\nRecordingRuleGroupSetSpec defines the desired state of a set of Coralogix recording rule groups.\nSee also https://coralogix.com/docs/user-guides/data-transformation/metric-rules/recording-rules/\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#recordingrulegroupsetspecgroupsindex">groups</a></b></td>\n        <td>[]object</td>\n        <td>\n          Recording rule groups.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### RecordingRuleGroupSet.spec.groups[index]\n<sup><sup>[↩ Parent](#recordingrulegroupsetspec)</sup></sup>\n\n\n\nA Coralogix recording rule group.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>intervalSeconds</b></td>\n        <td>integer</td>\n        <td>\n          How often rules in the group are evaluated (in seconds).<br/>\n          <br/>\n            <i>Format</i>: int32<br/>\n            <i>Default</i>: 60<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>limit</b></td>\n        <td>integer</td>\n        <td>\n          Limits the number of alerts an alerting rule and series a recording-rule can produce. 0 is no limit.<br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          The (unique) rule group name.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#recordingrulegroupsetspecgroupsindexrulesindex">rules</a></b></td>\n        <td>[]object</td>\n        <td>\n          Rules of this group.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### RecordingRuleGroupSet.spec.groups[index].rules[index]\n<sup><sup>[↩ Parent](#recordingrulegroupsetspecgroupsindex)</sup></sup>\n\n\n\nA recording rule.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>expr</b></td>\n        <td>string</td>\n        <td>\n          The PromQL expression to evaluate.\nEvery evaluation cycle this is evaluated at the current time, and the result recorded as a new set of time series with the metric name as given by 'record'.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>labels</b></td>\n        <td>map[string]string</td>\n        <td>\n          Labels to add or overwrite before storing the result.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>record</b></td>\n        <td>string</td>\n        <td>\n          The name of the time series to output to. Must be a valid metric name.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### RecordingRuleGroupSet.status\n<sup><sup>[↩ Parent](#recordingrulegroupset)</sup></sup>\n\n\n\nRecordingRuleGroupSetStatus defines the observed state of RecordingRuleGroupSet\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#recordingrulegroupsetstatusconditionsindex">conditions</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### RecordingRuleGroupSet.status.conditions[index]\n<sup><sup>[↩ Parent](#recordingrulegroupsetstatus)</sup></sup>\n\n\n\nCondition contains details for one aspect of the current state of this API Resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>lastTransitionTime</b></td>\n        <td>string</td>\n        <td>\n          lastTransitionTime is the last time the condition transitioned from one status to another.\nThis should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.<br/>\n          <br/>\n            <i>Format</i>: date-time<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>message</b></td>\n        <td>string</td>\n        <td>\n          message is a human readable message indicating details about the transition.\nThis may be an empty string.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>reason</b></td>\n        <td>string</td>\n        <td>\n          reason contains a programmatic identifier indicating the reason for the condition's last transition.\nProducers of specific condition types may define expected values and meanings for this field,\nand whether the values are considered a guaranteed API.\nThe value should be a CamelCase string.\nThis field may not be empty.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>status</b></td>\n        <td>enum</td>\n        <td>\n          status of the condition, one of True, False, Unknown.<br/>\n          <br/>\n            <i>Enum</i>: True, False, Unknown<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>type</b></td>\n        <td>string</td>\n        <td>\n          type of condition in CamelCase or in foo.example.com/CamelCase.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>observedGeneration</b></td>\n        <td>integer</td>\n        <td>\n          observedGeneration represents the .metadata.generation that the condition was set based upon.\nFor instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date\nwith respect to the current state of the instance.<br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n            <i>Minimum</i>: 0<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n## RuleGroup\n<sup><sup>[↩ Parent](#coralogixcomv1alpha1 )</sup></sup>\n\n\n\n\n\n\nRuleGroup is the Schema for the rulegroups API\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n      <td><b>apiVersion</b></td>\n      <td>string</td>\n      <td>coralogix.com/v1alpha1</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b>kind</b></td>\n      <td>string</td>\n      <td>RuleGroup</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b><a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.27/#objectmeta-v1-meta">metadata</a></b></td>\n      <td>object</td>\n      <td>Refer to the Kubernetes API documentation for the fields of the `metadata` field.</td>\n      <td>true</td>\n      </tr><tr>\n        <td><b><a href="#rulegroupspec">spec</a></b></td>\n        <td>object</td>\n        <td>\n          RuleGroupSpec defines the Desired state of RuleGroup<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#rulegroupstatus">status</a></b></td>\n        <td>object</td>\n        <td>\n          RuleGroupStatus defines the observed state of RuleGroup<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### RuleGroup.spec\n<sup><sup>[↩ Parent](#rulegroup)</sup></sup>\n\n\n\nRuleGroupSpec defines the Desired state of RuleGroup\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the rule-group.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>active</b></td>\n        <td>boolean</td>\n        <td>\n          Whether the rule-group is active.<br/>\n          <br/>\n            <i>Default</i>: true<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>applications</b></td>\n        <td>[]string</td>\n        <td>\n          Rules will execute on logs that match the these applications.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>creator</b></td>\n        <td>string</td>\n        <td>\n          Rule-group creator<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>description</b></td>\n        <td>string</td>\n        <td>\n          Description of the rule-group.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>hidden</b></td>\n        <td>boolean</td>\n        <td>\n          Hides the rule-group.<br/>\n          <br/>\n            <i>Default</i>: false<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>order</b></td>\n        <td>integer</td>\n        <td>\n          The index of the rule-group between the other rule-groups.<br/>\n          <br/>\n            <i>Format</i>: int32<br/>\n            <i>Minimum</i>: 1<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>severities</b></td>\n        <td>[]enum</td>\n        <td>\n          Rules will execute on logs that match the these severities.<br/>\n          <br/>\n            <i>Enum</i>: Debug, Verbose, Info, Warning, Error, Critical<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#rulegroupspecsubgroupsindex">subgroups</a></b></td>\n        <td>[]object</td>\n        <td>\n          Rules within the same subgroup have an OR relationship,\nwhile rules in different subgroups have an AND relationship.\nRefer to https://github.com/coralogix/coralogix-operator/blob/main/config/samples/v1alpha1/rulegroups/mixed_rulegroup.yaml\nfor an example.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>subsystems</b></td>\n        <td>[]string</td>\n        <td>\n          Rules will execute on logs that match the these subsystems.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### RuleGroup.spec.subgroups[index]\n<sup><sup>[↩ Parent](#rulegroupspec)</sup></sup>\n\n\n\nSub group of rules.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>active</b></td>\n        <td>boolean</td>\n        <td>\n          Determines whether to rule will be active or not.<br/>\n          <br/>\n            <i>Default</i>: true<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          The rule id.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>order</b></td>\n        <td>integer</td>\n        <td>\n          Determines the index of the rule inside the rule-subgroup.<br/>\n          <br/>\n            <i>Format</i>: int32<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#rulegroupspecsubgroupsindexrulesindex">rules</a></b></td>\n        <td>[]object</td>\n        <td>\n          List of rules associated with the sub group.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### RuleGroup.spec.subgroups[index].rules[index]\n<sup><sup>[↩ Parent](#rulegroupspecsubgroupsindex)</sup></sup>\n\n\n\nA rule to change data extraction.\nSee also https://coralogix.com/docs/user-guides/data-transformation/metric-rules/recording-rules/\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the rule.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>active</b></td>\n        <td>boolean</td>\n        <td>\n          Whether the rule will be activated.<br/>\n          <br/>\n            <i>Default</i>: true<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#rulegroupspecsubgroupsindexrulesindexblock">block</a></b></td>\n        <td>object</td>\n        <td>\n          Block rules allow for refined filtering of incoming logs with a Regular Expression.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>description</b></td>\n        <td>string</td>\n        <td>\n          Description of the rule.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#rulegroupspecsubgroupsindexrulesindexextract">extract</a></b></td>\n        <td>object</td>\n        <td>\n          Use a named Regular Expression group to extract specific values you need as JSON getKeysStrings without having to parse the entire log.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#rulegroupspecsubgroupsindexrulesindexextracttimestamp">extractTimestamp</a></b></td>\n        <td>object</td>\n        <td>\n          Replace rules are used to replace logs timestamp with JSON field.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#rulegroupspecsubgroupsindexrulesindexjsonextract">jsonExtract</a></b></td>\n        <td>object</td>\n        <td>\n          Name a JSON field to extract its value directly into a Coralogix metadata field<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#rulegroupspecsubgroupsindexrulesindexjsonstringify">jsonStringify</a></b></td>\n        <td>object</td>\n        <td>\n          Convert JSON object to JSON string.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#rulegroupspecsubgroupsindexrulesindexparse">parse</a></b></td>\n        <td>object</td>\n        <td>\n          Parse unstructured logs into JSON format using named Regular Expression groups.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#rulegroupspecsubgroupsindexrulesindexparsejsonfield">parseJsonField</a></b></td>\n        <td>object</td>\n        <td>\n          Convert JSON string to JSON object.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#rulegroupspecsubgroupsindexrulesindexremovefields">removeFields</a></b></td>\n        <td>object</td>\n        <td>\n          Remove Fields allows to select fields that will not be indexed.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#rulegroupspecsubgroupsindexrulesindexreplace">replace</a></b></td>\n        <td>object</td>\n        <td>\n          Replace rules are used to strings in order to fix log structure, change log severity, or obscure information.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### RuleGroup.spec.subgroups[index].rules[index].block\n<sup><sup>[↩ Parent](#rulegroupspecsubgroupsindexrulesindex)</sup></sup>\n\n\n\nBlock rules allow for refined filtering of incoming logs with a Regular Expression.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>regex</b></td>\n        <td>string</td>\n        <td>\n          Regular Expression. More info: https://coralogix.com/blog/regex-101/<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>sourceField</b></td>\n        <td>string</td>\n        <td>\n          The field on which the Regular Expression will operate on.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>blockingAllMatchingBlocks</b></td>\n        <td>boolean</td>\n        <td>\n          Block Logic. If true or nor set - blocking all matching blocks, if false - blocking all non-matching blocks.<br/>\n          <br/>\n            <i>Default</i>: true<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>keepBlockedLogs</b></td>\n        <td>boolean</td>\n        <td>\n          Determines if to view blocked logs in LiveTail and archive to S3.<br/>\n          <br/>\n            <i>Default</i>: false<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### RuleGroup.spec.subgroups[index].rules[index].extract\n<sup><sup>[↩ Parent](#rulegroupspecsubgroupsindexrulesindex)</sup></sup>\n\n\n\nUse a named Regular Expression group to extract specific values you need as JSON getKeysStrings without having to parse the entire log.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>regex</b></td>\n        <td>string</td>\n        <td>\n          Regular Expression. More info: https://coralogix.com/blog/regex-101/<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>sourceField</b></td>\n        <td>string</td>\n        <td>\n          The field on which the Regular Expression will operate on.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### RuleGroup.spec.subgroups[index].rules[index].extractTimestamp\n<sup><sup>[↩ Parent](#rulegroupspecsubgroupsindexrulesindex)</sup></sup>\n\n\n\nReplace rules are used to replace logs timestamp with JSON field.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>fieldFormatStandard</b></td>\n        <td>enum</td>\n        <td>\n          The format standard to parse the timestamp.<br/>\n          <br/>\n            <i>Enum</i>: Strftime, JavaSDF, Golang, SecondTS, MilliTS, MicroTS, NanoTS<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>sourceField</b></td>\n        <td>string</td>\n        <td>\n          The field on which the Regular Expression will operate on.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>timeFormat</b></td>\n        <td>string</td>\n        <td>\n          A time formatting string that matches the field format standard.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### RuleGroup.spec.subgroups[index].rules[index].jsonExtract\n<sup><sup>[↩ Parent](#rulegroupspecsubgroupsindexrulesindex)</sup></sup>\n\n\n\nName a JSON field to extract its value directly into a Coralogix metadata field\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>destinationField</b></td>\n        <td>enum</td>\n        <td>\n          The field that will be populated by the results of the Regular Expression operation.<br/>\n          <br/>\n            <i>Enum</i>: Category, CLASSNAME, METHODNAME, THREADID, SEVERITY<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>jsonKey</b></td>\n        <td>string</td>\n        <td>\n          JSON key to extract its value directly into a Coralogix metadata field.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### RuleGroup.spec.subgroups[index].rules[index].jsonStringify\n<sup><sup>[↩ Parent](#rulegroupspecsubgroupsindexrulesindex)</sup></sup>\n\n\n\nConvert JSON object to JSON string.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>destinationField</b></td>\n        <td>string</td>\n        <td>\n          The field that will be populated by the results of the Regular Expression<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>sourceField</b></td>\n        <td>string</td>\n        <td>\n          The field on which the Regular Expression will operate on.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>keepSourceField</b></td>\n        <td>boolean</td>\n        <td>\n          <br/>\n          <br/>\n            <i>Default</i>: false<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### RuleGroup.spec.subgroups[index].rules[index].parse\n<sup><sup>[↩ Parent](#rulegroupspecsubgroupsindexrulesindex)</sup></sup>\n\n\n\nParse unstructured logs into JSON format using named Regular Expression groups.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>destinationField</b></td>\n        <td>string</td>\n        <td>\n          The field that will be populated by the results of the Regular Expression operation.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>regex</b></td>\n        <td>string</td>\n        <td>\n          Regular Expression. More info: https://coralogix.com/blog/regex-101/<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>sourceField</b></td>\n        <td>string</td>\n        <td>\n          The field on which the Regular Expression will operate on.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### RuleGroup.spec.subgroups[index].rules[index].parseJsonField\n<sup><sup>[↩ Parent](#rulegroupspecsubgroupsindexrulesindex)</sup></sup>\n\n\n\nConvert JSON string to JSON object.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>destinationField</b></td>\n        <td>string</td>\n        <td>\n          The field that will be populated by the results of the Regular Expression<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>keepDestinationField</b></td>\n        <td>boolean</td>\n        <td>\n          Determines whether to keep or to delete the destination field.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>keepSourceField</b></td>\n        <td>boolean</td>\n        <td>\n          Determines whether to keep or to delete the source field.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>sourceField</b></td>\n        <td>string</td>\n        <td>\n          The field on which the Regular Expression will operate on.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### RuleGroup.spec.subgroups[index].rules[index].removeFields\n<sup><sup>[↩ Parent](#rulegroupspecsubgroupsindexrulesindex)</sup></sup>\n\n\n\nRemove Fields allows to select fields that will not be indexed.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>excludedFields</b></td>\n        <td>[]string</td>\n        <td>\n          Excluded fields won't be indexed.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### RuleGroup.spec.subgroups[index].rules[index].replace\n<sup><sup>[↩ Parent](#rulegroupspecsubgroupsindexrulesindex)</sup></sup>\n\n\n\nReplace rules are used to strings in order to fix log structure, change log severity, or obscure information.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>destinationField</b></td>\n        <td>string</td>\n        <td>\n          The field that will be populated by the results of the Regular Expression operation.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>regex</b></td>\n        <td>string</td>\n        <td>\n          Regular Expression. More info: https://coralogix.com/blog/regex-101/<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>replacementString</b></td>\n        <td>string</td>\n        <td>\n          The string that will replace the matched Regular Expression<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>sourceField</b></td>\n        <td>string</td>\n        <td>\n          The field on which the Regular Expression will operate on.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### RuleGroup.status\n<sup><sup>[↩ Parent](#rulegroup)</sup></sup>\n\n\n\nRuleGroupStatus defines the observed state of RuleGroup\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#rulegroupstatusconditionsindex">conditions</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### RuleGroup.status.conditions[index]\n<sup><sup>[↩ Parent](#rulegroupstatus)</sup></sup>\n\n\n\nCondition contains details for one aspect of the current state of this API Resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>lastTransitionTime</b></td>\n        <td>string</td>\n        <td>\n          lastTransitionTime is the last time the condition transitioned from one status to another.\nThis should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.<br/>\n          <br/>\n            <i>Format</i>: date-time<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>message</b></td>\n        <td>string</td>\n        <td>\n          message is a human readable message indicating details about the transition.\nThis may be an empty string.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>reason</b></td>\n        <td>string</td>\n        <td>\n          reason contains a programmatic identifier indicating the reason for the condition's last transition.\nProducers of specific condition types may define expected values and meanings for this field,\nand whether the values are considered a guaranteed API.\nThe value should be a CamelCase string.\nThis field may not be empty.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>status</b></td>\n        <td>enum</td>\n        <td>\n          status of the condition, one of True, False, Unknown.<br/>\n          <br/>\n            <i>Enum</i>: True, False, Unknown<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>type</b></td>\n        <td>string</td>\n        <td>\n          type of condition in CamelCase or in foo.example.com/CamelCase.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>observedGeneration</b></td>\n        <td>integer</td>\n        <td>\n          observedGeneration represents the .metadata.generation that the condition was set based upon.\nFor instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date\nwith respect to the current state of the instance.<br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n            <i>Minimum</i>: 0<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n## Scope\n<sup><sup>[↩ Parent](#coralogixcomv1alpha1 )</sup></sup>\n\n\n\n\n\n\nScope is the Schema for the scopes API.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n      <td><b>apiVersion</b></td>\n      <td>string</td>\n      <td>coralogix.com/v1alpha1</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b>kind</b></td>\n      <td>string</td>\n      <td>Scope</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b><a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.27/#objectmeta-v1-meta">metadata</a></b></td>\n      <td>object</td>\n      <td>Refer to the Kubernetes API documentation for the fields of the `metadata` field.</td>\n      <td>true</td>\n      </tr><tr>\n        <td><b><a href="#scopespec">spec</a></b></td>\n        <td>object</td>\n        <td>\n          ScopeSpec defines the desired state of a Coralogix Scope.\nSee also https://coralogix.com/docs/user-guides/account-management/user-management/scopes/<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#scopestatus">status</a></b></td>\n        <td>object</td>\n        <td>\n          ScopeStatus defines the observed state of Coralogix Scope.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Scope.spec\n<sup><sup>[↩ Parent](#scope)</sup></sup>\n\n\n\nScopeSpec defines the desired state of a Coralogix Scope.\nSee also https://coralogix.com/docs/user-guides/account-management/user-management/scopes/\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>defaultExpression</b></td>\n        <td>enum</td>\n        <td>\n          Default expression to use when no filter matches the query. Until further notice, this is limited to `true` (everything is included) or `false` (nothing is included). Use a version tag (e.g `<v1>true` or `<v1>false`)<br/>\n          <br/>\n            <i>Enum</i>: <v1>true, <v1>false<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#scopespecfiltersindex">filters</a></b></td>\n        <td>[]object</td>\n        <td>\n          Filters applied to include data in the scope.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Scope display name.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>description</b></td>\n        <td>string</td>\n        <td>\n          Description of the scope. Optional.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Scope.spec.filters[index]\n<sup><sup>[↩ Parent](#scopespec)</sup></sup>\n\n\n\nScopeFilter defines a filter to include data in a scope.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>entityType</b></td>\n        <td>enum</td>\n        <td>\n          Entity type to apply the expression on.<br/>\n          <br/>\n            <i>Enum</i>: logs, spans, unspecified<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>expression</b></td>\n        <td>string</td>\n        <td>\n          Expression to run.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### Scope.status\n<sup><sup>[↩ Parent](#scope)</sup></sup>\n\n\n\nScopeStatus defines the observed state of Coralogix Scope.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#scopestatusconditionsindex">conditions</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### Scope.status.conditions[index]\n<sup><sup>[↩ Parent](#scopestatus)</sup></sup>\n\n\n\nCondition contains details for one aspect of the current state of this API Resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>lastTransitionTime</b></td>\n        <td>string</td>\n        <td>\n          lastTransitionTime is the last time the condition transitioned from one status to another.\nThis should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.<br/>\n          <br/>\n            <i>Format</i>: date-time<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>message</b></td>\n        <td>string</td>\n        <td>\n          message is a human readable message indicating details about the transition.\nThis may be an empty string.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>reason</b></td>\n        <td>string</td>\n        <td>\n          reason contains a programmatic identifier indicating the reason for the condition's last transition.\nProducers of specific condition types may define expected values and meanings for this field,\nand whether the values are considered a guaranteed API.\nThe value should be a CamelCase string.\nThis field may not be empty.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>status</b></td>\n        <td>enum</td>\n        <td>\n          status of the condition, one of True, False, Unknown.<br/>\n          <br/>\n            <i>Enum</i>: True, False, Unknown<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>type</b></td>\n        <td>string</td>\n        <td>\n          type of condition in CamelCase or in foo.example.com/CamelCase.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>observedGeneration</b></td>\n        <td>integer</td>\n        <td>\n          observedGeneration represents the .metadata.generation that the condition was set based upon.\nFor instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date\nwith respect to the current state of the instance.<br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n            <i>Minimum</i>: 0<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n## TCOLogsPolicies\n<sup><sup>[↩ Parent](#coralogixcomv1alpha1 )</sup></sup>\n\n\n\n\n\n\nTCOLogsPolicies is the Schema for the tcologspolicies API.\nNOTE: This resource performs an atomic overwrite of all existing TCO logs policies\nin the backend. Any existing policies not defined in this resource will be\nremoved. Use with caution as this operation is destructive.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n      <td><b>apiVersion</b></td>\n      <td>string</td>\n      <td>coralogix.com/v1alpha1</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b>kind</b></td>\n      <td>string</td>\n      <td>TCOLogsPolicies</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b><a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.27/#objectmeta-v1-meta">metadata</a></b></td>\n      <td>object</td>\n      <td>Refer to the Kubernetes API documentation for the fields of the `metadata` field.</td>\n      <td>true</td>\n      </tr><tr>\n        <td><b><a href="#tcologspoliciesspec">spec</a></b></td>\n        <td>object</td>\n        <td>\n          TCOLogsPoliciesSpec defines the desired state of Coralogix TCO logs policies.\nSee also https://coralogix.com/docs/tco-optimizer-api<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#tcologspoliciesstatus">status</a></b></td>\n        <td>object</td>\n        <td>\n          TCOLogsPoliciesStatus defines the observed state of TCOLogsPolicies.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### TCOLogsPolicies.spec\n<sup><sup>[↩ Parent](#tcologspolicies)</sup></sup>\n\n\n\nTCOLogsPoliciesSpec defines the desired state of Coralogix TCO logs policies.\nSee also https://coralogix.com/docs/tco-optimizer-api\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#tcologspoliciesspecpoliciesindex">policies</a></b></td>\n        <td>[]object</td>\n        <td>\n          Coralogix TCO-Policies-List.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### TCOLogsPolicies.spec.policies[index]\n<sup><sup>[↩ Parent](#tcologspoliciesspec)</sup></sup>\n\n\n\nA TCO policy for logs.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the policy.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>priority</b></td>\n        <td>enum</td>\n        <td>\n          The policy priority.<br/>\n          <br/>\n            <i>Enum</i>: block, high, medium, low<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>severities</b></td>\n        <td>[]enum</td>\n        <td>\n          The severities to apply the policy on.<br/>\n          <br/>\n            <i>Enum</i>: info, warning, critical, error, debug, verbose<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#tcologspoliciesspecpoliciesindexapplications">applications</a></b></td>\n        <td>object</td>\n        <td>\n          The applications to apply the policy on. Applies the policy on all the applications by default.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#tcologspoliciesspecpoliciesindexarchiveretention">archiveRetention</a></b></td>\n        <td>object</td>\n        <td>\n          Matches the specified retention.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>description</b></td>\n        <td>string</td>\n        <td>\n          Description of the policy.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#tcologspoliciesspecpoliciesindexsubsystems">subsystems</a></b></td>\n        <td>object</td>\n        <td>\n          The subsystems to apply the policy on. Applies the policy on all the subsystems by default.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### TCOLogsPolicies.spec.policies[index].applications\n<sup><sup>[↩ Parent](#tcologspoliciesspecpoliciesindex)</sup></sup>\n\n\n\nThe applications to apply the policy on. Applies the policy on all the applications by default.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>names</b></td>\n        <td>[]string</td>\n        <td>\n          Names to match.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>ruleType</b></td>\n        <td>enum</td>\n        <td>\n          Type of matching for the name.<br/>\n          <br/>\n            <i>Enum</i>: is, is_not, start_with, includes<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### TCOLogsPolicies.spec.policies[index].archiveRetention\n<sup><sup>[↩ Parent](#tcologspoliciesspecpoliciesindex)</sup></sup>\n\n\n\nMatches the specified retention.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#tcologspoliciesspecpoliciesindexarchiveretentionbackendref">backendRef</a></b></td>\n        <td>object</td>\n        <td>\n          Reference to the retention policy<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### TCOLogsPolicies.spec.policies[index].archiveRetention.backendRef\n<sup><sup>[↩ Parent](#tcologspoliciesspecpoliciesindexarchiveretention)</sup></sup>\n\n\n\nReference to the retention policy\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the policy.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### TCOLogsPolicies.spec.policies[index].subsystems\n<sup><sup>[↩ Parent](#tcologspoliciesspecpoliciesindex)</sup></sup>\n\n\n\nThe subsystems to apply the policy on. Applies the policy on all the subsystems by default.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>names</b></td>\n        <td>[]string</td>\n        <td>\n          Names to match.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>ruleType</b></td>\n        <td>enum</td>\n        <td>\n          Type of matching for the name.<br/>\n          <br/>\n            <i>Enum</i>: is, is_not, start_with, includes<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### TCOLogsPolicies.status\n<sup><sup>[↩ Parent](#tcologspolicies)</sup></sup>\n\n\n\nTCOLogsPoliciesStatus defines the observed state of TCOLogsPolicies.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#tcologspoliciesstatusconditionsindex">conditions</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### TCOLogsPolicies.status.conditions[index]\n<sup><sup>[↩ Parent](#tcologspoliciesstatus)</sup></sup>\n\n\n\nCondition contains details for one aspect of the current state of this API Resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>lastTransitionTime</b></td>\n        <td>string</td>\n        <td>\n          lastTransitionTime is the last time the condition transitioned from one status to another.\nThis should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.<br/>\n          <br/>\n            <i>Format</i>: date-time<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>message</b></td>\n        <td>string</td>\n        <td>\n          message is a human readable message indicating details about the transition.\nThis may be an empty string.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>reason</b></td>\n        <td>string</td>\n        <td>\n          reason contains a programmatic identifier indicating the reason for the condition's last transition.\nProducers of specific condition types may define expected values and meanings for this field,\nand whether the values are considered a guaranteed API.\nThe value should be a CamelCase string.\nThis field may not be empty.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>status</b></td>\n        <td>enum</td>\n        <td>\n          status of the condition, one of True, False, Unknown.<br/>\n          <br/>\n            <i>Enum</i>: True, False, Unknown<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>type</b></td>\n        <td>string</td>\n        <td>\n          type of condition in CamelCase or in foo.example.com/CamelCase.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>observedGeneration</b></td>\n        <td>integer</td>\n        <td>\n          observedGeneration represents the .metadata.generation that the condition was set based upon.\nFor instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date\nwith respect to the current state of the instance.<br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n            <i>Minimum</i>: 0<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n## TCOTracesPolicies\n<sup><sup>[↩ Parent](#coralogixcomv1alpha1 )</sup></sup>\n\n\n\n\n\n\nTCOTracesPolicies is the Schema for the tcotracespolicies API.\nNOTE: This resource performs an atomic overwrite of all existing TCO traces policies\nin the backend. Any existing policies not defined in this resource will be\nremoved. Use with caution as this operation is destructive.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n      <td><b>apiVersion</b></td>\n      <td>string</td>\n      <td>coralogix.com/v1alpha1</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b>kind</b></td>\n      <td>string</td>\n      <td>TCOTracesPolicies</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b><a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.27/#objectmeta-v1-meta">metadata</a></b></td>\n      <td>object</td>\n      <td>Refer to the Kubernetes API documentation for the fields of the `metadata` field.</td>\n      <td>true</td>\n      </tr><tr>\n        <td><b><a href="#tcotracespoliciesspec">spec</a></b></td>\n        <td>object</td>\n        <td>\n          TCOTracesPoliciesSpec defines the desired state of Coralogix TCO policies for traces.\nSee also https://coralogix.com/docs/tco-optimizer-api<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#tcotracespoliciesstatus">status</a></b></td>\n        <td>object</td>\n        <td>\n          TCOTracesPoliciesStatus defines the observed state of TCOTracesPolicies.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### TCOTracesPolicies.spec\n<sup><sup>[↩ Parent](#tcotracespolicies)</sup></sup>\n\n\n\nTCOTracesPoliciesSpec defines the desired state of Coralogix TCO policies for traces.\nSee also https://coralogix.com/docs/tco-optimizer-api\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#tcotracespoliciesspecpoliciesindex">policies</a></b></td>\n        <td>[]object</td>\n        <td>\n          Coralogix TCO-Policies-List.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### TCOTracesPolicies.spec.policies[index]\n<sup><sup>[↩ Parent](#tcotracespoliciesspec)</sup></sup>\n\n\n\nCoralogix TCO policy for traces.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the policy.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>priority</b></td>\n        <td>enum</td>\n        <td>\n          The policy priority.<br/>\n          <br/>\n            <i>Enum</i>: block, high, medium, low<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#tcotracespoliciesspecpoliciesindexactions">actions</a></b></td>\n        <td>object</td>\n        <td>\n          The actions to apply the policy on. Applies the policy on all the actions by default.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#tcotracespoliciesspecpoliciesindexapplications">applications</a></b></td>\n        <td>object</td>\n        <td>\n          The applications to apply the policy on. Applies the policy on all the applications by default.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#tcotracespoliciesspecpoliciesindexarchiveretention">archiveRetention</a></b></td>\n        <td>object</td>\n        <td>\n          Matches the specified retention.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>description</b></td>\n        <td>string</td>\n        <td>\n          Description of the policy.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#tcotracespoliciesspecpoliciesindexservices">services</a></b></td>\n        <td>object</td>\n        <td>\n          The services to apply the policy on. Applies the policy on all the services by default.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#tcotracespoliciesspecpoliciesindexsubsystems">subsystems</a></b></td>\n        <td>object</td>\n        <td>\n          The subsystems to apply the policy on. Applies the policy on all the subsystems by default.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#tcotracespoliciesspecpoliciesindextagsindex">tags</a></b></td>\n        <td>[]object</td>\n        <td>\n          The tags to apply the policy on. Applies the policy on all the tags by default.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### TCOTracesPolicies.spec.policies[index].actions\n<sup><sup>[↩ Parent](#tcotracespoliciesspecpoliciesindex)</sup></sup>\n\n\n\nThe actions to apply the policy on. Applies the policy on all the actions by default.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>names</b></td>\n        <td>[]string</td>\n        <td>\n          Names to match.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>ruleType</b></td>\n        <td>enum</td>\n        <td>\n          Type of matching for the name.<br/>\n          <br/>\n            <i>Enum</i>: is, is_not, start_with, includes<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### TCOTracesPolicies.spec.policies[index].applications\n<sup><sup>[↩ Parent](#tcotracespoliciesspecpoliciesindex)</sup></sup>\n\n\n\nThe applications to apply the policy on. Applies the policy on all the applications by default.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>names</b></td>\n        <td>[]string</td>\n        <td>\n          Names to match.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>ruleType</b></td>\n        <td>enum</td>\n        <td>\n          Type of matching for the name.<br/>\n          <br/>\n            <i>Enum</i>: is, is_not, start_with, includes<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### TCOTracesPolicies.spec.policies[index].archiveRetention\n<sup><sup>[↩ Parent](#tcotracespoliciesspecpoliciesindex)</sup></sup>\n\n\n\nMatches the specified retention.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#tcotracespoliciesspecpoliciesindexarchiveretentionbackendref">backendRef</a></b></td>\n        <td>object</td>\n        <td>\n          Reference to the retention policy<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### TCOTracesPolicies.spec.policies[index].archiveRetention.backendRef\n<sup><sup>[↩ Parent](#tcotracespoliciesspecpoliciesindexarchiveretention)</sup></sup>\n\n\n\nReference to the retention policy\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the policy.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### TCOTracesPolicies.spec.policies[index].services\n<sup><sup>[↩ Parent](#tcotracespoliciesspecpoliciesindex)</sup></sup>\n\n\n\nThe services to apply the policy on. Applies the policy on all the services by default.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>names</b></td>\n        <td>[]string</td>\n        <td>\n          Names to match.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>ruleType</b></td>\n        <td>enum</td>\n        <td>\n          Type of matching for the name.<br/>\n          <br/>\n            <i>Enum</i>: is, is_not, start_with, includes<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### TCOTracesPolicies.spec.policies[index].subsystems\n<sup><sup>[↩ Parent](#tcotracespoliciesspecpoliciesindex)</sup></sup>\n\n\n\nThe subsystems to apply the policy on. Applies the policy on all the subsystems by default.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>names</b></td>\n        <td>[]string</td>\n        <td>\n          Names to match.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>ruleType</b></td>\n        <td>enum</td>\n        <td>\n          Type of matching for the name.<br/>\n          <br/>\n            <i>Enum</i>: is, is_not, start_with, includes<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### TCOTracesPolicies.spec.policies[index].tags[index]\n<sup><sup>[↩ Parent](#tcotracespoliciesspecpoliciesindex)</sup></sup>\n\n\n\nTCO Policy tag matching rule.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Tag names to match.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>ruleType</b></td>\n        <td>enum</td>\n        <td>\n          Operator to match with.<br/>\n          <br/>\n            <i>Enum</i>: is, is_not, start_with, includes<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>values</b></td>\n        <td>[]string</td>\n        <td>\n          Values to match for<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### TCOTracesPolicies.status\n<sup><sup>[↩ Parent](#tcotracespolicies)</sup></sup>\n\n\n\nTCOTracesPoliciesStatus defines the observed state of TCOTracesPolicies.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#tcotracespoliciesstatusconditionsindex">conditions</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### TCOTracesPolicies.status.conditions[index]\n<sup><sup>[↩ Parent](#tcotracespoliciesstatus)</sup></sup>\n\n\n\nCondition contains details for one aspect of the current state of this API Resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>lastTransitionTime</b></td>\n        <td>string</td>\n        <td>\n          lastTransitionTime is the last time the condition transitioned from one status to another.\nThis should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.<br/>\n          <br/>\n            <i>Format</i>: date-time<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>message</b></td>\n        <td>string</td>\n        <td>\n          message is a human readable message indicating details about the transition.\nThis may be an empty string.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>reason</b></td>\n        <td>string</td>\n        <td>\n          reason contains a programmatic identifier indicating the reason for the condition's last transition.\nProducers of specific condition types may define expected values and meanings for this field,\nand whether the values are considered a guaranteed API.\nThe value should be a CamelCase string.\nThis field may not be empty.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>status</b></td>\n        <td>enum</td>\n        <td>\n          status of the condition, one of True, False, Unknown.<br/>\n          <br/>\n            <i>Enum</i>: True, False, Unknown<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>type</b></td>\n        <td>string</td>\n        <td>\n          type of condition in CamelCase or in foo.example.com/CamelCase.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>observedGeneration</b></td>\n        <td>integer</td>\n        <td>\n          observedGeneration represents the .metadata.generation that the condition was set based upon.\nFor instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date\nwith respect to the current state of the instance.<br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n            <i>Minimum</i>: 0<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n## ViewFolder\n<sup><sup>[↩ Parent](#coralogixcomv1alpha1 )</sup></sup>\n\n\n\n\n\n\nViewFolder is the Schema for the viewfolders API.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n      <td><b>apiVersion</b></td>\n      <td>string</td>\n      <td>coralogix.com/v1alpha1</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b>kind</b></td>\n      <td>string</td>\n      <td>ViewFolder</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b><a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.27/#objectmeta-v1-meta">metadata</a></b></td>\n      <td>object</td>\n      <td>Refer to the Kubernetes API documentation for the fields of the `metadata` field.</td>\n      <td>true</td>\n      </tr><tr>\n        <td><b><a href="#viewfolderspec">spec</a></b></td>\n        <td>object</td>\n        <td>\n          ViewFolderSpec defines the desired state of ViewFolder.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#viewfolderstatus">status</a></b></td>\n        <td>object</td>\n        <td>\n          ViewFolderStatus defines the observed state of ViewFolder.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### ViewFolder.spec\n<sup><sup>[↩ Parent](#viewfolder)</sup></sup>\n\n\n\nViewFolderSpec defines the desired state of ViewFolder.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the view folder<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### ViewFolder.status\n<sup><sup>[↩ Parent](#viewfolder)</sup></sup>\n\n\n\nViewFolderStatus defines the observed state of ViewFolder.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#viewfolderstatusconditionsindex">conditions</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### ViewFolder.status.conditions[index]\n<sup><sup>[↩ Parent](#viewfolderstatus)</sup></sup>\n\n\n\nCondition contains details for one aspect of the current state of this API Resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>lastTransitionTime</b></td>\n        <td>string</td>\n        <td>\n          lastTransitionTime is the last time the condition transitioned from one status to another.\nThis should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.<br/>\n          <br/>\n            <i>Format</i>: date-time<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>message</b></td>\n        <td>string</td>\n        <td>\n          message is a human readable message indicating details about the transition.\nThis may be an empty string.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>reason</b></td>\n        <td>string</td>\n        <td>\n          reason contains a programmatic identifier indicating the reason for the condition's last transition.\nProducers of specific condition types may define expected values and meanings for this field,\nand whether the values are considered a guaranteed API.\nThe value should be a CamelCase string.\nThis field may not be empty.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>status</b></td>\n        <td>enum</td>\n        <td>\n          status of the condition, one of True, False, Unknown.<br/>\n          <br/>\n            <i>Enum</i>: True, False, Unknown<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>type</b></td>\n        <td>string</td>\n        <td>\n          type of condition in CamelCase or in foo.example.com/CamelCase.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>observedGeneration</b></td>\n        <td>integer</td>\n        <td>\n          observedGeneration represents the .metadata.generation that the condition was set based upon.\nFor instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date\nwith respect to the current state of the instance.<br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n            <i>Minimum</i>: 0<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n## View\n<sup><sup>[↩ Parent](#coralogixcomv1alpha1 )</sup></sup>\n\n\n\n\n\n\nView is the Schema for the views API.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n      <td><b>apiVersion</b></td>\n      <td>string</td>\n      <td>coralogix.com/v1alpha1</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b>kind</b></td>\n      <td>string</td>\n      <td>View</td>\n      <td>true</td>\n      </tr>\n      <tr>\n      <td><b><a href="https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.27/#objectmeta-v1-meta">metadata</a></b></td>\n      <td>object</td>\n      <td>Refer to the Kubernetes API documentation for the fields of the `metadata` field.</td>\n      <td>true</td>\n      </tr><tr>\n        <td><b><a href="#viewspec">spec</a></b></td>\n        <td>object</td>\n        <td>\n          ViewSpec defines the desired state of View.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#viewstatus">status</a></b></td>\n        <td>object</td>\n        <td>\n          ViewStatus defines the observed state of View.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### View.spec\n<sup><sup>[↩ Parent](#view)</sup></sup>\n\n\n\nViewSpec defines the desired state of View.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#viewspecfilters">filters</a></b></td>\n        <td>object</td>\n        <td>\n          Filters is the filters for the view.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the view.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#viewspectimeselection">timeSelection</a></b></td>\n        <td>object</td>\n        <td>\n          TimeSelection is the time selection for the view. Exactly one of quickSelection or customSelection must be set.<br/>\n          <br/>\n            <i>Validations</i>:<li>has(self.quickSelection) != has(self.customSelection): Exactly one of quickSelection or customSelection must be set</li>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b><a href="#viewspecfolder">folder</a></b></td>\n        <td>object</td>\n        <td>\n          Folder is the folder to which the view belongs.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#viewspecsearchquery">searchQuery</a></b></td>\n        <td>object</td>\n        <td>\n          SearchQuery is the search query for the view.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### View.spec.filters\n<sup><sup>[↩ Parent](#viewspec)</sup></sup>\n\n\n\nFilters is the filters for the view.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#viewspecfiltersfiltersindex">filters</a></b></td>\n        <td>[]object</td>\n        <td>\n          Filters is the list of filters for the view.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### View.spec.filters.filters[index]\n<sup><sup>[↩ Parent](#viewspecfilters)</sup></sup>\n\n\n\n\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name is the name of the filter.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>selectedValues</b></td>\n        <td>map[string]boolean</td>\n        <td>\n          SelectedValues is the selected values for the filter.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### View.spec.timeSelection\n<sup><sup>[↩ Parent](#viewspec)</sup></sup>\n\n\n\nTimeSelection is the time selection for the view. Exactly one of quickSelection or customSelection must be set.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#viewspectimeselectioncustomselection">customSelection</a></b></td>\n        <td>object</td>\n        <td>\n          CustomSelection is the custom selection for the view.<br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b><a href="#viewspectimeselectionquickselection">quickSelection</a></b></td>\n        <td>object</td>\n        <td>\n          QuickSelection is the quick selection for the view.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### View.spec.timeSelection.customSelection\n<sup><sup>[↩ Parent](#viewspectimeselection)</sup></sup>\n\n\n\nCustomSelection is the custom selection for the view.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>fromTime</b></td>\n        <td>string</td>\n        <td>\n          FromTime is the start time for the custom selection.<br/>\n          <br/>\n            <i>Format</i>: date-time<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>toTime</b></td>\n        <td>string</td>\n        <td>\n          ToTime is the end time for the custom selection.<br/>\n          <br/>\n            <i>Format</i>: date-time<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### View.spec.timeSelection.quickSelection\n<sup><sup>[↩ Parent](#viewspectimeselection)</sup></sup>\n\n\n\nQuickSelection is the quick selection for the view.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>seconds</b></td>\n        <td>integer</td>\n        <td>\n          Seconds is the number of seconds for the quick selection.<br/>\n          <br/>\n            <i>Format</i>: int32<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### View.spec.folder\n<sup><sup>[↩ Parent](#viewspec)</sup></sup>\n\n\n\nFolder is the folder to which the view belongs.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#viewspecfolderresourceref">resourceRef</a></b></td>\n        <td>object</td>\n        <td>\n          ViewFolder custom resource name and namespace. If namespace is not set, the View namespace will be used.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### View.spec.folder.resourceRef\n<sup><sup>[↩ Parent](#viewspecfolder)</sup></sup>\n\n\n\nViewFolder custom resource name and namespace. If namespace is not set, the View namespace will be used.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>name</b></td>\n        <td>string</td>\n        <td>\n          Name of the resource (not id).<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>namespace</b></td>\n        <td>string</td>\n        <td>\n          Kubernetes namespace.<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### View.spec.searchQuery\n<sup><sup>[↩ Parent](#viewspec)</sup></sup>\n\n\n\nSearchQuery is the search query for the view.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>query</b></td>\n        <td>string</td>\n        <td>\n          Query is the search query.<br/>\n        </td>\n        <td>true</td>\n      </tr></tbody>\n</table>\n\n\n### View.status\n<sup><sup>[↩ Parent](#view)</sup></sup>\n\n\n\nViewStatus defines the observed state of View.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b><a href="#viewstatusconditionsindex">conditions</a></b></td>\n        <td>[]object</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr><tr>\n        <td><b>id</b></td>\n        <td>string</td>\n        <td>\n          <br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>\n\n\n### View.status.conditions[index]\n<sup><sup>[↩ Parent](#viewstatus)</sup></sup>\n\n\n\nCondition contains details for one aspect of the current state of this API Resource.\n\n<table>\n    <thead>\n        <tr>\n            <th>Name</th>\n            <th>Type</th>\n            <th>Description</th>\n            <th>Required</th>\n        </tr>\n    </thead>\n    <tbody><tr>\n        <td><b>lastTransitionTime</b></td>\n        <td>string</td>\n        <td>\n          lastTransitionTime is the last time the condition transitioned from one status to another.\nThis should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.<br/>\n          <br/>\n            <i>Format</i>: date-time<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>message</b></td>\n        <td>string</td>\n        <td>\n          message is a human readable message indicating details about the transition.\nThis may be an empty string.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>reason</b></td>\n        <td>string</td>\n        <td>\n          reason contains a programmatic identifier indicating the reason for the condition's last transition.\nProducers of specific condition types may define expected values and meanings for this field,\nand whether the values are considered a guaranteed API.\nThe value should be a CamelCase string.\nThis field may not be empty.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>status</b></td>\n        <td>enum</td>\n        <td>\n          status of the condition, one of True, False, Unknown.<br/>\n          <br/>\n            <i>Enum</i>: True, False, Unknown<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>type</b></td>\n        <td>string</td>\n        <td>\n          type of condition in CamelCase or in foo.example.com/CamelCase.<br/>\n        </td>\n        <td>true</td>\n      </tr><tr>\n        <td><b>observedGeneration</b></td>\n        <td>integer</td>\n        <td>\n          observedGeneration represents the .metadata.generation that the condition was set based upon.\nFor instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date\nwith respect to the current state of the instance.<br/>\n          <br/>\n            <i>Format</i>: int64<br/>\n            <i>Minimum</i>: 0<br/>\n        </td>\n        <td>false</td>\n      </tr></tbody>\n</table>