# Coralogix Operator Metrics\n\n| Name | Type | Description | Labels |\n|------|------|-------------|---------|\n| cx_operator_info | Gauge | Coralogix Operator information | go_version, operator_version, coralogix_url |\n| cx_operator_resource_info | Gauge | Coralogix Operator custom resource information | kind, name, namespace, status |\n\n## Sending Metrics to Coralogix\nThe Coralogix Operator's metrics can be sent to Coralogix using Prometheus.\nThis requires configuring Prometheus to both scrape the operator's metrics and send them to Coralogix.\n\n### Scraping the Metrics\nBy default, the Coralogix Operator is deployed with:\n\n- A [ServiceMonitor](../charts/coralogix-operator/templates/service_monitor.yaml) that instructs Prometheus to scrape the operator's metrics endpoint.\n- A [ClusterRole](../charts/coralogix-operator/templates/metrics_reader_role.yaml) that grants Prometheus access to the metrics.\n    \nTo ensure proper scraping:\n\n- Verify that Prometheus is configured to select the provided ServiceMonitor.\n- Ensure that the ClusterRole is bound to Prometheus's ServiceAccount so it has the necessary permissions.\n\n### Sending the Metrics\nTo forward the collected metrics to Coralogix, follow [this guide](https://coralogix.com/docs/integrations/prometheus/prometheus-server/) to configure Prometheus accordingly.