site_name: Coralogix Docs
site_description: Welcome to Coralogix Documentation. Get the help you need — find product docs, guides, developer tools and other learning resources or submit a ticket for any urgent requests.
repo_url: https://github.com/coralogix/documentation
site_url: !ENV SITE_URL
repo_name: coralogix/documentation
nav:
  - Home:
    - index.md
  - User Guides:
    - user-guides/index.md
    - Getting Started:
      - user-guides/getting-started/get-started-with-coralogix/index.md
      - user-guides/getting-started/coralogix-features-tour/index.md
      - user-guides/whats-new/whats-new-in-coralogix/index.md
      - Packages and Extensions:
        - user-guides/getting-started/packages-and-extensions/integration-packages/index.md
        - user-guides/getting-started/packages-and-extensions/extension-packages/index.md
    - Data Flow:
      - user-guides/data-flow/forwarders/index.md
      - S3 Archive:
        - user-guides/data-flow/s3-archive/connect-s3-archive/index.md
        - user-guides/data-flow/s3-archive/archive-retention-policy/index.md
      - user-guides/data-flow/reserved-fields/index.md
    - Data Transformation:
      - Enrichments:
        - user-guides/data-transformation/enrichments/getting-started/index.md
        - user-guides/data-transformation/enrichments/aws-resource-enrichment/index.md
        - user-guides/data-transformation/enrichments/custom-enrichment/index.md
        - user-guides/data-transformation/enrichments/geo-enrichment/index.md
        - user-guides/data-transformation/enrichments/lookup-tables/index.md
        - user-guides/data-transformation/enrichments/unified-threat-intelligence/index.md
      - Metric Rules:
        - user-guides/data-transformation/metric-rules/recording-rules/index.md
      - Parsing:
        - user-guides/data-transformation/parsing/log-parsing-rules/index.md
        - user-guides/data-transformation/parsing/rules-cheat-sheet/index.md
        - user-guides/data-transformation/parsing/auto-json-parsing/index.md
        - user-guides/data-transformation/parsing/json-stringify/index.md
        - user-guides/data-transformation/parsing/parse-json-field/index.md
      - user-guides/data-transformation/log-normalization/index.md
      - user-guides/data-transformation/dynamic-blocking/index.md
    - Data Query:
      - Archive Query:
        - user-guides/data-query/archive-query/access-cx-data-directly/index.md
        - user-guides/data-query/archive-query/archive-query-from-the-explore-screen/index.md
        - user-guides/data-query/archive-query/archive-query-with-aws-athena/index.md
        - user-guides/data-query/archive-query/import-archived-logs/index.md
      - user-guides/data-query/background-queries/index.md
      - Log Query:
        - user-guides/data-query/log-query/log-query-simply-retrieve-your-data/index.md
        - user-guides/data-query/log-query/cross-team-query/index.md
        - user-guides/data-query/log-query/querying-coralogix-with-sql/index.md
      - user-guides/data-query/highlights/index.md
      - user-guides/data-query/metrics-api/index.md
    - Monitoring and Insights:
      - Explore Screen:
        - user-guides/monitoring-and-insights/fast-mode/index.md
        - user-guides/monitoring-and-insights/explore-screen/create-and-manage-saved-views/index.md
        - user-guides/monitoring-and-insights/explore-screen/custom-views/index.md
        - user-guides/monitoring-and-insights/explore-screen/content-column/index.md
        - user-guides/monitoring-and-insights/explore-screen/manage-keys/index.md
      - Logs Screen:
        - user-guides/monitoring-and-insights/logs-screen/logs-in-explore-screen/index.md
        - user-guides/monitoring-and-insights/logs-screen/logs-screen/index.md
        - user-guides/monitoring-and-insights/logs-screen/logs-info-panel/index.md
        - user-guides/monitoring-and-insights/logs-screen/aggregation-function/index.md
        - user-guides/monitoring-and-insights/logs-screen/coralogix-actions/index.md
        - user-guides/monitoring-and-insights/logs-screen/relative-time-series-graphs/index.md
        - user-guides/monitoring-and-insights/logs-screen/highlight-and-share/index.md
        - user-guides/monitoring-and-insights/logs-screen/shared-urls/index.md
        - user-guides/monitoring-and-insights/logs-screen/export-log-data/index.md
        - user-guides/monitoring-and-insights/logs-screen/fields-filters-and-counters/index.md
      - Distributed Tracing:
        - user-guides/monitoring-and-insights/distributed-tracing/exploring-async-trace-calls-with-otel-link/index.md
        - user-guides/monitoring-and-insights/distributed-tracing/distributed-tracing/index.md
        - user-guides/monitoring-and-insights/distributed-tracing/visualize-traces/index.md
      - user-guides/monitoring-and-insights/visual-explorer/index.md
      - Kubernetes Dashboard:
        - user-guides/monitoring-and-insights/kubernetes-dashboard/kubernetes-dashboard/index.md
        - user-guides/monitoring-and-insights/kubernetes-dashboard/kubernetes-dashboard-cluster-view/index.md
        - user-guides/monitoring-and-insights/kubernetes-dashboard/kubernetes-infrastructure-monitoring/index.md
      - Synthetic Monitoring:
        - user-guides/monitoring-and-insights/synthetic-monitoring/synthetic-monitoring-with-checkly/index.md
        - user-guides/monitoring-and-insights/synthetic-monitoring/synthetic-monitoring-with-telegraf/index.md
      - user-guides/monitoring-and-insights/label-mapping/index.md
      - user-guides/monitoring-and-insights/events2metrics/index.md
      - user-guides/monitoring-and-insights/setting-up-your-lambda-function-metrics-dashboard/index.md
      - user-guides/monitoring-and-insights/mapping-statistics/index.md
      - user-guides/monitoring-and-insights/version-benchmarks/index.md
      - user-guides/monitoring-and-insights/livetail/index.md
      - user-guides/monitoring-and-insights/loggregation-making-big-data-small/index.md
      - Anomaly Detection:
        - user-guides/monitoring-and-insights/anomaly-detection/new-error-and-critical-logs-anomaly/index.md
    - Continuous Profiling:
      - user-guides/continuous-profiling/introduction/index.md
      - user-guides/continuous-profiling/setup/index.md
      - user-guides/continuous-profiling/debug-symbols/index.md
      - user-guides/continuous-profiling/monitoring-cpu/index.md
      - user-guides/continuous-profiling/permissions/index.md
    - APM:
      - Getting Started:
        - user-guides/apm/getting-started/introduction-to-apm/index.md
        - user-guides/apm/getting-started/apm-onboarding-tutorial/index.md
        - user-guides/apm/getting-started/span-metrics/index.md
        - user-guides/apm/getting-started/ebpf-for-apm/index.md
        - user-guides/apm/getting-started/aligning-coralogix-and-otel-naming-conventions/index.md
      - Features:
        - user-guides/apm/features/service-catalog/index.md
        - user-guides/apm/features/database-monitoring/index.md
        - user-guides/apm/features/group-by-service-version/index.md
        - user-guides/apm/features/api-error-tracking/index.md
        - user-guides/apm/features/service-map/index.md
        - user-guides/apm/features/monitoring-with-alerts/index.md
        - user-guides/apm/features/service-slos/index.md
        - user-guides/apm/features/comparison-mode/index.md
        - user-guides/apm/features/transactions/index.md
        - user-guides/apm/features/apdex-score/index.md
        - user-guides/apm/features/pod-and-host/index.md
        - user-guides/apm/features/serverless-monitoring/index.md
    - RUM:
      - Getting Started:
        - user-guides/rum/getting-started/real-user-monitoring/index.md
        - user-guides/rum/getting-started/rum-integration-package/index.md
      - SDK Installation:
        - user-guides/rum/sdk-installation/android/index.md
        - Apple:  
          - user-guides/rum/sdk-installation/apple/ios/index.md
          - user-guides/rum/sdk-installation/apple/tvos/index.md
        - JavaScript:  
          - user-guides/rum/sdk-installation/javascript/cdn-browser/index.md
          - user-guides/rum/sdk-installation/javascript/npm-browser/index.md
          - user-guides/rum/sdk-installation/javascript/nextjs/index.md
          - user-guides/rum/sdk-installation/javascript/sveltekit/index.md
        - Flutter:
          - user-guides/rum/sdk-installation/flutter/mobile/index.md
          - user-guides/rum/sdk-installation/flutter/web/index.md
        - user-guides/rum/sdk-installation/react-native/index.md
      - SDK Features:
        - user-guides/rum/sdk-features/analyze-header-and-payload-data/index.md
        - user-guides/rum/sdk-features/web-workers-support/index.md
        - user-guides/rum/sdk-features/custom-logs/index.md
        - user-guides/rum/sdk-features/micro-frontend-error-tracking/index.md
        - user-guides/rum/sdk-features/measuring-memory-usage/index.md
        - user-guides/rum/sdk-features/trace-capturing/index.md
        - user-guides/rum/sdk-features/custom-spans/index.md
        - user-guides/rum/sdk-features/enhance-and-manage-browser-rum-data-with-beforesend/index.md
        - user-guides/rum/sdk-features/dom-navigation-indications/index.md
        - user-guides/rum/sdk-features/error-sampling/index.md
        - user-guides/rum/sdk-features/customized-timing/index.md
        - user-guides/rum/sdk-features/configure-your-browser-rum-data-proxy/index.md
        - user-guides/rum/sdk-features/custom-measurements/index.md
        - user-guides/rum/sdk-features/label-provider/index.md
        - user-guides/rum/sdk-features/intercept-and-control-rum-events-with-url-blueprinting/index.md
        - user-guides/rum/sdk-features/data-masking/index.md
        - user-guides/rum/sdk-features/ignore-errors/index.md
        - user-guides/rum/sdk-features/capturing-errors/index.md
        - user-guides/rum/sdk-features/source-maps/index.md
        - user-guides/rum/sdk-features/source-maps-react-native/index.md
      - Product Features:
        - user-guides/rum/product-features/network-call-performance-monitoring/index.md
        - user-guides/rum/product-features/user-measurements/index.md
        - user-guides/rum/product-features/user-action-and-error-screenshots/index.md
        - user-guides/rum/product-features/rum-saved-views/index.md
        - user-guides/rum/product-features/session-replay/index.md
        - user-guides/rum/product-features/error-tracking/index.md
        - user-guides/rum/product-features/error-tracking-user-manual/index.md
        - user-guides/rum/product-features/error-template-view/index.md
        - user-guides/rum/product-features/error-analytics/index.md
        - user-guides/rum/product-features/user-sessions/index.md
        - user-guides/rum/product-features/core-web-vitals/index.md
        - user-guides/rum/product-features/hide-error-templates/index.md
      - CLI:
        - user-guides/rum/cli/rum-cli/index.md
        - user-guides/rum/cli/uploading-debug-symbols-ios/index.md
    - AI Observability:
      - user-guides/ai-observability/ai-center/index.md
      - user-guides/ai-observability/getting-started/index.md
      - user-guides/ai-observability/ai-center-modules/index.md
      - user-guides/ai-observability/concepts-and-terms/index.md
      - user-guides/ai-observability/ai-observability-sdk-for-python/index.md
      - user-guides/ai-observability/ai-center-overview/index.md
      - user-guides/ai-observability/application-catalog/index.md
      - user-guides/ai-observability/application-overview/index.md
      - user-guides/ai-observability/eval-catalog/index.md
      - user-guides/ai-observability/llm-calls/index.md
      - user-guides/ai-observability/aispm/index.md
      - user-guides/ai-observability/observing-ecommerce-support-chatbot/index.md
      - user-guides/ai-observability/pricing-model/index.md
      - user-guides/ai-observability/permissions/index.md
    - Cora AI:
      - user-guides/cora/getting-started/index.md
      - user-guides/cora/dataprime-query-assistance/index.md
      - user-guides/cora/explain-log/index.md
      - user-guides/cora/knowledge-assistance/index.md
    - Alerting:
      - user-guides/alerting/introduction-to-alerts/index.md
      - user-guides/alerting/define-alert-details/index.md
      - Select a Source:
        - Logs:
          - user-guides/alerting/create-an-alert/logs/anomaly-detection-alerts/index.md
          - user-guides/alerting/create-an-alert/logs/immediate-notifications/index.md
          - user-guides/alerting/create-an-alert/logs/new-value-alerts/index.md
          - user-guides/alerting/create-an-alert/logs/ratio-alerts/index.md
          - user-guides/alerting/create-an-alert/logs/threshold-alerts/index.md
          - user-guides/alerting/create-an-alert/logs/time-relative-alerts/index.md
          - user-guides/alerting/create-an-alert/logs/unique-count-alerts/index.md
        - Metrics:
          - user-guides/alerting/create-an-alert/metrics/anomaly-detection-alerts/index.md
          - user-guides/alerting/create-an-alert/metrics/custom-webhooks-metric-alerts/index.md
          - user-guides/alerting/create-an-alert/metrics/threshold-alerts/index.md
        - Traces:
          - user-guides/alerting/create-an-alert/traces/tracing-alerts/index.md
        - user-guides/alerting/create-an-alert/flow-alerts/index.md
      - user-guides/alerting/multiple-alert-conditions/index.md
      - user-guides/alerting/custom-evaluation-delay/index.md
      - user-guides/alerting/anomaly-detection-deviation-percentage/index.md
      - Configure Notifications:
        - user-guides/alerting/configure-notifications/settings/index.md
        - user-guides/alerting/configure-notifications/destinations/index.md
        - user-guides/alerting/configure-notifications/source-type-schema/index.md
      - user-guides/alerting/alert-aggregation/index.md
      - user-guides/alerting/alert-suppression-rules/index.md
      - user-guides/alerting/alerts-map/index.md
      - user-guides/alerting/coralogix-reporter/index.md
      - user-guides/alerting/incidents/index.md
      - Outbound Webhooks:
        - user-guides/alerting/outbound-webhooks/aws-eventbridge-outbound-webhook/index.md
        - user-guides/alerting/outbound-webhooks/configure-alert-notifications-for-outbound-webhooks/index.md
        - user-guides/alerting/outbound-webhooks/send-log-outbound-webhooks/index.md
        - user-guides/alerting/outbound-webhooks/email-group-outbound-webhooks/index.md
        - user-guides/alerting/outbound-webhooks/generic-outbound-webhooks-alert-webhooks/index.md
        - user-guides/alerting/outbound-webhooks/create-alerting-email-templates-with-coralogix/index.md
        - user-guides/alerting/outbound-webhooks/alert-webhook-with-gcp-chat/index.md
        - user-guides/alerting/outbound-webhooks/slack-outbound-webhooks/index.md
        - user-guides/alerting/outbound-webhooks/jira-outbound-webhooks/index.md
        - user-guides/alerting/outbound-webhooks/opsgenie-outbound-webhooks/index.md
        - user-guides/alerting/outbound-webhooks/alert-webhook-with-victorops/index.md
        - user-guides/alerting/outbound-webhooks/microsoft-teams-outbound-webhooks/index.md
        - user-guides/alerting/outbound-webhooks/pagerduty-outbound-webhooks/index.md
        - user-guides/alerting/outbound-webhooks/workflow-based-microsoft-teams-outbound-webhooks/index.md
        - user-guides/alerting/outbound-webhooks/zenduty/index.md
    - Visualizations:
      - user-guides/visualizations/hosted-opensearch-view/index.md
      - user-guides/visualizations/hosted-opensearch-view/opensearch-api/index.md
      - user-guides/visualizations/hosted-grafana-view/index.md
      - user-guides/visualizations/tableau-plugin/index.md
      - user-guides/visualizations/predefined-heroku-dashboard/index.md
      - user-guides/visualizations/grafana-plugin/index.md
    - Custom Dashboards:
      - user-guides/custom-dashboards/getting-started/index.md
      - Tutorials:
        - user-guides/custom-dashboards/tutorials/dashboard-metadata/index.md
        - user-guides/custom-dashboards/tutorials/query-builder/index.md
        - user-guides/custom-dashboards/tutorials/create-and-manage-variables/index.md
        - user-guides/custom-dashboards/tutorials/create-annotations/index.md
        - user-guides/custom-dashboards/tutorials/import-and-export-custom-dashboards/index.md
        - user-guides/custom-dashboards/tutorials/add-monitoring-data-to-custom-dashboard-widgets/index.md
        - user-guides/custom-dashboards/tutorials/create-metrics-from-your-custom-dashboard-widget/index.md
        - user-guides/custom-dashboards/tutorials/create-alerts-from-custom-dashboard-widgets/index.md
        - user-guides/custom-dashboards/tutorials/migrate-from-opensearch-to-coralogix-custom-dashboards/index.md
        - user-guides/custom-dashboards/tutorials/multiple-queries-in-custom-dashboards/index.md
        - user-guides/custom-dashboards/tutorials/save-a-dashboard-as-a-pdf/index.md
      - Widgets:
        - Gauges:
          - user-guides/custom-dashboards/widgets/gauges/basic-gauges/index.md
          - user-guides/custom-dashboards/widgets/gauges/multi-gauges/index.md
        - user-guides/custom-dashboards/widgets/polystat-widget/index.md
        - user-guides/custom-dashboards/widgets/dataprime-widget/index.md
        - user-guides/custom-dashboards/widgets/markdown-widget/index.md
        - user-guides/custom-dashboards/widgets/horizontal-bar-charts/index.md
        - user-guides/custom-dashboards/widgets/vertical-bar-charts/index.md
        - user-guides/custom-dashboards/widgets/pie-charts/index.md
        - user-guides/custom-dashboards/widgets/line-charts/index.md
        - user-guides/custom-dashboards/widgets/data-tables/index.md
        - user-guides/custom-dashboards/widgets/legend-configuration-for-widgets/index.md
        - user-guides/custom-dashboards/widgets/switch-between-visualizations/index.md
    - Investigations:
      - user-guides/investigations/overview/index.md
      - user-guides/investigations/required-permissions/index.md
      - user-guides/investigations/create-an-investigation/index.md
      - user-guides/investigations/conduct-an-investigation/index.md
      - user-guides/investigations/view-and-manage-existing-investigations/index.md
      - user-guides/investigations/open-a-rum-comment/index.md
    - Notification Center:
      - Introduction:
        - user-guides/notification-center/introduction/index.md
        - user-guides/notification-center/introduction/terminology/index.md
        - user-guides/notification-center/introduction/connectors-explained/index.md
        - user-guides/notification-center/introduction/presets-explained/index.md
        - user-guides/notification-center/introduction/user-flow/index.md
      - Destintation Types:
        - user-guides/notification-center/destination-types/introduction/index.md
        - HTTPS:
          - user-guides/notification-center/destination-types/https/connector-config/index.md
          - user-guides/notification-center/destination-types/https/schema-structure/index.md
        - PagerDuty:
          - user-guides/notification-center/destination-types/pagerduty/connector-config/index.md
          - user-guides/notification-center/destination-types/pagerduty/schema-structure/index.md
        - Slack:
          - user-guides/notification-center/destination-types/slack/connector-config/index.md
          - user-guides/notification-center/destination-types/slack/schema-structure/index.md
      - Connectors:
        - user-guides/notification-center/connectors/introduction/index.md
        - user-guides/notification-center/connectors/setup/index.md
        - user-guides/notification-center/connectors/management/index.md
      - Presets:
        - user-guides/notification-center/presets/introduction/index.md
        - user-guides/notification-center/presets/alerts-preset-structure/index.md
        - Custom Preset Setup:
          - user-guides/notification-center/presets/setup-for-alerts/index.md
          - user-guides/notification-center/presets/setup-for-alerts-raw-output/index.md
        - user-guides/notification-center/presets/management/index.md
      - user-guides/notification-center/routing/index.md
      - user-guides/notification-center/dynamic-templating/index.md
      - user-guides/notification-center/migration/index.md
      - user-guides/notification-center/permissions/index.md
    - Security:
      - Getting Started:
        - user-guides/security/getting-started/coralogix-cloud-security-quick-start/index.md
        - user-guides/security/getting-started/installing-coralogix-sta/index.md
        - user-guides/security/getting-started/how-to-connect-a-wazuh-agent-to-the-sta/index.md
      - Security Data Sources:
        - user-guides/security/security-data-sources/okta-contextual-logs/index.md
        - user-guides/security/security-data-sources/pingsafe/index.md
        - user-guides/security/security-data-sources/cloudflare-data-ingestion/index.md
        - user-guides/security/security-data-sources/fortigate/index.md
      - Security Traffic Analyzer (STA):
        - user-guides/security/security-traffic-analyzer/sta-whats-in-the-box/index.md
        - user-guides/security/security-traffic-analyzer/installing-coralogix-sta-gcp/index.md
        - user-guides/security/security-traffic-analyzer/sta-capturing-network-traffic-into-cloud-storage/index.md
        - user-guides/security/security-traffic-analyzer/insights-detection/index.md
        - user-guides/security/security-traffic-analyzer/auto-generated-custom-enrichment-service/index.md
        - user-guides/security/security-traffic-analyzer/coralogix-sta-virtual-tap/index.md
        - user-guides/security/security-traffic-analyzer/kubernetes-context-enrichment-w/coralogix-sta/index.md
        - user-guides/security/security-traffic-analyzer/managing-the-sta/index.md
        - user-guides/security/security-traffic-analyzer/data-enrichment-in-coralogix-sta/index.md
        - user-guides/security/security-traffic-analyzer/sta-detection-for-encrypted-traffic/index.md
        - user-guides/security/security-traffic-analyzer/automate-vpc-mirroring/index.md
        - user-guides/security/security-traffic-analyzer/sta-dashboards/index.md
        - user-guides/security/security-traffic-analyzer/data-contained-in-sta-event-types/index.md
        - user-guides/security/security-traffic-analyzer/coralogix-sta-vs-others/index.md
        - user-guides/security/security-traffic-analyzer/sta-alerts/index.md
        - user-guides/security/security-traffic-analyzer/how-to-modify-an-sta-suricata-rule/index.md
        - user-guides/security/security-traffic-analyzer/how-to-protect-secrets-in-the-sta-config/index.md
      - Cloud Security Posture Management (CSPM):
        - user-guides/security/cloud-security-posture-management/downloading-your-security-report/index.md
        - user-guides/security/cloud-security-posture-management/gcp-security-posture-management/index.md
        - user-guides/security/cloud-security-posture-management/aws-cloud-security-posture-management/index.md
      - user-guides/security/aws-traffic-mirroring-strategies/index.md
    - Account Management:
      - Data Privacy:
        - user-guides/account-management/data-privacy/handling-pii-and-sensitive-data/index.md
      - Account Settings:
        - user-guides/account-management/account-settings/application-and-subsystem-names/index.md
        - user-guides/account-management/account-settings/coralogix-domain/index.md
        - user-guides/account-management/account-settings/general-settings/index.md
        - user-guides/account-management/account-settings/ip-access-control/index.md
        - user-guides/account-management/account-settings/notifications-preferences/index.md
        - user-guides/account-management/account-settings/session-length/index.md
        - user-guides/account-management/account-settings/team-id/index.md
      - API Keys:
        - user-guides/account-management/api-keys/api-keys/index.md
        - user-guides/account-management/api-keys/send-your-data-api-key/index.md
      - Fair Usage:
        - Metrics:
          - user-guides/account-management/fair-usage/overview/index.md
          - user-guides/account-management/fair-usage/limits/index.md
          - user-guides/account-management/fair-usage/monitoring-limits/index.md
      - Organization Management:
        - user-guides/account-management/organization-management/coralogix-entities/index.md
        - user-guides/account-management/organization-management/organization-domains/index.md
        - user-guides/account-management/organization-management/quota-management-across-organizations/index.md
        - user-guides/account-management/organization-management/create-an-organization/index.md
        - user-guides/account-management/organization-management/organization-admins/index.md
        - user-guides/account-management/organization-management/organization-admin-console/index.md
      - Payment and Billing:
        - user-guides/account-management/payment-and-billing/optimize-metrics-costs-in-coralogix-by-adjusting-your-scrape-interval/index.md
        - user-guides/account-management/payment-and-billing/data-usage-metrics/index.md
        - user-guides/account-management/payment-and-billing/plan-and-payments-management/index.md
        - user-guides/account-management/payment-and-billing/pay-as-you-go/index.md
        - user-guides/account-management/payment-and-billing/quota-management/index.md
        - user-guides/account-management/payment-and-billing/data-usage/index.md
        - user-guides/account-management/payment-and-billing/metrics-optimization/index.md
      - TCO Optimizer:
        - user-guides/account-management/tco-optimizer/traces/index.md
        - user-guides/account-management/tco-optimizer/logs/index.md
      - User Management:
        - user-guides/account-management/user-management/teams/index.md
        - user-guides/account-management/user-management/create-and-manage-teams/index.md
        - user-guides/account-management/user-management/create-roles-and-permissions/index.md
        - user-guides/account-management/user-management/scopes/index.md
        - user-guides/account-management/user-management/assign-user-roles-and-scopes-via-groups/index.md
        - user-guides/account-management/user-management/scim/index.md
        - user-guides/account-management/user-management/sso-with-saml/index.md
    - Troubleshooting:
      - user-guides/troubleshooting/coralogix-audit/index.md
      - user-guides/troubleshooting/troubleshoot-data-collection-with-coralogix/index.md
  - DataPrime:
    - dataprime/index.md
    - Introduction:
        - dataprime/introduction/welcome-to-the-dataprime-reference/index.md
        - dataprime/introduction/dataprime-explained/index.md
        - dataprime/introduction/dataprime-expression-language/index.md
    - Beginner's Guide:
        - dataprime/beginners-guide/index.md
        - dataprime/beginners-guide/query-structure/index.md
        - dataprime/beginners-guide/how-data-is-represented-in-dataprime/index.md
        - dataprime/beginners-guide/sources-in-dataprime/index.md
        - dataprime/beginners-guide/commands/index.md
        - dataprime/beginners-guide/functions/index.md
        - dataprime/beginners-guide/data-types/index.md
        - dataprime/beginners-guide/expressions/index.md
        - dataprime/beginners-guide/parameters/index.md
        - dataprime/beginners-guide/working-with-time-in-dataprime/index.md
        - dataprime/beginners-guide/troubleshooting/index.md
        - dataprime/beginners-guide/ai-query-prompts/index.md
        - dataprime/beginners-guide/fair-usage-limits/index.md
    - Advanced Guide:
        - dataprime/advanced-guide/index.md
        - dataprime/advanced-guide/dataprime-specifying-timestamp-formats/index.md
        - dataprime/advanced-guide/conditional-aggregations/index.md
    - API:
        - Background Queries:
          - dataprime/API/background-queries/grpc/index.md
          - dataprime/API/background-queries/http/index.md
        - dataprime/API/direct-archive-query-http-api/index.md
        - dataprime/API/quoting-json/index.md
    - DataPrime Language Reference:
      - dataprime/language-reference/index.md
      - dataprime/language-reference/limitations.md
      - Commands Reference:
        - dataprime/language-reference/commands-reference/index.md
        - Sources:
          - dataprime/language-reference/commands-reference/sources/source.md
          - Time:
            - dataprime/language-reference/commands-reference/sources/time/around.md
            - dataprime/language-reference/commands-reference/sources/time/between.md
            - dataprime/language-reference/commands-reference/sources/time/last.md
            - dataprime/language-reference/commands-reference/sources/time/timeshifted.md
        - dataprime/language-reference/commands-reference/aggregate.md
        - dataprime/language-reference/commands-reference/block.md
        - dataprime/language-reference/commands-reference/bottom.md
        - dataprime/language-reference/commands-reference/choose.md
        - dataprime/language-reference/commands-reference/convert.md
        - dataprime/language-reference/commands-reference/count.md
        - dataprime/language-reference/commands-reference/countby.md
        - dataprime/language-reference/commands-reference/create.md
        - dataprime/language-reference/commands-reference/dedupeby.md
        - dataprime/language-reference/commands-reference/distinct.md
        - dataprime/language-reference/commands-reference/enrich.md
        - dataprime/language-reference/commands-reference/explode.md
        - dataprime/language-reference/commands-reference/extract.md
        - dataprime/language-reference/commands-reference/filter.md
        - dataprime/language-reference/commands-reference/find_text.md
        - dataprime/language-reference/commands-reference/groupby.md
        - dataprime/language-reference/commands-reference/multigroupby.md
        - dataprime/language-reference/commands-reference/join.md
        - dataprime/language-reference/commands-reference/limit.md
        - dataprime/language-reference/commands-reference/lucene.md
        - dataprime/language-reference/commands-reference/move.md
        - dataprime/language-reference/commands-reference/orderby_sortby_order_by_sort_by.md
        - dataprime/language-reference/commands-reference/redact.md
        - dataprime/language-reference/commands-reference/remove.md
        - dataprime/language-reference/commands-reference/replace.md
        - dataprime/language-reference/commands-reference/stitch.md
        - dataprime/language-reference/commands-reference/top.md
        - dataprime/language-reference/commands-reference/union.md
        - dataprime/language-reference/commands-reference/wildfind_wildtext.md
      - Functions Reference:
        - dataprime/language-reference/functions-reference/index.md
        - Aggregration:
          - dataprime/language-reference/functions-reference/aggregation/any_value.md 
          - dataprime/language-reference/functions-reference/aggregation/approx_count_distinct.md 
          - dataprime/language-reference/functions-reference/aggregation/avg.md
          - dataprime/language-reference/functions-reference/aggregation/count_if.md 
          - dataprime/language-reference/functions-reference/aggregation/distinct_count_if.md 
          - dataprime/language-reference/functions-reference/aggregation/distinct_count.md 
          - dataprime/language-reference/functions-reference/aggregation/max_by.md 
          - dataprime/language-reference/functions-reference/aggregation/max.md 
          - dataprime/language-reference/functions-reference/aggregation/min_by.md 
          - dataprime/language-reference/functions-reference/aggregation/min.md 
          - dataprime/language-reference/functions-reference/aggregation/percentile.md 
          - dataprime/language-reference/functions-reference/aggregation/sample_stddev.md 
          - dataprime/language-reference/functions-reference/aggregation/sample_variance.md 
          - dataprime/language-reference/functions-reference/aggregation/stddev.md 
          - dataprime/language-reference/functions-reference/aggregation/sum.md 
          - dataprime/language-reference/functions-reference/aggregation/variance.md
        - Array:
          - dataprime/language-reference/functions-reference/array/arrayappend.md 
          - dataprime/language-reference/functions-reference/array/arrayconcat.md 
          - dataprime/language-reference/functions-reference/array/arraycontains.md 
          - dataprime/language-reference/functions-reference/array/arrayinsertat.md 
          - dataprime/language-reference/functions-reference/array/arrayjoin.md 
          - dataprime/language-reference/functions-reference/array/arraylength.md 
          - dataprime/language-reference/functions-reference/array/arrayremove.md 
          - dataprime/language-reference/functions-reference/array/arrayremoveat.md 
          - dataprime/language-reference/functions-reference/array/arrayreplaceall.md 
          - dataprime/language-reference/functions-reference/array/arrayreplaceat.md 
          - dataprime/language-reference/functions-reference/array/arraysort.md 
          - dataprime/language-reference/functions-reference/array/arraysplit.md 
          - dataprime/language-reference/functions-reference/array/cardinality.md 
          - dataprime/language-reference/functions-reference/array/inArray.md 
          - dataprime/language-reference/functions-reference/array/isempty.md 
          - dataprime/language-reference/functions-reference/array/issubset.md 
          - dataprime/language-reference/functions-reference/array/issuperset.md 
          - dataprime/language-reference/functions-reference/array/setdiff.md 
          - dataprime/language-reference/functions-reference/array/setdiffsymmetric.md 
          - dataprime/language-reference/functions-reference/array/setequalsto.md 
          - dataprime/language-reference/functions-reference/array/setintersection.md 
          - dataprime/language-reference/functions-reference/array/setunion.md
        - Case:
          - dataprime/language-reference/functions-reference/cases/case.md
          - dataprime/language-reference/functions-reference/cases/case_contains.md
          - dataprime/language-reference/functions-reference/cases/case_equals.md
          - dataprime/language-reference/functions-reference/cases/case_greaterthan.md
          - dataprime/language-reference/functions-reference/cases/case_lessthan.md  
        - General:
          - dataprime/language-reference/functions-reference/general/firstnonnull.md
          - dataprime/language-reference/functions-reference/general/if.md
          - dataprime/language-reference/functions-reference/general/in.md
          - dataprime/language-reference/functions-reference/general/recordlocation.md
        - IP:
          - dataprime/language-reference/functions-reference/ip/ipinsubnet.md
          - dataprime/language-reference/functions-reference/ip/ipprefix.md
        - Number:
          - dataprime/language-reference/functions-reference/number/abs.md
          - dataprime/language-reference/functions-reference/number/ceil.md
          - dataprime/language-reference/functions-reference/number/e.md
          - dataprime/language-reference/functions-reference/number/floor.md
          - dataprime/language-reference/functions-reference/number/frombase.md
          - dataprime/language-reference/functions-reference/number/ln.md
          - dataprime/language-reference/functions-reference/number/log.md
          - dataprime/language-reference/functions-reference/number/log2.md
          - dataprime/language-reference/functions-reference/number/mod.md
          - dataprime/language-reference/functions-reference/number/pi.md
          - dataprime/language-reference/functions-reference/number/power.md
          - dataprime/language-reference/functions-reference/number/random.md
          - dataprime/language-reference/functions-reference/number/randomint.md
          - dataprime/language-reference/functions-reference/number/round.md
          - dataprime/language-reference/functions-reference/number/sqrt.md
          - dataprime/language-reference/functions-reference/number/tobase.md
        - String:
          - dataprime/language-reference/functions-reference/string/bytelength.md
          - dataprime/language-reference/functions-reference/string/chr.md
          - dataprime/language-reference/functions-reference/string/codepoint.md
          - dataprime/language-reference/functions-reference/string/concat.md
          - dataprime/language-reference/functions-reference/string/contains.md
          - dataprime/language-reference/functions-reference/string/decodebase64.md
          - dataprime/language-reference/functions-reference/string/encodebase64.md
          - dataprime/language-reference/functions-reference/string/endswith.md
          - dataprime/language-reference/functions-reference/string/indexof.md
          - dataprime/language-reference/functions-reference/string/length.md
          - dataprime/language-reference/functions-reference/string/ltrim.md
          - dataprime/language-reference/functions-reference/string/matches.md
          - dataprime/language-reference/functions-reference/string/pad.md
          - dataprime/language-reference/functions-reference/string/padleft.md
          - dataprime/language-reference/functions-reference/string/padright.md
          - dataprime/language-reference/functions-reference/string/regexpsplitparts.md
          - dataprime/language-reference/functions-reference/string/rtrim.md
          - dataprime/language-reference/functions-reference/string/splitparts.md
          - dataprime/language-reference/functions-reference/string/startswith.md
          - dataprime/language-reference/functions-reference/string/substr.md
          - dataprime/language-reference/functions-reference/string/textsearch.md
          - dataprime/language-reference/functions-reference/string/tolowercase.md
          - dataprime/language-reference/functions-reference/string/touppercase.md
          - dataprime/language-reference/functions-reference/string/trim.md
        - Time:
          - dataprime/language-reference/functions-reference/time/addinterval.md
          - dataprime/language-reference/functions-reference/time/addtime.md
          - dataprime/language-reference/functions-reference/time/difftime.md
          - dataprime/language-reference/functions-reference/time/extracttime.md
          - dataprime/language-reference/functions-reference/time/formatinterval.md
          - dataprime/language-reference/functions-reference/time/formattimestamp.md
          - dataprime/language-reference/functions-reference/time/fromunixtime.md
          - dataprime/language-reference/functions-reference/time/multiplyinterval.md
          - dataprime/language-reference/functions-reference/time/now.md
          - dataprime/language-reference/functions-reference/time/parseinterval.md
          - dataprime/language-reference/functions-reference/time/parsetimestamp.md
          - dataprime/language-reference/functions-reference/time/parsetotimestamp.md
          - dataprime/language-reference/functions-reference/time/roundinterval.md
          - dataprime/language-reference/functions-reference/time/roundtime.md
          - dataprime/language-reference/functions-reference/time/subtractinterval.md
          - dataprime/language-reference/functions-reference/time/subtracttime.md
          - dataprime/language-reference/functions-reference/time/timeround.md
          - dataprime/language-reference/functions-reference/time/tointerval.md
          - dataprime/language-reference/functions-reference/time/toiso8601datetime.md
          - dataprime/language-reference/functions-reference/time/tounixtime.md
        - URL:
          - dataprime/language-reference/functions-reference/url/urldecode.md
          - dataprime/language-reference/functions-reference/url/urlencode.md
        - UUID:
          - dataprime/language-reference/functions-reference/uuid/isuuid.md
          - dataprime/language-reference/functions-reference/uuid/randomuuid.md
          - dataprime/language-reference/functions-reference/uuid/uuid.md
  - Integrations:
    - integrations/index.md
    - integrations/getting-started.md
    - integrations/extensions/index.md
    - integrations/coralogix-endpoints.md
    - AI Observability:
      - integrations/ai-observability/github-app-for-ai-discovery/index.md
    - AWS:
      - external/coralogix-aws-shipper/README.md
      - external/terraform-coralogix-aws/modules/coralogix-aws-shipper/README.md
      - Amazon Data Firehose:
        - integrations/aws/amazon-data-firehose/send-logs-using-amazon-data-firehose/index.md
        - integrations/aws/amazon-data-firehose/aws-cloudwatch-metric-streams-with-amazon-data-firehose/index.md
        - integrations/aws/amazon-data-firehose/extensions/index.md
      - AWS CloudWatch:
        - integrations/aws/aws-cloudwatch/aws-cloudwatch-data-collection-options/index.md
        - integrations/aws/aws-cloudwatch/aws-metrics-via-cloudwatch/index.md
        - integrations/aws/aws-cloudwatch/ecs-enhanced-monitoring/index.md
        - integrations/aws/aws-cloudwatch/rds-enhanced-monitoring/index.md
        - integrations/aws/aws-cloudwatch/aws-cloudwatch-metrics-processing/index.md
      - AWS PrivateLink:
        - integrations/aws/aws-privatelink/aws-privatelink/index.md
        - integrations/aws/aws-privatelink/aws-privatelink-cross-region-connection/index.md
        - integrations/aws/aws-privatelink/aws-privatelink-vpc-peering-configuration/index.md
        - integrations/aws/aws-privatelink/aws-privatelink-lambda-configuration/index.md
      - integrations/aws/aws-cloudfront-logs/index.md
      - integrations/aws/aws-elastic-beanstalk/index.md
      - integrations/aws/amazon-eks-fargate-logs/index.md
      - integrations/aws/apm-amazon-ec2/index.md
      - integrations/aws/amazon-data-firehose-terraform-module/index.md
      - integrations/aws/aws-secrets-manager-lambda-layer/index.md
      - integrations/aws/opentelemetry-ecs-fargate/index.md
      - integrations/aws/aws-eks-fargate/index.md
      - integrations/aws/aws-coudtrail-log-collection-via-sns-trigger/index.md
      - integrations/aws/aws-vpc-flow-logs-terraform-module/index.md
      - integrations/aws/aws-resource-metadata-collection/index.md
      - integrations/aws/aws-resource-metadata-collection-terraform-module/index.md
      - integrations/aws/aws-inspector/index.md
      - integrations/aws/aws-terraform-module/index.md
      - integrations/aws/aws-cloudformation-logs/index.md
      - integrations/aws/aws-lambda-telemetry-exporter/index.md
      - integrations/aws/aws-eventbridge/index.md
      - integrations/aws/aws-cloudtrail-terraform-module/index.md
      - integrations/aws/aws-msk-and-kafka/index.md
      - integrations/aws/aws-vpc-flow-logs/index.md
      - integrations/aws/aws-kinesis-with-logstash/index.md
      - integrations/aws/aws-load-balancer/index.md
    - Azure:
      - integrations/azure/azure-metrics/index.md
      - integrations/azure/azure-resource-logs/index.md
      - integrations/azure/azure-activity-logs/index.md
      - integrations/azure/microsoft-entra-id-logs/index.md
      - integrations/azure/azure-platform-monitoring/index.md
      - integrations/azure/azure-resource-manager-integration-packages/index.md
      - integrations/azure/introduction-to-microsoft-azure/index.md
      - integrations/azure/optional-configurations-microsoft-azure/index.md
      - integrations/azure/diagnostic-data-microsoft-azure-resource-manager/index.md
      - integrations/azure/azure-queue-storage-terraform-module/index.md
      - integrations/azure/azure-diagnostic-data-terraform-module/index.md
      - integrations/azure/azure-blob-storage-via-event-grid-terraform-module/index.md
      - integrations/azure/queue-storage-microsoft-azure-resource-manager/index.md
      - integrations/azure/event-hub-microsoft-azure-resource-manager/index.md
      - integrations/azure/blob-storage-via-event-grid-microsoft-azure-resource-manager/index.md
      - integrations/azure/azure-event-hub-terraform-module/index.md
      - integrations/azure/microsoft-azure-status-logs/index.md
      - integrations/azure/microsoft-azure-functions/index.md
      - integrations/azure/azure-activity-and-audit-logs-with-filebeat/index.md
      - integrations/azure/microsoft-azure-compute-scale-and-quotas/index.md
      - integrations/azure/microsoft-azure-service-bus/index.md
      - integrations/azure/microsoft-azure-virtual-network/index.md
    - CDNs:
      - integrations/cdns/cloudflare/index.md
      - integrations/cdns/cloudflare/cloudflare-logpush-terraform-module/index.md
      - integrations/cdns/fastly-logs-via-https-streaming/index.md
      - integrations/cdns/akamai-datastream/index.md
    - CI/CD:
      - integrations/ci/cd/jenkins-telemetry/index.md
      - integrations/ci/cd/jenkins-plugin/index.md
      - integrations/ci/cd/circleci/index.md
    - Contextual Data:
      - integrations/contextual-data/statuspage-data-ingestion/index.md
      - integrations/contextual-data/intercom-data-ingestion/index.md
      - integrations/contextual-data/gcp-status-logs/index.md
      - integrations/contextual-data/aws-status-logs/index.md
      - integrations/contextual-data/opsgenie-data-ingestion/index.md
      - integrations/contextual-data/slack-data-ingestion/index.md
      - integrations/contextual-data/aws-sns-data-ingestion/index.md
      - integrations/contextual-data/pagerduty-data-ingestion/index.md
      - integrations/contextual-data/github-data-ingestion/index.md
      - integrations/contextual-data/gitlab-data-ingestion/index.md
      - integrations/contextual-data/bitbucket-data-ingestion/index.md
      - integrations/contextual-data/google-workspace-data-ingestion-gcp/index.md
    - Data Ingestion:
      - integrations/data-ingestion/opentelemetry-custom-traces/index.md
      - integrations/data-ingestion/opentelemetry-custom-metrics/index.md
      - integrations/data-ingestion/opentelemetry-custom-logs/index.md
    - Docker:
      - integrations/docker/gelf/index.md
    - Files:
      - integrations/files/cockroachdb/index.md
      - integrations/files/fluent-bit/index.md
      - integrations/files/vector/index.md
      - integrations/files/nxlog/index.md
      - integrations/files/windows-event-logs-with-winlogbeat/index.md
      - integrations/files/logstash/index.md
      - integrations/files/beats-filebeat/index.md
      - integrations/files/fluentd/index.md
    - GCP:
      - integrations/gcp/gcp-getting-started/index.md
      - integrations/gcp/google-workspace/index.md
      - integrations/gcp/google-workspace-alert-center/index.md
      - integrations/gcp/google-workspace-users/index.md
      - integrations/gcp/gcp-logs/index.md
      - integrations/gcp/gcp-traces/index.md
      - integrations/gcp/gcp-metrics/index.md
      - integrations/gcp/gcp-log-explorer/index.md
      - integrations/gcp/gcp-storage/index.md
      - integrations/gcp/gcp-pub/sub-terraform-module/index.md
    - Incoming Webhooks:
      - integrations/incoming-webhooks/generic-incoming-webhooks/index.md
      - integrations/incoming-webhooks/upguard/index.md
    - Kubernetes:
      - integrations/kubernetes/fluent-bit-helm-chart-for-kubernetes/index.md
      - integrations/kubernetes/fluentd-helm-chart-for-kubernetes/index.md
      - integrations/kubernetes/kubernetes-with-fluent-bit-without-helm/index.md
      - integrations/kubernetes/kubernetes-with-fluentd-without-helm/index.md
      - integrations/kubernetes/kubernetes-with-filebeat/index.md
    - Metrics:
      - integrations/metrics/mongodb-atlas/index.md
      - integrations/metrics/metrics-usage/index.md
      - integrations/metrics/external-labels/index.md
      - integrations/metrics/collect-cloudwatch-metrics-with-telegraf/index.md
      - integrations/metrics/telegraf-operator/index.md
      - integrations/metrics/zabbix/index.md
      - integrations/metrics/nagios/index.md
      - integrations/metrics/telegraf/index.md
      - integrations/metrics/statsd/index.md
      - integrations/metrics/custom-metrics/index.md
      - integrations/metrics/rabbitmq-metrics/index.md
      - integrations/metrics/prometheus/index.md
      - integrations/metrics/metricbeat/index.md
    - PaaS Platforms:
      - integrations/paas-platforms/open-commerce-api/index.md
      - integrations/paas-platforms/salesforce-commerce-cloud/index.md
      - integrations/paas-platforms/heroku-logs/index.md
    - Prometheus:
      - integrations/prometheus/prometheus-server/index.md
      - integrations/prometheus/prometheus-agent/index.md
      - integrations/prometheus/prometheus-operator/index.md
      - integrations/prometheus/prometheus-alertmanager-data-ingestion/index.md
    - Pull:
      - integrations/pull/slack/index.md
      - integrations/pull/slack-audit-logs/index.md
      - integrations/pull/zoom/index.md
    - SDKs:
      - integrations/sdks/go/index.md
      - integrations/sdks/nlog/index.md
      - integrations/sdks/ruby/index.md
      - integrations/sdks/log4j/index.md
      - integrations/sdks/logback/index.md
      - integrations/sdks/java/index.md
      - integrations/sdks/nodejs-winston/index.md
      - integrations/sdks/nodejs-bunyan/index.md
      - integrations/sdks/nodejs/index.md
      - integrations/sdks/python-sdk/index.md
      - integrations/sdks/log4net/index.md
      - integrations/sdks/dotnet-logging/index.md
      - integrations/sdks/serilog/index.md
      - integrations/sdks/serilog-net-core/index.md
    - Security:
      - integrations/security/alcide-kaudit/index.md
      - integrations/security/alibaba-cloud-actiontrail/index.md
      - integrations/security/beats-auditbeat/index.md
      - integrations/security/beats-packetbeat/index.md
      - integrations/security/bitdefender/index.md
      - integrations/security/crowdstrike-falcon/index.md
      - integrations/security/crowdstrike-falcon-siem-connector/index.md
      - integrations/security/duo-security/index.md
      - integrations/security/github-enterprise/index.md
      - integrations/security/jumpcloud-coralogix-integration/index.md
      - integrations/security/jumpcloud-scim-identity-management-integration/index.md
      - integrations/security/kandji/index.md
      - integrations/security/microsoft-365/index.md
      - integrations/security/office-365-audit-logs/index.md
      - integrations/security/onelogin/index.md
      - integrations/security/palo-alto-network-cortex-xsoar/index.md
      - integrations/security/perimeter-81-integration/index.md
      - integrations/security/salesforce/index.md
      - integrations/security/sentinelone/index.md
      - integrations/security/sentinelone-syslog/index.md
      - integrations/security/shipping-snowflake-logs-and-audit-data-to-coralogix/index.md
      - integrations/security/snyk-vulnerability-monitoring-with-coralogix/index.md
      - integrations/security/surf/index.md
      - integrations/security/suricata/index.md
      - integrations/security/wiz/index.md
      - integrations/security/zeek/index.md
      - integrations/security/zscaler-internet-access-zia/index.md
      - integrations/security/zscaler-secure-private-access-zpa/index.md
    - Syslog:
      - integrations/syslog/configuring-tls-on-rsyslog/index.md
      - integrations/syslog/syslog/index.md
      - integrations/syslog/syslog-using-opentelemetry/index.md
      - integrations/syslog/custom-syslog/index.md
      - integrations/syslog/syslogng/index.md
      - integrations/syslog/rsyslog/index.md
  - OpenTelemetry:
    - opentelemetry/index.md
    - Getting-Started:
      - opentelemetry/getting-started/index.md
    - OTel Integrations:
      - opentelemetry/integrations/kubernetes-complete-observability-advanced-configuration/index.md
      - opentelemetry/integrations/introduction-to-kubernetes-observability-using-opentelemetry/index.md
      - opentelemetry/integrations/running-opentelemetry-as-a-cli-application/index.md
      - opentelemetry/integrations/apm-kubernetes/index.md
      - opentelemetry/integrations/apm-kubernetes-open-telemetry-opentelemetry/index.md
      - opentelemetry/integrations/collect-kubernetes-events-using-opentelemetry/index.md
      - opentelemetry/integrations/ecs-fargate/index.md
    - Configuration Options:
      - opentelemetry/configuration-options/windows-event-logs-and-opentelemetry/index.md
      - opentelemetry/configuration-options/install-opentelemetry-on-an-ec2-instance/index.md
      - opentelemetry/configuration-options/opentelemetry-using-docker/index.md
      - opentelemetry/configuration-options/aws-ecs-ec2-using-opentelemetry/index.md
      - opentelemetry/configuration-options/amazon-web-services-aws-ecs-ec2-opentelemetry-instrumentation/index.md
    - Instrumentation Options:
      - opentelemetry/instrumentation-options/nodejs-opentelemetry-instrumentation/index.md
      - opentelemetry/instrumentation-options/golang-opentelemetry-instrumentation/index.md
      - opentelemetry/instrumentation-options/php-opentelemetry-instrumentation/index.md
      - opentelemetry/instrumentation-options/opentelemetry-lambda-auto-instrumentation/index.md
      - opentelemetry/instrumentation-options/java-opentelemetry-instrumentation/index.md
      - opentelemetry/instrumentation-options/python-opentelemetry-instrumentation/index.md
    - Kubernetes Observability:
      - opentelemetry/kubernetes-observability/kubernetes-observability-using-opentelemetry/index.md
      - opentelemetry/kubernetes-observability/kubernetes-complete-observability-basic-configuration/index.md
      - opentelemetry/kubernetes-observability/advanced-configuration/index.md
      - opentelemetry/kubernetes-observability/validation/index.md
      - opentelemetry/kubernetes-observability/troubleshooting/index.md
      - opentelemetry/kubernetes-observability/faqs/index.md
      - opentelemetry/kubernetes-observability/tail-sampling-with-opentelemetry-using-kubernetes/index.md
      - opentelemetry/kubernetes-observability/target-allocator-and-prometheuscr-with-opentelemetry/index.md
    - Monitoring:
      - opentelemetry/monitoring/monitoring--windows-server-using-otel-and-prometheus/index.md
    - Tail Sampling:
      - opentelemetry/tail-sampling/tail-sampling-with-opentelemetry-using-docker-compose/index.md
      - opentelemetry/tail-sampling/tail-sampling-with-coralogix-and-opentelemetry/index.md
    - opentelemetry/workshops/index.md
  - Developer Portal:
    - developer-portal/index.md
    - APIs:
      - Getting Started:
        - developer-portal/apis/getting-started/getting-started-with-coralogix-apis/index.md
      - Data Management:
        - Alerts API:
          - developer-portal/apis/data-management/alerts-api/alerts-api-v1-v2/index.md
          - developer-portal/apis/data-management/alerts-api/alerts-grpc-api/index.md
        - developer-portal/apis/data-management/alert-suppression-rules-api/index.md
        - developer-portal/apis/data-management/incident-management-api/index.md
        - developer-portal/apis/data-management/slo-management-api/index.md
        - developer-portal/apis/data-management/service-removal-grpc-api/index.md
        - developer-portal/apis/data-management/data-usage-service-api/index.md
        - developer-portal/apis/data-management/send-your-data-management-api/index.md
        - developer-portal/apis/data-management/recording-rules-api/index.md
        - developer-portal/apis/data-management/metrics-cardinality-api/index.md
        - developer-portal/apis/data-management/metrics-cost-optimizer-api/index.md
        - developer-portal/apis/data-management/webhooks-api/index.md
        - developer-portal/apis/data-management/insights-api/index.md
        - developer-portal/apis/data-management/parsing-rules-api/index.md
        - developer-portal/apis/data-management/custom-enrichment-api/index.md
        - developer-portal/apis/data-management/service-retention-period-grpc-api/index.md
      - TCO Controls:
        - developer-portal/apis/tco-controls/tco-optimizer-http-api/index.md
        - developer-portal/apis/tco-controls/tco-tracing-policy-grpc-api/index.md
        - developer-portal/apis/tco-controls/archive-setup-grpc-api/index.md
        - developer-portal/apis/tco-controls/dynamic-tco-app/index.md
      - Log Ingestion:
        - developer-portal/apis/log-ingestion/coralogix-rest-api/index.md
        - developer-portal/apis/log-ingestion/coralogix-rest-api-singles/index.md
        - developer-portal/apis/log-ingestion/coralogix-rest-api-bulk/index.md
      - Version Tags:
        - developer-portal/apis/version-tags/github-version-tags/index.md
        - developer-portal/apis/version-tags/curl-version-tags/index.md
        - developer-portal/apis/version-tags/spinnaker-version-tags/index.md
        - developer-portal/apis/version-tags/gitlab-version-tags/index.md
        - developer-portal/apis/version-tags/microsoft-azure-devops-server-version-tags/index.md
        - developer-portal/apis/version-tags/argo-cd-version-tags/index.md
        - developer-portal/apis/version-tags/bitbucket-version-tags/index.md
      - Grafana:
        - developer-portal/apis/grafana/hosted-grafana-api/index.md
    - Infrastructure as Code:
      - CLI:
        - developer-portal/infrastructure-as-code/cli/coralogix-cli/index.md
        - developer-portal/infrastructure-as-code/cli/team-management/index.md
        - developer-portal/infrastructure-as-code/cli/saml-management/index.md
      - Terraform Provider:
        - developer-portal/infrastructure-as-code/terraform-provider/coralogix-terraform-provider/index.md
        - Integrations:
          - developer-portal/infrastructure-as-code/terraform-provider/integrations/aws-metrics-collector/index.md
          - developer-portal/infrastructure-as-code/terraform-provider/integrations/azure-metrics-collector/index.md
          - developer-portal/infrastructure-as-code/terraform-provider/integrations/crowd-strike-collector/index.md
          - developer-portal/infrastructure-as-code/terraform-provider/integrations/gcp-logs-collector/index.md
          - developer-portal/infrastructure-as-code/terraform-provider/integrations/gcp-metrics-collector/index.md
          - developer-portal/infrastructure-as-code/terraform-provider/integrations/gcp-resources/index.md
          - developer-portal/infrastructure-as-code/terraform-provider/integrations/gcp-traces-collector/index.md
          - developer-portal/infrastructure-as-code/terraform-provider/integrations/github-enterprise-collector/index.md
          - developer-portal/infrastructure-as-code/terraform-provider/integrations/google-alert-center-collector/index.md
          - developer-portal/infrastructure-as-code/terraform-provider/integrations/google-workspace-audit-logs-collector/index.md
          - developer-portal/infrastructure-as-code/terraform-provider/integrations/google-workspace-users/index.md
          - developer-portal/infrastructure-as-code/terraform-provider/integrations/microsoft-365-collector/index.md
          - developer-portal/infrastructure-as-code/terraform-provider/integrations/okta-users/index.md
          - developer-portal/infrastructure-as-code/terraform-provider/integrations/salesforce-events-collector/index.md
          - developer-portal/infrastructure-as-code/terraform-provider/integrations/sentinel-one-collector/index.md
          - developer-portal/infrastructure-as-code/terraform-provider/integrations/wiz-collector/index.md
      - Terraform Integrations:
        - AWS:
          - CloudWatch: external/terraform-coralogix-aws/modules/coralogix-aws-shipper/examples/cloudwatch-integration.md
          - Kafka: external/terraform-coralogix-aws/modules/coralogix-aws-shipper/examples/kafka-integration.md
          - Kinesis: external/terraform-coralogix-aws/modules/coralogix-aws-shipper/examples/kinesis-integration.md
          - MSK: external/terraform-coralogix-aws/modules/coralogix-aws-shipper/examples/msk-integration.md
          - S3: external/terraform-coralogix-aws/modules/coralogix-aws-shipper/examples/s3-integration.md
          - SNS: external/terraform-coralogix-aws/modules/coralogix-aws-shipper/examples/sns-integration.md
          - SQS: external/terraform-coralogix-aws/modules/coralogix-aws-shipper/examples/sqs-integration.md
      - developer-portal/infrastructure-as-code/coralogix-openapi/index.md
      - Coralogix Operator:
        - Coralogix Operator: external/coralogix-operator/README.md
        - Coralogix Operator Helm Chart: external/coralogix-operator/charts/coralogix-operator/README.md
        - Coralogix Operator Metrics: external/coralogix-operator/documents/metrics.md
        - Prometheus Integration: external/coralogix-operator/documents/prometheus-integration.md
        - CXO Observer: external/coralogix-operator/tools/cxo-observer/README.md
        - API Reference: external/coralogix-operator/documents/api.md
plugins:
  - search
  - tags
  - macros
  - glightbox
  - minify_html:
      enabled: !ENV [IS_PROD, False]
  - git-revision-date-localized:
      enabled: !ENV [IS_PROD, False]
  - redirects:
      redirect_maps:
        user-guides/notification-center/connectors/configuration-https/index.md: user-guides/notification-center/destination-types/https/connector-config/index.md
        user-guides/apm/features/service-flows/index.md: user-guides/apm/features/transactions/index.md
        integrations/aws/aws-cloudwatch/aws-cloudwatch-terraform-module/index.md: external/terraform-coralogix-aws/modules/coralogix-aws-shipper/examples/cloudwatch-integration.md
        integrations/aws/aws-kinesis-with-lambda-function-terraform-module/index.md: external/terraform-coralogix-aws/modules/coralogix-aws-shipper/examples/kinesis-integration.md
        integrations/aws/aws-s3-logs-collection-terraform-module/index.md: external/terraform-coralogix-aws/modules/coralogix-aws-shipper/examples/s3-integration.md
        user-guides/notification-center/connectors/configuration-pagerduty/index.md: user-guides/notification-center/destination-types/pagerduty/connector-config/index.md
        user-guides/notification-center/connectors/configuration-slack/index.md: user-guides/notification-center/destination-types/slack/connector-config/index.md 
        user-guides/notification-center/connectors/schema-structures/https/index.md: user-guides/notification-center/destination-types/https/schema-structure/index.md
        user-guides/notification-center/connectors/schema-structures/pagerduty/index.md: user-guides/notification-center/destination-types/pagerduty/schema-structure/index.md
        user-guides/notification-center/connectors/schema-structures/slack/index.md: user-guides/notification-center/destination-types/slack/schema-structure/index.md
        user-guides/rum/product-features/network-call-perfrormance-monitoring/index.md: user-guides/rum/product-features/network-call-performance-monitoring/index.md
        user-guides/notification-center/presets/customization/index.md: user-guides/notification-center/dynamic-templating/index.md
        user-guides/notification-center/connectors/setup-and-management/index.md: user-guides/notification-center/connectors/setup/index.md
        user-guides/alerting/alert-types/standard-alerts/index.md: user-guides/alerting/create-an-alert/logs/threshold-alerts/index.md
        integrations/aws/forward-aws-logs-via-lambda-shipper/index.md: external/coralogix-aws-shipper/README.md
        user-guides/data-query/ai-query-assistant/index.md: user-guides/cora/dataprime-query-assistance/index.md
        user-guides/index.md: user-guides/getting-started/get-started-with-coralogix/index.md
        user-guides/monitoring-and-insights/compact-mode/index.md: user-guides/monitoring-and-insights/fast-mode/index.md
        dataprime/index.md: dataprime/introduction/welcome-to-the-dataprime-reference/index.md
        integrations/index.md: integrations/getting-started.md
        opentelemetry/index.md: opentelemetry/getting-started/index.md
        developer-portal/index.md: developer-portal/apis/getting-started/getting-started-with-coralogix-apis/index.md
        adding-archive-data-to-custom-dashboard-widgets.md: user-guides/custom-dashboards/tutorials/add-monitoring-data-to-custom-dashboard-widgets/index.md
        aggregation-function.md: user-guides/monitoring-and-insights/logs-screen/aggregation-function/index.md
        ai-query-assistant.md: user-guides/cora/dataprime-query-assistance/index.md
        integrations/cdns/akamai-cloud-monitor/index.md: integrations/cdns/akamai-datastream/index.md
        alcide-kaudit.md: integrations/security/alcide-kaudit/index.md
        alert-aggregation.md: user-guides/alerting/alert-aggregation/index.md
        alert-notifications-outbound-webhooks.md: user-guides/alerting/outbound-webhooks/configure-alert-notifications-for-outbound-webhooks/index.md
        alert-suppression-rules-api.md: developer-portal/apis/data-management/alert-suppression-rules-api/index.md
        alert-suppression-rules.md: user-guides/alerting/alert-suppression-rules/index.md
        alert-webhook-with-google-chat.md: user-guides/alerting/outbound-webhooks/alert-webhook-with-gcp-chat/index.md
        alert-webhook-with-slack.md: user-guides/alerting/outbound-webhooks/slack-outbound-webhooks/index.md
        alert-webhooks.md: user-guides/alerting/outbound-webhooks/generic-outbound-webhooks-alert-webhooks/index.md
        alerts-api-v1-2.md: developer-portal/apis/data-management/alerts-api/alerts-api-v1-v2/index.md
        alerts-api-v1-v2.md: developer-portal/apis/data-management/alerts-api/alerts-api-v1-v2/index.md
        alerts-api-v3.md: developer-portal/apis/data-management/alerts-api/alerts-grpc-api/index.md
        alerts-api.md: developer-portal/apis/data-management/alerts-api/alerts-grpc-api/index.md
        alerts-map.md: user-guides/alerting/alerts-map/index.md
        alerts-rules-tags-api-key.md: user-guides/account-management/api-keys/api-keys/index.md
        amazon-eks-fargate-logs.md: integrations/aws/amazon-eks-fargate-logs/index.md
        amazon-eventbridge.md: integrations/aws/aws-eventbridge/index.md
        amazon-kinesis-data-firehose-metrics.md: integrations/aws/amazon-data-firehose/aws-cloudwatch-metric-streams-with-amazon-data-firehose/index.md
        amazon-s3-data-collection-options.md: integrations/aws/forward-aws-logs-via-lambda-shipper/index.md
        amazon-web-services-aws-ecs-ec2-opentelemetry-instrumentation.md: opentelemetry/configuration-options/amazon-web-services-aws-ecs-ec2-opentelemetry-instrumentation/index.md
        amazon-web-services-aws-s3-log-collection-via-sns-trigger.md: integrations/aws/forward-aws-logs-via-lambda-shipper/index.md
        android.md: user-guides/rum/sdk-installation/android/index.md
        apdex-score.md: user-guides/apm/features/apdex-score/index.md
        api-keys.md: user-guides/account-management/api-keys/api-keys/index.md
        apm-amazon-ec2.md: integrations/aws/apm-amazon-ec2/index.md
        apm-kubernetes-open-telemetry-opentelemetry.md: opentelemetry/integrations/apm-kubernetes-open-telemetry-opentelemetry/index.md
        apm-kubernetes.md: opentelemetry/integrations/apm-kubernetes/index.md
        apm-monitoring-alerts.md: user-guides/apm/features/monitoring-with-alerts/index.md
        apm-monitoring-with-alerts.md: user-guides/apm/features/monitoring-with-alerts/index.md
        apm-onboarding-tutorial.md: user-guides/apm/getting-started/apm-onboarding-tutorial/index.md
        apm.md: user-guides/apm/getting-started/introduction-to-apm/index.md
        apm2.md: user-guides/apm/getting-started/introduction-to-apm/index.md
        apple.md: user-guides/rum/sdk-installation/apple/ios/index.md
        application-and-subsystem-names.md: user-guides/account-management/account-settings/application-and-subsystem-names/index.md
        archive-query-from-logs-screen.md: user-guides/data-query/archive-query/archive-query-from-the-explore-screen/index.md
        archive-query.md: user-guides/data-query/background-queries/index.md
        user-guides/data-query/archive-query/archive-query/index.md: user-guides/data-query/background-queries/index.md
        archive-retention-policy.md: user-guides/data-flow/s3-archive/archive-retention-policy/index.md
        archive-s3-bucket-forever.md: user-guides/data-flow/s3-archive/connect-s3-archive/index.md
        archive-setup-grpc-api.md: developer-portal/apis/tco-controls/archive-setup-grpc-api/index.md
        argo-cd-version-tags.md: developer-portal/apis/version-tags/argo-cd-version-tags/index.md
        async-traces-otellink.md: user-guides/monitoring-and-insights/distributed-tracing/exploring-async-trace-calls-with-otel-link/index.md
        user-guides/account-management/account-management/audit-trail/index.md: user-guides/troubleshooting/coralogix-audit/index.md
        user-guides/account-management/account-management/coralogix-audit/index.md: user-guides/troubleshooting/coralogix-audit/index.md
        auditbeat.md: integrations/security/beats-auditbeat/index.md
        auto-json-parsing.md: user-guides/data-transformation/parsing/auto-json-parsing/index.md
        automate-security-incidents-management-with-cortex-xsoar-and-coralogix.md: integrations/security/palo-alto-network-cortex-xsoar/index.md
        automate-vpc-mirroring.md: user-guides/security/security-traffic-analyzer/automate-vpc-mirroring/index.md
        aws-cloudformation.md: integrations/aws/aws-cloudformation-logs/index.md
        aws-cloudfront-logs-via-firehose.md: integrations/aws/aws-cloudfront-logs/index.md
        aws-cloudfront-logs.md: integrations/aws/aws-cloudfront-logs/index.md
        aws-cloudtrail-data-collection-options.md: integrations/aws/forward-aws-logs-via-lambda-shipper/index.md
        aws-cloudtrail.md: integrations/aws/forward-aws-logs-via-lambda-shipper/index.md
        aws-cloudwatch-2.md: integrations/aws/aws-cloudwatch/aws-cloudwatch-data-collection-options/index.md
        aws-cloudwatch-integration-options.md: integrations/aws/aws-cloudwatch/aws-cloudwatch-data-collection-options/index.md
        aws-coudtrail-log-collection-via-sns-trigger.md: integrations/aws/aws-coudtrail-log-collection-via-sns-trigger/index.md
        aws-ecs-ec2-fargate-logs.md: integrations/aws/opentelemetry-ecs-fargate/index.md
        aws-ecs-fargate.md: integrations/aws/opentelemetry-ecs-fargate/index.md
        aws-eks-fargate.md: integrations/aws/aws-eks-fargate/index.md
        aws-elastic-beanstalk.md: integrations/aws/aws-elastic-beanstalk/index.md
        aws-eventbridge-outbound-webhook.md: user-guides/alerting/outbound-webhooks/aws-eventbridge-outbound-webhook/index.md
        aws-firehose.md: integrations/aws/amazon-data-firehose/send-logs-using-amazon-data-firehose/index.md
        aws-inspecto-integration.md: integrations/aws/aws-inspector/index.md
        aws-integration-guide.md: integrations/aws/forward-aws-logs-via-lambda-shipper/index.md
        aws-kinesis-data-firehose-terraform-module.md: integrations/aws/amazon-data-firehose-terraform-module/index.md
        aws-kinesis-with-lambda-function-terraform-module.md: external/terraform-coralogix-aws/modules/coralogix-aws-shipper/examples/kinesis-integration/
        aws-kinesis-with-lambda-function.md: integrations/aws/forward-aws-logs-via-lambda-shipper/index.md
        aws-kinesis-with-logstash.md: integrations/aws/aws-kinesis-with-logstash/index.md
        aws-lambda-auto-instrumentation.md: opentelemetry/instrumentation-options/opentelemetry-lambda-auto-instrumentation/index.md
        aws-lambda-opentelemetry-wrappers.md: opentelemetry/instrumentation-options/opentelemetry-lambda-auto-instrumentation/index.md
        aws-load-balancer.md: integrations/aws/forward-aws-logs-via-lambda-shipper/index.md
        aws-metrics-via-cloudwatch.md: integrations/aws/aws-cloudwatch/aws-metrics-via-cloudwatch/index.md
        integrations/aws/aws-metrics-via-cloudwatch/index.md: integrations/aws/aws-cloudwatch/aws-metrics-via-cloudwatch/index.md
        aws-privatelink-lambda-configuration.md: integrations/aws/aws-privatelink/aws-privatelink-lambda-configuration/index.md
        aws-privatelink-vpc-peering-configuration.md: integrations/aws/aws-privatelink/aws-privatelink-vpc-peering-configuration/index.md
        aws-privatelink-cross-region-connection.md: integrations/aws/aws-privatelink/aws-privatelink-cross-region-connection
        aws-resource-enrichment.md: user-guides/data-transformation/enrichments/aws-resource-enrichment/index.md
        aws-resource-metadata-collection.md: integrations/aws/aws-resource-metadata-collection/index.md
        aws-secrets-manager-lambda-layer.md: integrations/aws/aws-secrets-manager-lambda-layer/index.md
        aws-sns-data-ingestion.md: integrations/contextual-data/aws-sns-data-ingestion/index.md
        aws-status-logs.md: integrations/contextual-data/aws-status-logs/index.md
        aws-traffic-mirroring.md: user-guides/security/aws-traffic-mirroring-strategies/index.md
        aws-vpc-flow-logs-collection-options.md: integrations/aws/forward-aws-logs-via-lambda-shipper/index.md
        aws-vpc-flow-logs.md: integrations/aws/forward-aws-logs-via-lambda-shipper/index.md
        azure-active-directory-logs.md: integrations/azure/microsoft-entra-id-logs/index.md
        azure-activity-and-audit-logs-with-filebeat.md: integrations/azure/azure-activity-and-audit-logs-with-filebeat/index.md
        azure-activity-logs.md: integrations/azure/azure-activity-logs/index.md
        azure-devops-server-version-tags.md: developer-portal/apis/version-tags/microsoft-azure-devops-server-version-tags/index.md
        azure-eventhub-trigger-function.md: integrations/azure/event-hub-microsoft-azure-resource-manager/index.md
        azure-metrics.md: integrations/azure/azure-metrics/index.md
        azure-platform-monitoring.md: integrations/azure/azure-platform-monitoring/index.md
        azure-resource-logs.md: integrations/azure/azure-resource-logs/index.md
        azure-resource-manager-arm-integration-packages.md: integrations/azure/azure-resource-manager-integration-packages/index.md
        azure-status-logs.md: integrations/azure/microsoft-azure-status-logs/index.md
        bitbucket-data-ingestion.md: integrations/contextual-data/bitbucket-data-ingestion/index.md
        bitbucket-version-tags.md: developer-portal/apis/version-tags/bitbucket-version-tags/index.md
        blobstorage-microsoft-azure-functions.md: integrations/azure/blob-storage-via-event-grid-microsoft-azure-resource-manager/index.md
        browser-sdk-installation-guide.md: user-guides/rum/sdk-installation/javascript/npm-browser/index.md
        capture-opentelemetry-traces-from-your-python-applications.md: opentelemetry/instrumentation-options/python-opentelemetry-instrumentation/index.md
        cdn-browser-sdk-installation-guide.md: user-guides/rum/sdk-installation/javascript/cdn-browser/index.md
        circleci.md: integrations/ci/cd/circleci/index.md
        cloud-security-posture-cspm-gcp.md: user-guides/security/cloud-security-posture-management/gcp-security-posture-management/index.md
        cloud-security-posture-cspm.md: user-guides/security/cloud-security-posture-management/aws-cloud-security-posture-management/index.md
        cloud-security-posture-management-cspm.md: user-guides/security/cloud-security-posture-management/gcp-security-posture-management/index.md
        cloud-security-quick-start.md: user-guides/security/getting-started/coralogix-cloud-security-quick-start/index.md
        cloudflare-audit-logs.md: user-guides/security/security-data-sources/cloudflare-data-ingestion/index.md
        cloudflare-coralogix.md: integrations/cdns/cloudflare/index.md
        cloudflare.md: integrations/cdns/cloudflare/index.md
        cloudtrail-s3-terraform.md: integrations/aws/aws-cloudtrail-terraform-module/index.md
        cloudwatch-logs.md: integrations/aws/forward-aws-logs-via-lambda-shipper/index.md
        cloudwatch-metric-firehose-delivery-stream.md: integrations/aws/aws-cloudwatch/aws-cloudwatch-metrics-processing/index.md
        cloudwatch-metrics-data-collection-options.md: integrations/aws/amazon-data-firehose/aws-cloudwatch-metric-streams-with-amazon-data-firehose/index.md
        cloudwatch-metrics.md: integrations/aws/amazon-data-firehose/aws-cloudwatch-metric-streams-with-amazon-data-firehose/index.md
        cloudwatch-metricstreams-kinesis-data-firehose.md: integrations/aws/amazon-data-firehose/aws-cloudwatch-metric-streams-with-amazon-data-firehose/index.md
        cockroachdb.md: integrations/files/cockroachdb/index.md
        collect-cloudwatch-metrics-telegraf.md: integrations/metrics/collect-cloudwatch-metrics-with-telegraf/index.md
        collect-kubernetes-events-using-opentelemetry.md: opentelemetry/integrations/collect-kubernetes-events-using-opentelemetry/index.md
        comparison-mode.md: user-guides/apm/features/comparison-mode/index.md
        configure-your-browser-rum-data-proxy.md: user-guides/rum/sdk-features/configure-your-browser-rum-data-proxy/index.md
        configuring-tls-on-rsyslog.md: integrations/syslog/configuring-tls-on-rsyslog/index.md
        coralogix-action-extension.md: user-guides/monitoring-and-insights/logs-screen/coralogix-actions/index.md
        coralogix-alerts-webhook-integration-with-jira.md: user-guides/alerting/outbound-webhooks/jira-outbound-webhooks/index.md
        coralogix-amazon-web-services-aws-privatelink-endpoints.md: integrations/aws/aws-privatelink/aws-privatelink/index.md
        coralogix-apis.md: developer-portal/apis/getting-started/getting-started-with-coralogix-apis/index.md
        coralogix-aps.md: developer-portal/apis/getting-started/getting-started-with-coralogix-apis/index.md
        coralogix-aws-lambda-telemetry-exporter.md: integrations/aws/aws-lambda-telemetry-exporter/index.md
        coralogix-aws-shipper.md: integrations/aws/forward-aws-logs-via-lambda-shipper/index.md
        coralogix-cli.md: developer-portal/infrastructure-as-code/cli/coralogix-cli/index.md
        coralogix-domain.md: user-guides/account-management/account-settings/coralogix-domain/index.md
        coralogix-endpoints.md: integrations/coralogix-endpoints.md
        coralogix-entities.md: user-guides/account-management/organization-management/coralogix-entities/index.md
        user-guides/account-management/coralogix-entities/index.md: user-guides/account-management/organization-management/coralogix-entities/index.md
        coralogix-extensions-for-aws-lambda.md: integrations/aws/forward-aws-logs-via-lambda-shipper/index.md
        coralogix-features-tour.md: user-guides/getting-started/coralogix-features-tour/index.md
        coralogix-java-integration.md: integrations/sdks/java/index.md
        coralogix-kubernetes-operator-cx-operator.md: developer-portal/infrastructure-as-code/coralogix-operator/index.md
        coralogix-live-tail.md: user-guides/monitoring-and-insights/livetail/index.md
        coralogix-log4j-integration.md: integrations/sdks/log4j/index.md
        coralogix-log4net-integration.md: integrations/sdks/log4net/index.md
        coralogix-logback-integration.md: integrations/sdks/logback/index.md
        coralogix-logstash-integration.md: integrations/files/logstash/index.md
        coralogix-net-integration.md: integrations/sdks/dotnet-logging/index.md
        coralogix-nodejs-bunyan-integration.md: integrations/sdks/nodejs-bunyan/index.md
        coralogix-nodejs-integration-3.md: opentelemetry/instrumentation-options/nodejs-opentelemetry-instrumentation/index.md
        coralogix-nodejs-integration.md: opentelemetry/instrumentation-options/nodejs-opentelemetry-instrumentation/index.md
        coralogix-nodejs-winston-integration.md: integrations/sdks/nodejs-winston/index.md
        coralogix-nodejs.md: integrations/sdks/nodejs/index.md
        coralogix-operator.md: developer-portal/infrastructure-as-code/coralogix-operator/index.md
        coralogix-python-integration.md: integrations/sdks/python-sdk/index.md
        coralogix-rabbitmq-agent.md: integrations/metrics/rabbitmq-metrics/index.md
        coralogix-reporter.md: user-guides/alerting/coralogix-reporter/index.md
        coralogix-rest-api-logs.md: developer-portal/apis/log-ingestion/coralogix-rest-api-bulk/index.md
        coralogix-rest-api-singles.md: developer-portal/apis/log-ingestion/coralogix-rest-api-singles/index.md
        coralogix-rest-api.md: developer-portal/apis/log-ingestion/coralogix-rest-api/index.md
        coralogix-sta-kubernetes-context-enrichment.md: user-guides/security/security-traffic-analyzer/kubernetes-context-enrichment-w/coralogix-sta/index.md
        coralogix-sta-virtual-tap.md: user-guides/security/security-traffic-analyzer/coralogix-sta-virtual-tap/index.md
        coralogix-sta-vs-others.md: user-guides/security/security-traffic-analyzer/coralogix-sta-vs-others/index.md
        coralogix-team-id.md: user-guides/account-management/account-settings/team-id/index.md
        coralogix-terraform-provider.md: developer-portal/infrastructure-as-code/terraform-provider/coralogix-terraform-provider/index.md
        developer-portal/infrastructure-as-code/coralogix-terraform-provider/index.md: developer-portal/infrastructure-as-code/terraform-provider/coralogix-terraform-provider/index.md
        coralogix-user-defined-alerts.md: user-guides/alerting/create-an-alert/logs/threshold-alerts/index.md
        coralogix-webhook-integration-with-microsoft-teams.md: user-guides/alerting/outbound-webhooks/microsoft-teams-outbound-webhooks/index.md
        core-web-vitals.md: user-guides/rum/product-features/core-web-vitals/index.md
        create-alerting-email-templates-with-coralogix.md: user-guides/alerting/outbound-webhooks/create-alerting-email-templates-with-coralogix/index.md
        create-annotations.md: user-guides/custom-dashboards/tutorials/create-annotations/index.md
        create-lambda-function-metrics-dashboard.md: user-guides/monitoring-and-insights/setting-up-your-lambda-function-metrics-dashboard/index.md
        create-manage-saved-views.md: user-guides/monitoring-and-insights/explore-screen/create-and-manage-saved-views/index.md
        create-manage-variables.md: user-guides/custom-dashboards/tutorials/create-and-manage-variables/index.md
        create-metric-from-custom-dashboard-widget.md: user-guides/custom-dashboards/tutorials/create-metrics-from-your-custom-dashboard-widget/index.md
        creating-alerts-from-custom-dashboard-widgets.md: user-guides/custom-dashboards/tutorials/create-alerts-from-custom-dashboard-widgets/index.md
        cross-team-query.md: user-guides/data-query/log-query/cross-team-query/index.md
        crowdstrike-falcon.md: integrations/security/crowdstrike-falcon/index.md
        custom-dashboards-bar-charts.md: user-guides/custom-dashboards/widgets/vertical-bar-charts/index.md
        custom-dashboards-data-tables.md: user-guides/custom-dashboards/widgets/data-tables/index.md
        custom-dashboards-gauges.md: user-guides/custom-dashboards/widgets/gauges/basic-gauges/index.md
        custom-dashboards-line-charts.md: user-guides/custom-dashboards/widgets/line-charts/index.md
        custom-dashboards-markdown-widget.md: user-guides/custom-dashboards/widgets/markdown-widget/index.md
        custom-dashboards-pie-charts.md: user-guides/custom-dashboards/widgets/pie-charts/index.md
        custom-dashboards.md: user-guides/custom-dashboards/getting-started/index.md
        custom-enrichment-api.md: developer-portal/apis/data-management/custom-enrichment-api/index.md
        custom-log-enrichment.md: user-guides/data-transformation/enrichments/custom-enrichment/index.md
        custom-metrics.md: integrations/metrics/custom-metrics/index.md
        custom-syslog.md: integrations/syslog/custom-syslog/index.md
        custom-webhooks-metric-alerts.md: user-guides/alerting/create-an-alert/metrics/custom-webhooks-metric-alerts/index.md
        dashboard-widgets.md: user-guides/custom-dashboards/getting-started/index.md
        data-collection-s3.md: integrations/aws/forward-aws-logs-via-lambda-shipper/index.md
        data-contained-in-sta-event-types.md: user-guides/security/security-traffic-analyzer/data-contained-in-sta-event-types/index.md
        data-enrichment-coralogix-sta.md: user-guides/security/security-traffic-analyzer/data-enrichment-in-coralogix-sta/index.md
        data-usage-metrics.md: user-guides/account-management/payment-and-billing/data-usage-metrics/index.md
        data-usage-service-api.md: developer-portal/apis/data-management/data-usage-service-api/index.md
        data-usage.md: user-guides/account-management/payment-and-billing/data-usage/index.md
        database-monitoring.md: user-guides/apm/features/database-monitoring/index.md
        datamap.md: user-guides/custom-dashboards/widgets/polystat-widget/index.md
        user-guides/monitoring-and-insights/datamap/index.md: user-guides/custom-dashboards/widgets/polystat-widget/index.md
        dataprime-cheat-sheet.md: dataprime/introduction/welcome-to-the-dataprime-reference/index.md
        dataprime-query-language.md: dataprime/introduction/welcome-to-the-dataprime-reference/index.md
        user-guides/dataprime/dataprime-query-language/index.md: dataprime/introduction/welcome-to-the-dataprime-reference/index.md
        dataprime-expression-language-dpxl.md: dataprime/introduction/dataprime-expression-language/index.md
        dataprime-quick-start-guide.md: dataprime/introduction/welcome-to-the-dataprime-reference/index.md
        dataprime-specifying-timestamp-formats.md: dataprime/advanced-guide/dataprime-specifying-timestamp-formats/index.md
        dataprime-widget.md: user-guides/custom-dashboards/widgets/dataprime-widget/index.md
        user-guides/data-query/dataprime-expression-language/index.md: dataprime/introduction/dataprime-expression-language/index.md
        user-guides/data-query/dataprime/dataprime-quick-start-guide/index.md: dataprime/introduction/welcome-to-the-dataprime-reference/index.md
        user-guides/data-query/dataprime/dataprime-specifying-timestamp-formats/index.md: dataprime/introduction/welcome-to-the-dataprime-reference/index.md
        user-guides/data-query/dataprime/dataprime-glossary-operators-and-expressions/index.md: dataprime/language-reference/index.md
        user-guides/data-query/dataprime/dataprime-cheat-sheet/index.md: dataprime/language-reference/index.md
        dataprime/dataprime-cheat-sheet/index.md: dataprime/language-reference/index.md
        user-guides/data-query/dataprime/dataprime-query-language/index.md: dataprime/introduction/welcome-to-the-dataprime-reference/index.md
        dataprime/dataprime-query-language/index.md: dataprime/introduction/welcome-to-the-dataprime-reference/index.md
        deployment-of-customized-aws-msk.md: user-guides/data-flow/forwarders/deployment-of-customized-aws-msk/index.md
        diagnostic-data-microsoft-azure-resource-manager-arm.md: integrations/azure/diagnostic-data-microsoft-azure-resource-manager/index.md
        direct-query-http-api.md: developer-portal/apis/data-query/direct-archive-query-http-api/index.md
        distributed-tracing.md: user-guides/monitoring-and-insights/distributed-tracing/distributed-tracing/index.md
        docker-gelf.md: integrations/docker/gelf/index.md
        downloading-your-security-report.md: user-guides/security/cloud-security-posture-management/downloading-your-security-report/index.md
        duo-security.md: integrations/security/duo-security/index.md
        dynamic-alerts.md: user-guides/alerting/create-an-alert/logs/anomaly-detection-alerts/index.md
        user-guides/alerting/create-an-alert/anomaly-alerts/index.md: user-guides/alerting/create-an-alert/logs/anomaly-detection-alerts/index.md
        dynamic-blocking.md: user-guides/data-transformation/dynamic-blocking/index.md
        dynamic-tco-app.md: developer-portal/apis/tco-controls/dynamic-tco-app/index.md
        elastic-api.md: user-guides/visualizations/hosted-opensearch-view/opensearch-api/index.md
        email-group-outbound-webhooks.md: user-guides/alerting/outbound-webhooks/email-group-outbound-webhooks/index.md
        error-analytics.md: user-guides/rum/product-features/error-analytics/index.md
        error-template-view.md: user-guides/rum/product-features/error-template-view/index.md
        error-tracking-user-manual.md: user-guides/rum/product-features/error-tracking-user-manual/index.md
        error-tracking.md: user-guides/rum/product-features/error-tracking/index.md
        error-volume-anomaly.md: user-guides/alerting/incidents/index.md
        event-viewer-logs.md: opentelemetry/configuration-options/windows-event-logs-and-opentelemetry/index.md
        event2metrics.md: user-guides/monitoring-and-insights/events2metrics/index.md
        events2metrics.md: user-guides/monitoring-and-insights/events2metrics/index.md
        explore-screen-content-column.md: user-guides/monitoring-and-insights/explore-screen/content-column/index.md
        explore-screen-custom-views.md: user-guides/monitoring-and-insights/explore-screen/custom-views/index.md
        explore-screen-manage-columns.md: user-guides/monitoring-and-insights/explore-screen/manage-keys/index.md
        explore-screen-manage-keys.md: user-guides/monitoring-and-insights/explore-screen/manage-keys/index.md
        explore-screen.md: user-guides/monitoring-and-insights/logs-screen/logs-in-explore-screen/index.md
        export-log-data-and-dashboards.md: user-guides/monitoring-and-insights/logs-screen/export-log-data/index.md
        extension-packages.md: user-guides/getting-started/packages-and-extensions/extension-packages/index.md
        external-labels.md: integrations/metrics/external-labels/index.md
        faqs-kubernetes-observability-using-opentelemetry.md: opentelemetry/kubernetes-observability/faqs/index.md
        fastly-logs-integration-through-https-streaming.md: integrations/cdns/fastly-logs-via-https-streaming/index.md
        fields-filters-and-counters.md: user-guides/monitoring-and-insights/logs-screen/fields-filters-and-counters/index.md
        filebeat.md: integrations/files/beats-filebeat/index.md
        firehose-cloudwatch-logs.md: integrations/aws/amazon-data-firehose/send-logs-using-amazon-data-firehose/index.md
        firehose-fargate-logs.md: integrations/aws/amazon-data-firehose/send-logs-using-amazon-data-firehose/index.md
        firehose-metrics.md: integrations/aws/amazon-data-firehose/aws-cloudwatch-metric-streams-with-amazon-data-firehose/index.md
        firehose-waf-logs.md: integrations/aws/amazon-data-firehose/send-logs-using-amazon-data-firehose/index.md
        flow-alert.md: user-guides/alerting/create-an-alert/flow-alerts/index.md
        flow-alerts.md: user-guides/alerting/create-an-alert/flow-alerts/index.md
        fluent-bit.md: integrations/files/fluent-bit/index.md
        fluentbit-helm-chart-for-kubernetes.md: integrations/kubernetes/fluent-bit-helm-chart-for-kubernetes/index.md
        fluentd-helm-chart-for-kubernetes.md: integrations/kubernetes/fluentd-helm-chart-for-kubernetes/index.md
        fluentd.md: integrations/files/fluentd/index.md
        flutter.md: user-guides/rum/sdk-installation/flutter/mobile/index.md
        fortigate-traffic-logging.md: user-guides/security/security-data-sources/fortigate/index.md
        forwarders.md: user-guides/data-flow/forwarders/index.md
        gauges.md: user-guides/custom-dashboards/widgets/gauges/basic-gauges/index.md
        gcp-getting-started.md: integrations/gcp/gcp-getting-started/index.md
        gcp-log-explorer.md: integrations/gcp/gcp-log-explorer/index.md
        gcp-logs.md: integrations/gcp/gcp-logs/index.md
        gcp-metrics.md: integrations/gcp/gcp-metrics/index.md
        gcp-status-logs.md: integrations/contextual-data/gcp-status-logs/index.md
        gcp-traces.md: integrations/gcp/gcp-traces/index.md
        general-settings.md: user-guides/account-management/account-settings/general-settings/index.md
        generic-firehose-logs.md: integrations/aws/amazon-data-firehose/send-logs-using-amazon-data-firehose/index.md
        generic-incoming-webhooks.md: integrations/incoming-webhooks/generic-incoming-webhooks/index.md
        generic-outbound-webhooks.md: user-guides/alerting/outbound-webhooks/generic-outbound-webhooks-alert-webhooks/index.md
        generic-outgoing-webhooks.md: user-guides/alerting/outbound-webhooks/generic-outbound-webhooks-alert-webhooks/index.md
        geo-queries-using-ip-based-geo-enrichment.md: user-guides/data-transformation/enrichments/geo-enrichment/index.md
        getting-started-with-coralogix-alerts.md: user-guides/alerting/introduction-to-alerts/index.md
        getting-started.md: user-guides/getting-started/get-started-with-coralogix/index.md
        github-data-ingestion.md: integrations/contextual-data/github-data-ingestion/index.md
        github-version-tags.md: developer-portal/apis/version-tags/github-version-tags/index.md
        gitlab-data-ingestion.md: integrations/contextual-data/gitlab-data-ingestion/index.md
        gitlab-version-tags.md: developer-portal/apis/version-tags/gitlab-version-tags/index.md
        glossary-dataprime-operators.md: dataprime/language-reference/commands-reference/index.md
        go.md: integrations/sdks/go/index.md
        golang-open-telemetry-instrumentation.md: opentelemetry/instrumentation-options/golang-opentelemetry-instrumentation/index.md
        google-alert-center.md: integrations/gcp/google-workspace-alert-center/index.md
        google-cloud-pub-sub.md: integrations/gcp/gcp-logs/index.md
        google-cloud-storage.md: integrations/gcp/gcp-storage/index.md
        google-workspace-alert-center.md: integrations/gcp/google-workspace-alert-center/index.md
        google-workspace-integration.md: integrations/contextual-data/google-workspace-data-ingestion-gcp/index.md
        google-workspace-users.md: integrations/gcp/google-workspace-users/index.md
        google-workspace.md: integrations/gcp/google-workspace/index.md
        grafana-api.md: developer-portal/apis/grafana/hosted-grafana-api/index.md
        grafana-plugin.md: user-guides/visualizations/grafana-plugin/index.md
        groups.md: user-guides/account-management/user-management/assign-user-roles-and-scopes-via-groups/index.md
        gsuite-integration.md: integrations/contextual-data/google-workspace-data-ingestion-gcp/index.md
        guide-first-steps-coralogix.md: user-guides/getting-started/get-started-with-coralogix/index.md
        heroku-logging.md: integrations/paas-platforms/heroku-logs/index.md
        horizontal-bar-charts.md: user-guides/custom-dashboards/widgets/horizontal-bar-charts/index.md
        hosted-grafana-view.md: user-guides/visualizations/hosted-grafana-view/index.md
        hosted-opensearch-view.md: user-guides/visualizations/hosted-opensearch-view/index.md
        how-to-connect-a-wazuh-agent-to-the-sta.md: user-guides/security/getting-started/how-to-connect-a-wazuh-agent-to-the-sta/index.md
        how-to-install-coralogix-sta.md: user-guides/security/getting-started/installing-coralogix-sta/index.md
        how-to-modify-an-sta-suricata-rule.md: user-guides/security/security-traffic-analyzer/how-to-modify-an-sta-suricata-rule/index.md
        how-to-protect-secrets-in-the-sta-config.md: user-guides/security/security-traffic-analyzer/how-to-protect-secrets-in-the-sta-config/index.md
        how-to-troubleshoot-your-log-shipment.md: user-guides/troubleshooting/troubleshoot-data-collection-with-coralogix/index.md
        ignore-errors.md: user-guides/rum/sdk-features/ignore-errors/index.md
        import-archived-logs.md: user-guides/data-query/archive-query/import-archived-logs/index.md
        import-export-custom-dashboards.md: user-guides/custom-dashboards/tutorials/import-and-export-custom-dashboards/index.md
        incidents-api.md: developer-portal/apis/data-management/incident-management-api/index.md
        incidents.md: user-guides/alerting/incidents/index.md
        infrastructure-monitoring.md: user-guides/monitoring-and-insights/kubernetes-dashboard/kubernetes-dashboard/index.md
        insights-api.md: developer-portal/apis/data-management/insights-api/index.md
        insights-detection.md: user-guides/security/security-traffic-analyzer/insights-detection/index.md
        install-opentelemetry-ec2.md: opentelemetry/configuration-options/install-opentelemetry-on-an-ec2-instance/index.md
        install-opentelemetry-on-an-ec2-instance.md: opentelemetry/configuration-options/install-opentelemetry-on-an-ec2-instance/index.md
        installing-coralogix-sta-gcp.md: user-guides/security/security-traffic-analyzer/installing-coralogix-sta-gcp/index.md
        integrating-coralogix-alerts-with-opsgenie.md: user-guides/alerting/outbound-webhooks/opsgenie-outbound-webhooks/index.md
        integration-packages-coralogix-apps.md: user-guides/getting-started/packages-and-extensions/integration-packages/index.md
        integration-packages.md: user-guides/getting-started/packages-and-extensions/integration-packages/index.md
        intercept-control-rum-events-with-url-blueprinting.md: user-guides/rum/sdk-features/intercept-and-control-rum-events-with-url-blueprinting/index.md
        intercom-logs.md: integrations/contextual-data/intercom-data-ingestion/index.md
        introduction-to-kubernetes-observability-using-opentelemetry.md: opentelemetry/integrations/introduction-to-kubernetes-observability-using-opentelemetry/index.md
        introduction-to-microsoft-azure.md: integrations/azure/introduction-to-microsoft-azure/index.md
        ip-access-control.md: user-guides/account-management/account-settings/ip-access-control/index.md
        jaeger.md: opentelemetry/getting-started/index.md
        java-opentelemetry-instrumentation.md: opentelemetry/instrumentation-options/java-opentelemetry-instrumentation/index.md
        jenkins-telemetry.md: integrations/ci/cd/jenkins-telemetry/index.md
        jenkins.md: integrations/ci/cd/jenkins-plugin/index.md
        json-stringify.md: user-guides/data-transformation/parsing/json-stringify/index.md
        jumpcloud-coralogix-integration.md: integrations/security/jumpcloud-coralogix-integration/index.md
        jumpcloud-corlalogix-integration.md: integrations/security/jumpcloud-coralogix-integration/index.md
        jumpcloud-scim-identity-management-integration.md: integrations/security/jumpcloud-scim-identity-management-integration/index.md
        k8s-otel-advancedconfig.md: opentelemetry/kubernetes-observability/kubernetes-observability-using-opentelemetry/index.md
        kafka-and-amazon-msk.md: integrations/aws/aws-msk-and-kafka/index.md
        kubernetes-collector.md: user-guides/monitoring-and-insights/kubernetes-dashboard/kubernetes-infrastructure-monitoring/index.md
        kubernetes-dashboard-cluster-view.md: user-guides/monitoring-and-insights/kubernetes-dashboard/kubernetes-dashboard-cluster-view/index.md
        kubernetes-dashboard.md: user-guides/monitoring-and-insights/kubernetes-dashboard/kubernetes-dashboard/index.md
        kubernetes-integration.md: integrations/kubernetes/kubernetes-with-fluentd-without-helm/index.md
        kubernetes-observability-opentelemetry.md: opentelemetry/kubernetes-observability/kubernetes-observability-using-opentelemetry/index.md
        kubernetes-with-filebeat.md: integrations/kubernetes/kubernetes-with-filebeat/index.md
        kubernetes-with-fluent-bit.md: integrations/kubernetes/kubernetes-with-fluent-bit-without-helm/index.md
        label-mapping.md: user-guides/monitoring-and-insights/label-mapping/index.md
        label-provider.md: user-guides/rum/sdk-features/label-provider/index.md
        log-normalization.md: user-guides/data-transformation/log-normalization/index.md
        log-parsing-rules.md: user-guides/data-transformation/parsing/log-parsing-rules/index.md
        log-query-simply-retrieve-data.md: user-guides/data-query/log-query/log-query-simply-retrieve-your-data/index.md
        logs-screen-highlighting.md: user-guides/monitoring-and-insights/logs-screen/highlight-and-share/index.md
        logs-screen.md: user-guides/monitoring-and-insights/logs-screen/logs-screen/index.md
        logs2metrics.md: user-guides/monitoring-and-insights/events2metrics/index.md
        lookup-tables.md: user-guides/data-transformation/enrichments/lookup-tables/index.md
        machine-learning-metric-alert.md: user-guides/alerting/create-an-alert/metrics/anomaly-detection-alerts/index.md
        management-api-endpoints.md: integrations/coralogix-endpoints.md#management
        managing-the-sta.md: user-guides/security/security-traffic-analyzer/managing-the-sta/index.md
        managing-your-organization-manage-admins.md: user-guides/account-management/organization-management/organization-admins/index.md
        managing-your-organization-my-teams.md: user-guides/account-management/user-management/create-and-manage-teams/index.md
        user-guides/account-management/account-management/create-and-manage-teams/index.md: user-guides/account-management/user-management/create-and-manage-teams/index.md
        managing-your-organization-organization-settings.md: user-guides/account-management/organization-management/organization-domains/index.md
        managing-your-organization-quota-manager.md: user-guides/account-management/organization-management/quota-management-across-organizations/index.md
        managing-your-organization.md: user-guides/account-management/organization-management/create-an-organization/index.md
        mapping-statistics.md: user-guides/monitoring-and-insights/mapping-statistics/index.md
        mask-data.md: user-guides/rum/sdk-features/data-masking/index.md
        metric-alerts-promql.md: user-guides/alerting/create-an-alert/metrics/threshold-alerts/index.md
        metric-data.md: integrations/metrics/metricbeat/index.md
        metricbeat.md: integrations/metrics/metricbeat/index.md
        metrics-api.md: user-guides/data-query/metrics-api/index.md
        developer-portal/infrastructure-as-code/metrics-api/index.md: user-guides/data-query/metrics-api/index.md
        metrics-cardinality.md: developer-portal/apis/data-management/metrics-cardinality-api/index.md
        metrics-optimization.md: user-guides/account-management/payment-and-billing/metrics-optimization/index.md
        metrics-usage.md: integrations/metrics/metrics-usage/index.md
        microsoft-azure-compute-scale-and-quotas.md: integrations/azure/microsoft-azure-compute-scale-and-quotas/index.md
        microsoft-azure-functions.md: integrations/azure/microsoft-azure-functions/index.md
        microsoft-azure-service-bus.md: integrations/azure/microsoft-azure-service-bus/index.md
        microsoft-azure-virtual-network.md: integrations/azure/microsoft-azure-virtual-network/index.md
        microsoftentraid-logs.md: integrations/azure/microsoft-entra-id-logs/index.md
        migrating-from-opensearch-to-coralogix-custom-dashboards.md: user-guides/custom-dashboards/tutorials/migrate-from-opensearch-to-coralogix-custom-dashboards/index.md
        mongodb-atlas.md: integrations/metrics/mongodb-atlas/index.md
        monitor-windows-server-using-otel.md: opentelemetry/monitoring/monitoring--windows-server-using-otel-and-prometheus/index.md
        monitoring-explore-screen.md: user-guides/custom-dashboards/widgets/polystat-widget/index.md
        multiple-queries-in-custom-dashboard-widgets.md: user-guides/custom-dashboards/tutorials/multiple-queries-in-custom-dashboards/index.md
        nagios-coralogix.md: integrations/metrics/nagios/index.md
        nagios.md: integrations/metrics/nagios/index.md
        new-value-alerts.md: user-guides/alerting/create-an-alert/logs/new-value-alerts/index.md
        nlog.md: integrations/sdks/nlog/index.md
        node-js.md: opentelemetry/instrumentation-options/nodejs-opentelemetry-instrumentation/index.md
        notifications-preferences.md: user-guides/account-management/account-settings/notifications-preferences/index.md
        nxlog.md: integrations/files/nxlog/index.md
        office-365-audit-logs.md: integrations/security/office-365-audit-logs/index.md
        okta-audit-logs.md: user-guides/security/security-data-sources/okta-contextual-logs/index.md
        okta-contextual-logs.md: user-guides/security/security-data-sources/okta-contextual-logs/index.md
        onelogin.md: integrations/security/onelogin/index.md
        open-commerce-api.md: integrations/paas-platforms/open-commerce-api/index.md
        openapi.md: developer-portal/infrastructure-as-code/coralogix-openapi/index.md
        opensearch-api.md: user-guides/visualizations/hosted-opensearch-view/opensearch-api/index.md
        developer-portal/infrastructure-as-code/opensearch-api/index.md: user-guides/visualizations/hosted-opensearch-view/opensearch-api/index.md
        opentelemetry-collector-installation-ecs-fargate.md: integrations/aws/opentelemetry-ecs-fargate/index.md
        opentelemetry/integrations/opentelemetry-collector-installation-ecs-fargate/index.md: integrations/aws/opentelemetry-ecs-fargate/index.md
        opentelemetry-custom-logs.md: developer-portal/apis/data-ingestion/opentelemetry-custom-logs/index.md
        opentelemetry-custom-metrics.md: developer-portal/apis/data-ingestion/opentelemetry-custom-metrics/index.md
        opentelemetry-custom-traces.md: developer-portal/apis/data-ingestion/opentelemetry-custom-traces/index.md
        opentelemetry-tracing.md: opentelemetry/getting-started/index.md
        opentelemetry-using-docker.md: opentelemetry/configuration-options/opentelemetry-using-docker/index.md
        opentelemetry-using-ecs-ec2.md: opentelemetry/configuration-options/aws-ecs-ec2-using-opentelemetry/index.md
        opentelemetry-using-kubernetes.md: opentelemetry/kubernetes-observability/kubernetes-observability-using-opentelemetry/index.md
        opentelemetry-using-windows.md: opentelemetry/monitoring/monitoring--windows-server-using-otel-and-prometheus/index.md
        opentelemetry2.md: opentelemetry/getting-started/index.md
        opsgenie-data-ingestion.md: integrations/contextual-data/opsgenie-data-ingestion/index.md
        optimize-log-management-costs.md: user-guides/account-management/tco-optimizer/logs/index.md
        optimize-metrics-costs-in-coralogix-by-adjusting-your-scrape-interval.md: user-guides/account-management/payment-and-billing/optimize-metrics-costs-in-coralogix-by-adjusting-your-scrape-interval/index.md
        optional-configurations-microsoft-azure.md: integrations/azure/optional-configurations-microsoft-azure/index.md
        organization-admin-console.md: user-guides/account-management/organization-management/organization-admin-console/index.md
        otel-collector-for-k8s.md: opentelemetry/kubernetes-observability/kubernetes-complete-observability-basic-configuration/index.md
        otel-ecs-fargate.md: integrations/aws/opentelemetry-ecs-fargate/index.md
        otel-lambda-auto-instrumentation.md: opentelemetry/instrumentation-options/opentelemetry-lambda-auto-instrumentation/index.md
        packetbeat.md: integrations/security/beats-packetbeat/index.md
        pagerduty-data-ingestion.md: integrations/contextual-data/pagerduty-data-ingestion/index.md
        pagerduty-integration.md: user-guides/alerting/outbound-webhooks/pagerduty-outbound-webhooks/index.md
        parse-json-field.md: user-guides/data-transformation/parsing/parse-json-field/index.md
        partition-labels.md: integrations/metrics/external-labels/index.md
        pay-as-you-go.md: user-guides/account-management/payment-and-billing/pay-as-you-go/index.md
        perimeter-81-integration.md: integrations/security/perimeter-81-integration/index.md
        php-opentelemetry-instrumentation.md: opentelemetry/instrumentation-options/php-opentelemetry-instrumentation/index.md
        pingsafe-using-fluentd.md: user-guides/security/security-data-sources/pingsafe/index.md
        plan-and-payments-management.md: user-guides/account-management/payment-and-billing/plan-and-payments-management/index.md
        pod-host.md: user-guides/apm/features/pod-and-host/index.md
        predefined-heroku-visualizations.md: user-guides/visualizations/predefined-heroku-dashboard/index.md
        private-key.md: user-guides/account-management/api-keys/send-your-data-api-key/index.md
        private-keys-management-api.md: developer-portal/apis/data-management/send-your-data-management-api/index.md
        prometheus-agent.md: integrations/prometheus/prometheus-agent/index.md
        prometheus-alertmanager.md: integrations/prometheus/prometheus-alertmanager-data-ingestion/index.md
        prometheus-api.md: user-guides/data-query/metrics-api/index.md
        prometheus-operator.md: integrations/prometheus/prometheus-operator/index.md
        prometheus-server.md: integrations/prometheus/prometheus-server/index.md
        prometheus.md: integrations/metrics/prometheus/index.md
        query-archive-with-aws-athena.md: user-guides/data-query/archive-query/archive-query-with-aws-athena/index.md
        query-builder.md: user-guides/custom-dashboards/tutorials/query-builder/index.md
        query-s3-coralogix-archive-bucket-cx-data.md: user-guides/data-query/archive-query/access-cx-data-directly/index.md
        querying-coralogix-with-sql.md: user-guides/data-query/log-query/querying-coralogix-with-sql/index.md
        queue-storage-microsoft-azure-functions.md: integrations/azure/queue-storage-microsoft-azure-resource-manager/index.md
        quota-management.md: user-guides/account-management/payment-and-billing/quota-management/index.md
        ratio-alerts.md: user-guides/alerting/create-an-alert/logs/ratio-alerts/index.md
        react-native-sdk-installation-guide.md: user-guides/rum/sdk-installation/react-native/index.md
        real-user-monitoring.md: user-guides/rum/getting-started/real-user-monitoring/index.md
        recording-rules-2.md: user-guides/data-transformation/metric-rules/recording-rules/index.md
        recording-rules-api.md: developer-portal/apis/data-management/recording-rules-api/index.md
        recording-rules.md: developer-portal/apis/data-management/recording-rules-api/index.md
        recordingrules.md: user-guides/data-transformation/metric-rules/recording-rules/index.md
        reindex.md: user-guides/data-query/background-queries/index.md
        relative-time-series-graphs.md: user-guides/monitoring-and-insights/logs-screen/relative-time-series-graphs/index.md
        rest-api-bulk.md: developer-portal/apis/log-ingestion/coralogix-rest-api-bulk/index.md
        rest-api-singles.md: developer-portal/apis/log-ingestion/coralogix-rest-api-singles/index.md
        role-based-access-control-logs.md: user-guides/account-management/user-management/create-roles-and-permissions/index.md
        role-based-access-control-traces.md: user-guides/account-management/user-management/create-roles-and-permissions/index.md
        roles-permissions.md: user-guides/account-management/user-management/create-roles-and-permissions/index.md
        rsyslog.md: integrations/syslog/rsyslog/index.md
        ruby.md: integrations/sdks/ruby/index.md
        rules-api.md: developer-portal/apis/data-management/parsing-rules-api/index.md
        rules-cheat-sheet.md: user-guides/data-transformation/parsing/rules-cheat-sheet/index.md
        rules-toggle.md: developer-portal/apis/data-management/parsing-rules-api/index.md
        rum-beforesend.md: user-guides/rum/sdk-features/enhance-and-manage-browser-rum-data-with-beforesend/index.md
        rum-integration-package.md: user-guides/rum/getting-started/rum-integration-package/index.md
        rum-ios-monitoring-setup.md: user-guides/rum/sdk-installation/apple/ios/index.md
        rum-saved-views.md: user-guides/rum/product-features/rum-saved-views/index.md
        rum-source-maps.md: user-guides/rum/sdk-features/source-maps/index.md
        running-opentelemetry-as-a-cli-application.md: opentelemetry/integrations/running-opentelemetry-as-a-cli-application/index.md
        salesforce-commerce-cloud.md: integrations/paas-platforms/salesforce-commerce-cloud/index.md
        salesforce-integration.md: integrations/security/salesforce/index.md
        saml-management-via-cli.md: developer-portal/infrastructure-as-code/cli/saml-management/index.md
        save-a-dashboard-as-a-pdf.md: user-guides/custom-dashboards/tutorials/save-a-dashboard-as-a-pdf/index.md
        scim.md: user-guides/account-management/user-management/scim/index.md
        scopes.md: user-guides/account-management/user-management/scopes/index.md
        security-traffic-analyzer-sta-dashboards.md: user-guides/security/security-traffic-analyzer/sta-dashboards/index.md
        send-log-outbound-webhooks.md: user-guides/alerting/outbound-webhooks/send-log-outbound-webhooks/index.md
        send-logs-using-aws-kinesis-data-firehose.md: integrations/aws/amazon-data-firehose/send-logs-using-amazon-data-firehose/index.md
        send-your-data-api-key.md: user-guides/account-management/api-keys/send-your-data-api-key/index.md
        send-your-data-management-api.md: developer-portal/apis/data-management/send-your-data-management-api/index.md
        sentinelone-integration-package.md: integrations/security/sentinelone/index.md
        sentinelone.md: integrations/security/sentinelone-syslog/index.md
        serilog-net-core.md: integrations/sdks/serilog-net-core/index.md
        serilog-net-opentelemetry.md: integrations/sdks/serilog/index.md
        serilog.md: integrations/sdks/serilog/index.md
        serverless-monitoring.md: user-guides/apm/features/serverless-monitoring/index.md
        service-catalog.md: user-guides/apm/features/service-catalog/index.md
        service-flows.md: user-guides/apm/features/service-flows/index.md
        service-map.md: user-guides/apm/features/service-map/index.md
        service-removal-api.md: developer-portal/apis/data-management/service-removal-grpc-api/index.md
        service-retention-period-grpc-api.md: developer-portal/apis/data-management/service-retention-period-grpc-api/index.md
        session-length-management.md: user-guides/account-management/account-settings/session-length/index.md
        user-guides/account-management/account-management/session-length/index.md: user-guides/account-management/account-settings/session-length/index.md
        session-replay.md: user-guides/rum/product-features/session-replay/index.md
        set-up-kubernetes-observability-using-opentelemetry-advanced-configuration.md: opentelemetry/kubernetes-observability/advanced-configuration/index.md
        severless-monitoring.md: user-guides/apm/features/serverless-monitoring/index.md
        shipping-snowflake-logs-and-audit-data-to-coralogix.md: integrations/security/shipping-snowflake-logs-and-audit-data-to-coralogix/index.md
        slack-data-ingestion.md: integrations/contextual-data/slack-data-ingestion/index.md
        slo-management-api.md: developer-portal/apis/data-management/slo-management-api/index.md
        snyk-vulnerability-monitoring-with-coralogix.md: integrations/security/snyk-vulnerability-monitoring-with-coralogix/index.md
        software-builds-display.md: user-guides/monitoring-and-insights/version-benchmarks/index.md
        source-maps-react-native.md: user-guides/rum/sdk-features/source-maps-react-native/index.md
        span-metrics.md: user-guides/apm/getting-started/span-metrics/index.md
        spinnaker-version-tags.md: developer-portal/apis/version-tags/spinnaker-version-tags/index.md
        sso-with-saml.md: user-guides/account-management/user-management/sso-with-saml/index.md
        user-guides/account-management/account-management/sso-with-saml/index.md: user-guides/account-management/user-management/sso-with-saml/index.md
        sta-capturing-network-traffic-into-cloud-storage.md: user-guides/security/security-traffic-analyzer/sta-capturing-network-traffic-into-cloud-storage/index.md
        sta-custom-enrichment.md: user-guides/security/security-traffic-analyzer/auto-generated-custom-enrichment-service/index.md
        sta-detection-encrypted-traffic.md: user-guides/security/security-traffic-analyzer/sta-detection-for-encrypted-traffic/index.md
        sta-whats-in-the-box.md: user-guides/security/security-traffic-analyzer/sta-whats-in-the-box/index.md
        statsd.md: integrations/metrics/statsd/index.md
        statuspage-logs.md: integrations/contextual-data/statuspage-data-ingestion/index.md
        surf.md: integrations/security/surf/index.md
        suricata.md: integrations/security/suricata/index.md
        synthetic-monitoring-coralogix-telegraf.md: user-guides/monitoring-and-insights/synthetic-monitoring/synthetic-monitoring-with-telegraf/index.md
        synthetic-monitoring-with-checkly.md: user-guides/monitoring-and-insights/synthetic-monitoring/synthetic-monitoring-with-checkly/index.md
        syslog-coralogix.md: integrations/syslog/syslog/index.md
        syslog-integration-walk-through-guide-coralogix.md: integrations/syslog/syslog-using-opentelemetry/index.md
        syslog-using-fluentd.md: integrations/syslog/syslog-using-opentelemetry/index.md
        syslog-using-otel.md: integrations/syslog/syslog-using-opentelemetry/index.md
        syslogng.md: integrations/syslog/syslogng/index.md
        tableau-plugin.md: user-guides/visualizations/tableau-plugin/index.md
        tail-sampling-with-coralogix-and-opentelemetry.md: opentelemetry/tail-sampling/tail-sampling-with-coralogix-and-opentelemetry/index.md
        tail-sampling-with-opentelemetry-using-docker.md: opentelemetry/tail-sampling/tail-sampling-with-opentelemetry-using-docker-compose/index.md
        tail-sampling-with-opentelemetry-using-kubernetes.md: opentelemetry/kubernetes-observability/tail-sampling-with-opentelemetry-using-kubernetes/index.md
        target-allocator-prometheuscr-with-opentelemetry.md: opentelemetry/kubernetes-observability/target-allocator-and-prometheuscr-with-opentelemetry/index.md
        tco-optimizer-api.md: developer-portal/apis/tco-controls/tco-optimizer-http-api/index.md
        developer-portal/infrastructure-as-code/tco-controls/tco-optimizer-http-api/index.md: developer-portal/apis/tco-controls/tco-optimizer-http-api/index.md
        tco-tracing-policy-grpc-api.md: developer-portal/apis/tco-controls/tco-tracing-policy-grpc-api/index.md
        developer-portal/infrastructure-as-code/tco-controls/tco-tracing-policy-grpc-api/index.md: developer-portal/apis/tco-controls/tco-tracing-policy-grpc-api/index.md
        team-management-via-cli.md: developer-portal/infrastructure-as-code/cli/team-management/index.md
        telegraf-operator.md: integrations/metrics/telegraf-operator/index.md
        telegraf.md: integrations/metrics/telegraf/index.md
        terraform-module-for-azure-queue-storage.md: integrations/azure/azure-queue-storage-terraform-module/index.md
        terraform-module-for-cloudflare-logpush.md: integrations/cdns/cloudflare/cloudflare-logpush-terraform-module/index.md
        terraform-modules-aws-s3-logs.md: external/terraform-coralogix-aws/modules/coralogix-aws-shipper/examples/s3-integration.md
        terraform-modules-for-amazon-web-services-aws.md: integrations/aws/aws-terraform-module/index.md
        terraform-modules-for-aws-cloudtrail.md: integrations/aws/aws-cloudtrail-terraform-module/index.md
        terraform-modules-for-aws-vpc-flow-logs.md: integrations/aws/aws-vpc-flow-logs-terraform-module/index.md
        terraform-modules-for-azure-blob-storage-via-event-grid.md: integrations/azure/azure-blob-storage-via-event-grid-terraform-module/index.md
        terraform-modules-for-azure-diagnostic-data.md: integrations/azure/azure-diagnostic-data-terraform-module/index.md
        terraform-modules-for-azure-eventhub.md: integrations/azure/azure-event-hub-terraform-module/index.md
        terraform-modules-for-gcp-pub-sub.md: integrations/gcp/gcp-pub/sub-terraform-module/index.md
        terraform-modules.md: external/terraform-coralogix-aws/modules/coralogix-aws-shipper/examples/cloudwatch-integration.md
        the-default-set-of-alerts-in-the-coralogix-security-traffic-analyzer-sta.md: user-guides/security/security-traffic-analyzer/sta-alerts/index.md
        threat-discovery.md: user-guides/data-transformation/enrichments/unified-threat-intelligence/index.md
        time-relative-alerts.md: user-guides/alerting/create-an-alert/logs/time-relative-alerts/index.md
        tracing-alert.md: user-guides/alerting/create-an-alert/traces/tracing-alerts/index.md
        tracing-tco-optimizer.md: user-guides/account-management/tco-optimizer/traces/index.md
        tracing.md: user-guides/monitoring-and-insights/distributed-tracing/distributed-tracing/index.md
        track-user-sessions-with-errors.md: user-guides/rum/sdk-features/error-sampling/index.md
        troubleshooting-kubernetes-observability-using-opentelemetry.md: opentelemetry/kubernetes-observability/troubleshooting/index.md
        troubleshooting.md: opentelemetry/kubernetes-observability/troubleshooting/index.md
        tutorial-install-and-configure-filebeat-to-send-your-logs-to-coralogix.md: integrations/files/beats-filebeat/index.md
        integrations/files/install-and-configure-filebeat-to-send-your-logs-to-coralogix/index.md: integrations/files/beats-filebeat/index.md
        unified-threat-feed.md: user-guides/data-transformation/enrichments/unified-threat-intelligence/index.md
        unified-threat-intelligence.md: user-guides/data-transformation/enrichments/unified-threat-intelligence/index.md
        unique-count-alert.md: user-guides/alerting/create-an-alert/logs/unique-count-alerts/index.md
        upguard.md: integrations/incoming-webhooks/upguard/index.md
        developer-portal/infrastructure-as-code/real-user-monitoring/uploading-debug-symbols-ios/index.md: user-guides/rum/cli/uploading-debug-symbols-ios/index.md
        user-sessions.md: user-guides/rum/product-features/user-sessions/index.md
        user-team-management.md: user-guides/account-management/user-management/teams/index.md
        validation-kubernetes-observability-using-opentelemetry.md: opentelemetry/kubernetes-observability/validation/index.md
        vector.md: integrations/files/vector/index.md
        version-tags-with-curl.md: developer-portal/apis/version-tags/curl-version-tags/index.md
        visualize-traces.md: user-guides/monitoring-and-insights/distributed-tracing/visualize-traces/index.md
        webhook-integration-with-victorops.md: user-guides/alerting/outbound-webhooks/alert-webhook-with-victorops/index.md
        webhooks-api.md: developer-portal/apis/data-management/webhooks-api/index.md
        what-is-coralogix-loggregation.md: user-guides/monitoring-and-insights/loggregation-making-big-data-small/index.md
        what-is-coralogix-new-error-and-critical-logs-anomaly.md: user-guides/monitoring-and-insights/anomaly-detection/new-error-and-critical-logs-anomaly/index.md
        what-is-coralogix-pattern-anomaly.md: user-guides/monitoring-and-insights/anomaly-detection/flow-anomaly/index.md
        whats-new.md: user-guides/whats-new/whats-new-in-coralogix/index.md
        windows-event-logs-opentelemetry.md: opentelemetry/configuration-options/windows-event-logs-and-opentelemetry/index.md
        workflow-based-microsoft-teams-outbound-webhooks.md: user-guides/alerting/outbound-webhooks/workflow-based-microsoft-teams-outbound-webhooks/index.md
        zabbix.md: integrations/metrics/zabbix/index.md
        zeek.md: integrations/security/zeek/index.md
        zenduty.md: user-guides/alerting/outbound-webhooks/zenduty/index.md
        zscaler-internet-access-zia.md: integrations/security/zscaler-internet-access-zia/index.md
        zscaler-secure-private-access-zpa.md: integrations/security/zscaler-secure-private-access-zpa/index.md
        docs/dataprime/language-reference/functions-reference/array/inArray.md: dataprime/language-reference/functions-reference/array/inArray.md
        developer-portal/infrastructure-as-code/coralogix-operator/index.md: external/coralogix-operator/README.md
extra:
  tags:
    monitoring: Monitoring
    alerts: Alerts
  analytics:
    provider: google
    property: G-8Z4BVKNBM0
    feedback:
      title: Was this helpful?
      ratings:
        - icon: images/icons/thumb-up.svg
          name: 'Yes'
          data: 1
          note: >-
            Thanks for your feedback!
        - icon: images/icons/thumb-down.svg
          name: 'No'
          data: 0
          note: >-
            Thanks for your feedback!
  main_site_url: https://coralogix.com/
  main_site_signup_url: https://signup.coralogix.com/
  footer_nav:
    [
      {
        title: 'Platform',
        children: [
          {
            title: 'Overview',
            url: 'https://coralogix.com/platform/'
          },
          {
            title: 'Logs',
            url: 'https://coralogix.com/platform/logs/'
          },
          {
            title: 'Metrics',
            url: 'https://coralogix.com/platform/metrics/'
          },
          {
            title: 'Tracing',
            url: 'https://coralogix.com/platform/tracing/'
          },
          {
            title: 'Security',
            url: 'https://coralogix.com/platform/security/'
          },
          {
            title: 'Integrations',
            url: 'https://coralogix.com/integrations/'
          },
        ]
      },
      {
        title: 'Solutions',
        children: [
          {
            title: 'Log Monitoring',
            url: 'https://coralogix.com/solutions/log-monitoring/'
          },
          {
            title: 'AWS Observability',
            url: 'https://coralogix.com/solutions/aws-observability/'
          },
          {
            title: 'CI/CD Acceleration',
            url: 'https://coralogix.com/solutions/ci-cd-acceleration/'
          },
          {
            title: 'Homegrown Alternative',
            url: 'https://coralogix.com/solutions/homegrown-alternative/'
          },
          {
            title: 'Cost Optimization',
            url: 'https://coralogix.com/solutions/cost-optimization/'
          },
          {
            title: 'Contextual Data Analysis',
            url: 'https://coralogix.com/solutions/contextual-data-analysis/'
          },
        ]
      },
      {
        title: 'Company',
        children: [
          {
            title: 'About',
            url: 'https://coralogix.com/about/'
          },
          {
            title: 'Customers',
            url: 'https://coralogix.com/case-studies/'
          },
          {
            title: 'Careers',
            url: 'https://coralogix.com/careers/'
          },
          {
            title: 'Pricing',
            url: 'https://coralogix.com/pricing/'
          },
          {
            title: 'Partners',
            url: 'https://coralogix.com/partners/'
          },
          {
            title: 'Upcoming Events',
            url: 'https://coralogix.com/resources/events/'
          },
          {
            title: 'Security & Compliance',
            url: 'https://coralogix.com/security-and-compliance/'
          },
        ]
      },
      {
        title: 'Resources',
        children: [
          {
            title: 'Blog',
            url: 'https://coralogix.com/blog/'
          },
          {
            title: 'Docs',
            url: 'https://coralogix.com/docs/'
          },
          {
            title: 'Webinars',
            url: 'https://coralogix.com/resources/webinars/'
          },
          {
            title: 'eBooks',
            url: 'https://coralogix.com/resources/e-books/'
          },
          {
            title: 'Videos',
            url: 'https://coralogix.com/resources/videos/'
          },
          {
            title: 'Whitepapers',
            url: 'https://coralogix.com/resources/whitepapers/'
          },
          {
            title: 'Coralogix vs. Datadog',
            url: 'https://coralogix.com/guides/datadog-apm/datadog-pricing/'
          },
        ]
      },
    ]
  domain_list:
    [
      { name: 'US1', value: 'coralogix.us' },
      { name: 'US2', value: 'cx498.coralogix.com' },
      { name: 'EU1', value: 'coralogix.com' },
      { name: 'EU2', value: 'eu2.coralogix.com' },
      { name: 'AP1', value: 'coralogix.in' },
      { name: 'AP2', value: 'coralogixsg.com' },
      { name: 'AP3', value: 'ap3.coralogix.com' },
    ]
  external_docs_meta: {
    'external/coralogix-aws-shipper/README.md':
      {
        'date': '2024-02-13',
        'title': 'Forward AWS Logs via Lambda Shipper'
      },
    'external/terraform-coralogix-aws/modules/coralogix-aws-shipper/README.md':
      {
        'date': '2024-01-02',
        'title': 'Forward AWS Logs via Lambda Shipper with Terraform'
      },
    'external/coralogix-operator/README.md':
      {
        'date': '2025-05-08',
        'title': 'Coralogix Operator'
      },
    'external/coralogix-operator/charts/coralogix-operator/README.md':
      {
        'date': '2025-05-08',
        'title': 'Coralogix Operator Helm Chart'
      },
    'external/coralogix-operator/documents/metrics.md':
      {
        'date': '2025-05-08',
        'title': 'Coralogix Operator Metrics'
      },
    'external/coralogix-operator/documents/prometheus-integration.md':
      {
        'date': '2025-05-08',
        'title': 'Prometheus Integration'
      },
    'external/coralogix-operator/documents/api.md':
      {
        'date': '2025-05-08',
        'title': 'API Reference'
      },  
    'external/coralogix-operator/tools/cxo-observer/README.md':
      {
        'date': '2025-05-08',
        'title': 'Coralogix CXO Observer'
      },
      
  }
  global_categories:
    [
      {
        title: 'Web',
        children: [
          {
            name: 'CDN browser',
            value: 'web_cdn_browser',
            redirectUri: 'user-guides/rum/sdk-installation/javascript/cdn-browser/'
          },
          {
            name: 'NPM browser',
            value: 'web_npm_browser',
            redirectUri: 'user-guides/rum/sdk-installation/javascript/npm-browser/'
          },
          {
            name: 'Next.js',
            value: 'web_next_js',
            redirectUri: 'user-guides/rum/sdk-installation/javascript/nextjs/'
          },
          {
            name: 'SvelteKit',
            value: 'web_sveltekit',
            redirectUri: 'user-guides/rum/sdk-installation/javascript/sveltekit/'
          },
          {
            name: 'Flutter',
            value: 'web_flutter',
            redirectUri: 'user-guides/rum/sdk-installation/flutter/web/'
          },
        ]
      },
      {
        title: 'Moblie',
        children: [
          {
            name: 'iOS',
            value: 'mobile_ios',
            redirectUri: 'user-guides/rum/sdk-installation/apple/ios/'
          },
          {
            name: 'tvOS',
            value: 'mobile_tvos',
            redirectUri: 'user-guides/rum/sdk-installation/apple/tvos/'
          },
          {
            name: 'Android',
            value: 'mobile_android',
            redirectUri: 'user-guides/rum/sdk-installation/android/'
          },
          {
            name: 'Flutter',
            value: 'mobile_flutter',
            redirectUri: 'user-guides/rum/sdk-installation/flutter/mobile/'
          },
          {
            name: 'React Native',
            value: 'mobile_react_native',
            redirectUri: 'user-guides/rum/sdk-installation/react-native/'
          },
        ]
      },
    ]
  endpoints: {
    domain_only: '<span class="domain-value"></span>',
    ingress_domain_only: 'ingress.<span class="domain-value"></span>',
    opentelemetry: 'ingress.<span class="domain-value"></span>:443',
    logs: {
      singles: 'https://ingress.<span class="domain-value"></span>/logs/v1/singles',
      bulk: 'https://ingress.<span class="domain-value"></span>/logs/v1/bulk',
    },
    prometheus: 'https://ingress.<span class="domain-value"></span>/prometheus/v1',
    syslog: 'syslog.<span class="domain-value"></span>:6514',
    dataprime: 'https://ng-api-http.<span class="domain-value"></span>/api/v1/dataprime/query',
    external: {
      alerts: 'https://api.<span class="domain-value"></span>/api/v2/external/alerts/',
    },
    aws: {
      firehose: 'https://ingress.<span class="domain-value"></span>/aws/firehose',
      events: 'https://ingress.<span class="domain-value"></span>/aws/event-bridge',
    },
    grpc: 'ng-api-grpc.<span class="domain-value"></span>',
    management: 'ng-api-grpc.<span class="domain-value"></span>:443',
    sql: 'https://ng-api-http.<span class="domain-value"></span>/sql',
    scim: 'https://ng-api-http.<span class="domain-value"></span>/scim',
    opensearch: 'https://api.<span class="domain-value"></span>/data/os-api',
    metrics: {
      metrics: 'https://ng-api-http.<span class="domain-value"></span>/metrics',
      cardinality: 'https://ng-api-http.<span class="domain-value"></span>/api/metrics/cardinality',
    },
    promql: 'https://prom-api.<span class="domain-value"></span>',
    enrichment: 'https://api.<span class="domain-value"></span>/api/v1/external/custom-enrichments',
    bitbucket_version_tags: 'https://ng-api-http.<span class="domain-value"></span>/api/v1/external/bitbucket/',
    azure_devops_version_tags: 'https://ng-api-http.<span class="domain-value"></span>/api/v1/external/tfs?application=YOUR_APPLICATION_NAME&subsystem=YOUR_SUBSYSTEM_NAME&name=YOUR_TAG_NAME',
    curl_version_tags: 'https://ng-api-http.<span class="domain-value"></span>/api/v1/external/tags',
    spinnaker_version_tags: 'https://ng-api-http.<span class="domain-value"></span>/api/v1/external/tags',
    agrocd_version_tags: 'https://ng-api-http.<span class="domain-value"></span>/api/v1/external/tags',
  }
  get_started_items:
    [
      {
        title: 'Create an account',
        description: 'Don''t have an account?',
        button: {
          title: 'Sign up here',
          url: 'https://signup.coralogix.com/',
        },
      },
      {
        title: 'Introduction to Coralogix',
        description: 'Get to know our observability monitoring platform and how it can best serve you',
        button: {
          title: 'Get started',
          url: 'user-guides/getting-started/get-started-with-coralogix/',
        },
      },
      {
        title: 'Integrations & extensions',
        description: 'Hit the ground running with our quick start integration & extension packages',
        button: {
          title: 'Quick-start',
          url: 'integrations/getting-started/',
        },
      },
      {
        title: 'User guides',
        description: 'Orient yourself with our core features',
        button: {
          title: 'User guides',
          url: 'user-guides/',
        },
      },
    ]
  popular_docs:
    [
      {
        title: 'OpenTelemetry',
        description: 'Pipe your OTel logs, metrics, and traces into Coralogix',
        url: 'opentelemetry/getting-started/',
        icon_url: '/images/icons/popular-docs/opentelemetry.svg',
      },
      {
        title: 'AI Center',
        description: 'Gain full visibility into all your AI apps',
        url: 'user-guides/ai-observability/ai-center/',
        icon_url: '/images/icons/popular-docs/ai-query.svg',
      },
      {
        title: 'DataPrime',
        description: 'Query, transform, and aggregate your data with Coralogix''s piped syntax language',
        url: 'dataprime/introduction/welcome-to-the-dataprime-reference/',
        icon_url: '/images/icons/popular-docs/dataprime.svg',
      },
      {
        title: 'APIs',
        description: 'Unlock our most powerful features with these APIs',
        url: 'developer-portal/apis/getting-started/getting-started-with-coralogix-apis/',
        icon_url: '/images/icons/popular-docs/apis.svg',
      },
    ]
  buttons_nav:
    [
      {
        title: 'Java',
        url: 'integrations/aws/aws-resource-metadata-collection/',
      },
      {
        title: 'Python',
        url: 'integrations/aws/aws-resource-metadata-collection/',
      },
      {
        title: 'Rust/C/C++',
        url: 'integrations/test/',
      },
      {
        title: 'PHP',
        url: 'integrations/aws/aws-resource-metadata-collection/',
      },
      {
        title: 'Node',
        url: 'integrations/aws/aws-resource-metadata-collection/',
      },
      {
        title: 'Go',
        url: 'integrations/aws/aws-resource-metadata-collection/',
      },
      {
        title: 'Ruby',
        url: 'integrations/aws/aws-resource-metadata-collection/',
      },
      {
        title: '.NET',
        url: 'integrations/aws/aws-resource-metadata-collection/',
      },
    ]

theme:
  name: material
  custom_dir: overrides
  logo: images/main-logo.svg
  features:
    - content.action.edit
    - content.code.copy
    - navigation.path
    - navigation.footer
    - content.code.annotation
    - content.code.copy
  language: en
  font: false
  palette:
    - scheme: light
      primary: custom
      accent: deep blue
      toggle:
        icon: images/icons/sun.svg
        name: Switch to dark mode
        title: light

    - scheme: dark
      primary: green
      accent: deep blue
      toggle:
        icon: images/icons/moon.svg
        name: Switch to light mode
        title: dark
markdown_extensions:
  - pymdownx.highlight:
        anchor_linenums: true
        line_spans: __span
        pygments_lang_class: true
        use_pygments: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.superfences
  - pymdownx.blocks.tab
  - pymdownx.details
  - pymdownx.emoji
  - admonition
  - attr_list
  - md_in_html
  - tables
  - customblocks:
      generators:
        custom-list-item: hooks:custom_list_item
        additional-content: hooks:additional_content
        json-schema: hooks:json_schema
        json-schema-item: hooks:json_schema_item
        json-schema-prop: hooks:json_schema_prop
hooks:
  - hooks.py
extra_css:
  - dist/bundle.css
extra_javascript:
  - path: dist/bundle.js
    async: true
